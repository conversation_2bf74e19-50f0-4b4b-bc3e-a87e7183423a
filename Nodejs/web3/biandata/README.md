# 合约量化监控站（Next.js + TypeScript + Redis + VPS）
> **版本 v0.2** – 支持自定义阈值行高亮 + 飞书 / Telegram 推送

合约量化监控站（Next.js + TypeScript + Redis + VPS）

版本 v0.3 – 新增 项目需求说明，并继续保留自定义阈值行高亮 + 飞书 / Telegram 推送

⸻

目录
	1.	总体目标
	2.	项目需求
	3.	系统架构
	4.	数据流与关键接口
	5.	后端组件说明
	6.	前端交互与样式
	7.	报警推送实现
	8.	Docker Compose 部署
	9.	运维与监控
	10.	迭代路线

⸻

总体目标
	•	实时监控 Binance 合约：资金费率 APR、OI、24-h Vol 等
	•	用户阈值：OI ≥ 指定值且 APR ≥ 指定值 ⇒
	•	前端整行高亮（红底 / 深红）
	•	同时推送飞书 / Telegram 消息
	•	SSR（Next.js App Router）保证 SEO；Tailwind CSS 表格美观
	•	单台 VPS 可跑全部服务，月费 ≈ 5 €

⸻

项目需求

1 功能性需求

编号	需求	说明
F-1	数据采集	必须秒级获取 Binance Funding、MiniTicker、OI、24h 数据；支持 Top 100 币扩展；抓取错误需自动重连。
F-2	指标计算	支持 APR、OI/Cap、Vol/Cap 公式；未来可扩展新增指标（多空比、清算量等）。
F-3	阈值配置	提供全局默认阈值；注册用户可保存个人阈值，前端输入实时生效。
F-4	高亮展示	当行数据满足阈值条件，整行自动变色；支持暗黑模式。
F-5	消息推送	命中阈值触发飞书 &/or Telegram 消息；同一币 15 min 内仅推一次（去抖）。
F-6	多平台登录（可选）	邮箱 / Feishu OAuth；未登录只能用默认阈值。
F-7	Web API	/api/metrics 返回最新列表；/api/threshold 读写阈值；均需 100 ms 内响应。
F-8	权限控制	后台 Admin 可增删币种白名单、修改全局阈值。

2 非功能性需求

分类	目标	说明
性能	首页 SSR TTFB < 200 ms；前端表格 500 行渲染 < 16 ms	使用 Redis 内存缓存，Next.js RSC 流式渲染。
可用性	年可用性 ≥ 99.5 %	Worker 与 RuleEngine 崩溃后 30 s 内自动重启；推送失败重试 3 次。
扩展性	模块化	数据采集、计算、推送解耦；未来可插 OKX / Bybit。
安全	API 需鉴权；仅开放 80/443；Redis 不暴露公网	使用 JWT + HTTPS；UFW 限制端口。
SEO	静态 HTML + OG 标签	Next.js SSR + 动态 meta。
部署	单 Docker Compose 启动；CI < 5 min	GitHub Actions CI：推送即自动构建镜像并重启。


⸻

系统架构

┌─────────┐ 1 s WS  ┌────────┐   Redis HSet   ┌────────────┐
│ Binance │────────▶│ Worker │──────────────▶│   缓存层    │
└─────────┘         └────────┘                └────────────┘
                    ▲     ▲                        ▲
REST 60 s ──────────┘     │                        │
                          │ 轮询 5 s               │
                    ┌────────────┐ 命中事件  ┌──────────────┐
                    │ RuleEngine │──────────▶│ Push Service │─▶ Feishu/TG
                    └────────────┘            └──────────────┘
                          ▲  HTTP /SWR
                    ┌─────────────┐  SSR  ┌─────────┐
                    │  Next.js Web │◀─────│ Browser │
                    └─────────────┘       └─────────┘

---

## 目录
1. [总体目标](#总体目标)
2. [系统架构](#系统架构)
3. [数据流与关键接口](#数据流与关键接口)
4. [后端组件说明](#后端组件说明)
5. [前端交互与样式](#前端交互与样式)
6. [报警推送实现](#报警推送实现)
7. [Docker Compose 部署](#docker-compose-部署)
8. [运维与监控](#运维与监控)
9. [迭代路线](#迭代路线)

---

## 总体目标
- **实时**监控 Binance 合约：资金费率 APR、OI、24-h Vol 等
- **用户阈值**：OI ≥ 指定值且 APR ≥ 指定值 ⇒
  - 前端行整行高亮（红底 / 深红）
  - 同时推送飞书/Telegram 消息
- **SSR**（Next.js App Router）保证 SEO；Tailwind CSS 表格美观
- **单台 VPS** 可跑全部服务，月费 ≈ 5 €

---

## 系统架构
```text
┌─────────┐ 1 s WS  ┌────────┐   Redis HSet   ┌────────────┐
│ Binance │────────▶│ Worker │──────────────▶│   缓存层    │
└─────────┘         └────────┘                └────────────┘
                    ▲     ▲                        ▲
REST 60 s ──────────┘     │                        │
                          │ 轮询 5 s               │
                    ┌────────────┐ 命中事件  ┌──────────────┐
                    │ RuleEngine │──────────▶│ Push Service │─▶ Feishu/TG
                    └────────────┘            └──────────────┘
                          ▲  HTTP /SWR
                    ┌─────────────┐  SSR  ┌─────────┐
                    │  Next.js Web │◀─────│ Browser │
                    └─────────────┘       └─────────┘


⸻

数据流与关键接口

指标	数据源	更新频率	说明
Funding Rate	WS !markPrice@arr	1 s	字段 r；顺带拿 nextFundingTime
APR	fundingRate × freq × 365	1 s	1 h 档 freq=24；8 h 档 freq=3
MiniTicker	WS !miniTicker@arr	1 s	价格 & 24 h 成交额/量
OI	REST /openInterest?symbol= (批并发)	45 s	Top N 币异步抓；无法全量 WS
市值	CoinGecko /coins/markets	300 s	vs_currency=usd，一次 250 币

所有原始数据写入 Redis：
funding:{symbol}、ticker24:{symbol}、oi:{symbol}。

⸻

后端组件说明

1. 数据采集 Worker (apps/worker)

// src/index.ts
ws('wss://fstream.binance.com/ws/!markPrice@arr', updateFunding);
ws('wss://fstream.binance.com/ws/!miniTicker@arr', update24h);

setInterval(fetchOI, 45_000);            // axios 并发 OI
setInterval(fetchMarketCap, 300_000);    // gecko 市值

2. 规则引擎 (apps/worker/ruleEngine.ts)

const loop = async () => {
  const [oiT, aprT] = await redis.hmget('alert:global','oi','apr');
  const rows = JSON.parse(await redis.get('snapshot') || '[]');
  rows.filter(r => r.oi>=+oiT && r.apr>=+aprT)
      .forEach(r => push(`[⚠] ${r.symbol}\nOI ${fmt(r.oi)}\nAPR ${r.apr}%`));
};
setInterval(loop, 5_000);

3. 推送服务
	•	飞书：Webhook /bot/v2/hook/xxx
	•	Telegram：https://api.telegram.org/bot<TOKEN>/sendMessage

⸻

前端交互与样式

// app/page.tsx (Next.js 14 / RSC)
export default async function Home() {
  const rows = await fetchMetrics();       // 直接 SSR
  return (
    <Table>
      {rows.map(r =>
        <tr key={r.symbol}
            className={clsx(
              r.oi>=th.oi && r.apr>=th.apr && 'bg-red-200 dark:bg-red-900'
            )}>
          <td>{r.symbol}</td>
          <td>{fmt(r.oi)}</td>
          <td className="text-green-600">{r.apr.toFixed(1)}%</td>
          ...
        </tr>
      )}
    </Table>
  );
}

	•	阈值表单：两个 <input type="number" /> ↔ /api/alert/threshold
	•	客户端 SWR：1 s 拉 /api/metrics 实时刷新，不用 socket

⸻

报警推送实现

// push/index.ts
export async function push(text: string) {
  if (process.env.FEISHU_WEBHOOK) await pushFeishu(text);
  if (process.env.TG_TOKEN)       await pushTG(text);
}

	•	去抖：Redis setnx alert:last:${symbol}，过期 900 s。

⸻

Docker Compose 部署

version: '3.9'
services:
  redis:
    image: redis:7-alpine
    volumes: [redis:/data]
    restart: always

  worker:
    build: ./apps/worker
    command: pnpm ts-node src/index.ts
    env_file: .env
    depends_on: [redis]
    restart: always

  rule:
    build: ./apps/worker
    command: pnpm ts-node src/ruleEngine.ts
    env_file: .env
    depends_on: [redis]
    restart: always

  web:
    build: ./apps/web
    command: pnpm next start -p 3000
    env_file: .env
    depends_on: [redis]
    restart: always

  caddy:
    image: caddy:alpine
    volumes:
      - ./Caddyfile:/etc/caddy/Caddyfile
      - caddy_data:/data
    ports: ["80:80","443:443"]
    restart: always
volumes:
  redis:
  caddy_data:

Caddyfile

quant.example.com {
  reverse_proxy web:3000
}


⸻

运维与监控

工具	作用
UFW	仅放行 22 80 443
Watchtower	自动更新容器
Grafana + Loki	日志 / 监控


⸻

迭代路线

阶段	目标
M0	Worker + Redis + Web SSR 成功跑通
M1	自定义阈值 + 行高亮 + 飞书推送
M2	Telegram, 静默期去抖
M3	用户登录、个人化 Watchlist
M4	多交易所（OKX / Bybit）
M5	自动下单（量化 Bot）


⸻

以上即完整 Markdown 方案文档，复制到 README.md 即可直接用。
有任何环节要代码样例或部署脚本，随时吩咐～熊哥，一路暴赚！🚀

