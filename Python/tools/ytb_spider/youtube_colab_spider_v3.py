#!/usr/bin/env python3
"""
YouTube频道持续蜘蛛式爬虫 - Google Colab专用版 v3.0
专门为Google Colab环境优化的版本

使用方法:
1. 上传webshare_proxy.txt到Colab
2. 运行此脚本
3. 自动开始持续爬取并显示Twitter链接

作者: GreenJoson
创建时间: 2025-06-26
版本: v3.0 - Google Colab专用版
"""

import requests
import json
import time
import random
import re
import os
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging

# 配置日志 - Colab友好
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ColabProxyPool:
    """Google Colab专用代理池"""
    
    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = self._load_proxies()
        self.failed_proxies = set()
        self.current_index = 0
        self.consecutive_failures = 0
        
        print(f"✅ 代理池初始化完成，共 {len(self.proxies)} 个代理")
    
    def _load_proxies(self) -> List[Dict]:
        """从文件加载代理列表"""
        proxies = []
        
        if not os.path.exists(self.proxy_file):
            print(f"❌ 代理文件不存在: {self.proxy_file}")
            print("💡 请确保已上传 webshare_proxy.txt 文件到Colab")
            return []
        
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            # 解析格式: p.webshare.io:80:username:password
                            parts = line.split(':')
                            if len(parts) >= 4:
                                host, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{host}:{port}"
                                proxies.append({
                                    'http': proxy_url,
                                    'https': proxy_url,
                                    'id': line_num,
                                    'username': username,
                                    'host': host
                                })
                        except Exception as e:
                            print(f"⚠️ 解析代理失败 (行{line_num}): {e}")
                            
        except Exception as e:
            print(f"❌ 加载代理文件失败: {e}")
            
        return proxies
    
    def get_proxy(self) -> Optional[Dict]:
        """获取可用代理"""
        if not self.proxies:
            return None
            
        available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]
        
        if not available_proxies:
            print("🔄 重置失败代理列表")
            self.failed_proxies.clear()
            self.consecutive_failures = 0
            available_proxies = self.proxies
        
        if available_proxies:
            # 连续失败时跳过一些代理
            if self.consecutive_failures >= 3:
                self.current_index += random.randint(5, 15)
                self.consecutive_failures = 0
                print(f"🔀 跳转到代理 {self.current_index}")
            
            proxy = available_proxies[self.current_index % len(available_proxies)]
            self.current_index += 1
            return proxy
        
        return None
    
    def mark_proxy_failed(self, proxy_id: int):
        """标记代理失败"""
        self.failed_proxies.add(proxy_id)
        self.consecutive_failures += 1
    
    def mark_proxy_success(self, proxy_id: int):
        """标记代理成功"""
        self.consecutive_failures = 0
    
    def get_stats(self) -> Dict:
        """获取代理统计"""
        total_proxies = len(self.proxies)
        failed_proxies = len(self.failed_proxies)
        active_proxies = total_proxies - failed_proxies
        
        return {
            'total_proxies': total_proxies,
            'active_proxies': active_proxies,
            'failed_proxies': failed_proxies,
            'success_rate': f"{(active_proxies/total_proxies)*100:.1f}%" if total_proxies > 0 else "0%"
        }

class TwitterExtractor:
    """Twitter链接提取器"""
    
    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]
    
    def extract_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()
        
        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 标准化链接
                if not match.startswith('http'):
                    match = f"https://{match}"
                
                # 清理链接
                clean_link = match.split('?')[0].split('#')[0]
                
                # 转换x.com为twitter.com
                clean_link = clean_link.replace('x.com', 'twitter.com')
                
                twitter_links.add(clean_link)
        
        return sorted(list(twitter_links))

class ColabSpider:
    """Google Colab专用蜘蛛爬虫"""
    
    def __init__(self):
        self.proxy_pool = ColabProxyPool()
        self.twitter_extractor = TwitterExtractor()
        self.session = requests.Session()
        
        # 爬取状态
        self.discovered_channels = set()
        self.crawled_channels = set()
        self.crawl_queue = []
        self.twitter_discoveries = []
        self.total_crawled = 0
        self.is_running = True
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Connection': 'keep-alive',
        })
        
        print("🕷️ Colab蜘蛛爬虫初始化完成")
    
    def add_seed_channels(self, seed_channels: List[str]):
        """添加种子频道"""
        for channel in seed_channels:
            if channel not in self.discovered_channels:
                self.crawl_queue.append(channel)
                self.discovered_channels.add(channel)
        
        print(f"🌱 添加了 {len(seed_channels)} 个种子频道")
    
    def crawl_channel_for_twitter(self, channel_url: str) -> Dict:
        """爬取频道寻找Twitter链接"""
        result = {
            'channel_url': channel_url,
            'twitter_links': [],
            'success': False,
            'error': None,
            'proxy_used': None
        }
        
        try:
            proxy = self.proxy_pool.get_proxy()
            if not proxy:
                result['error'] = "无可用代理"
                return result
                
            result['proxy_used'] = f"{proxy['username']}@{proxy['host']}"
            
            # 访问频道的about页面
            about_url = f"{channel_url.rstrip('/')}/about"
            
            response = self.session.get(
                about_url,
                proxies=proxy,
                timeout=25,
                allow_redirects=True
            )
            
            if response.status_code == 200:
                self.proxy_pool.mark_proxy_success(proxy['id'])
                
                # 提取Twitter链接
                twitter_links = self.twitter_extractor.extract_from_html(response.text)
                result['twitter_links'] = twitter_links
                result['success'] = True
                
                if twitter_links:
                    print(f"✅ 发现Twitter: {channel_url}")
                    for link in twitter_links:
                        print(f"   🔗 {link}")
                    
                    self.twitter_discoveries.append({
                        'channel_url': channel_url,
                        'twitter_links': twitter_links,
                        'discovered_at': datetime.now().isoformat(),
                        'proxy_used': result['proxy_used']
                    })
                    
            else:
                self.proxy_pool.mark_proxy_failed(proxy['id'])
                result['error'] = f"HTTP {response.status_code}"
                
        except Exception as e:
            if proxy:
                self.proxy_pool.mark_proxy_failed(proxy['id'])
            result['error'] = str(e)
        
        return result
    
    def discover_channels_from_url(self, channel_url: str, max_discover: int = 6) -> List[str]:
        """从频道URL发现推荐频道"""
        try:
            base_url = channel_url.rstrip('/')
            if '/videos' in base_url or '/about' in base_url:
                base_url = base_url.split('/videos')[0].split('/about')[0]
            
            discovered = set()
            
            # 从channels页面发现
            channels_page = f"{base_url}/channels"
            channels_from_page = self._get_channels_from_url(channels_page)
            discovered.update(channels_from_page)
            
            # 从主页面发现
            if len(discovered) < max_discover:
                channels_from_main = self._get_channels_from_url(base_url)
                discovered.update(channels_from_main)
            
            # 过滤新频道
            new_channels = []
            for url in discovered:
                if (len(new_channels) >= max_discover):
                    break
                if (url not in self.discovered_channels and 
                    url != channel_url and 
                    self._is_valid_channel_url(url)):
                    new_channels.append(url)
                    self.discovered_channels.add(url)
            
            return new_channels
            
        except Exception as e:
            print(f"⚠️ 发现频道失败: {e}")
            return []
