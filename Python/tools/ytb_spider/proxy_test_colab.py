#!/usr/bin/env python3
"""
代理测试脚本 - Google Colab专用版本
测试Webshare代理的连接状态和账户状态

作者: GreenJoson
版本: v1.0
创建时间: 2025-06-27
"""

import requests
import time
import random
from typing import List, Dict, Tuple
import json

class ProxyTester:
    """代理测试器"""

    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = []
        self.load_proxies()

    def load_proxies(self):
        """加载代理列表"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            print(f"📁 代理文件: {self.proxy_file}")
            print(f"📊 文件包含: {len(lines)} 行")

            valid_count = 0
            for line in lines:
                line = line.strip()
                if line and ':' in line:
                    parts = line.split(':')
                    if len(parts) == 4:
                        host, port, username, password = parts
                        proxy_info = {
                            'host': host,
                            'port': int(port),
                            'username': username,
                            'password': password,
                            'proxy_dict': {
                                'http': f'http://{username}:{password}@{host}:{port}',
                                'https': f'http://{username}:{password}@{host}:{port}'
                            }
                        }
                        self.proxies.append(proxy_info)
                        valid_count += 1

            print(f"✅ 有效代理: {valid_count} 个")

        except FileNotFoundError:
            print(f"❌ 代理文件不存在: {self.proxy_file}")
            print("💡 请确保代理文件已上传到Colab")
        except Exception as e:
            print(f"❌ 加载代理失败: {e}")

    def test_single_proxy(self, proxy_info: Dict, test_url: str = "http://httpbin.org/ip", timeout: int = 15) -> Dict:
        """测试单个代理"""
        result = {
            'proxy': f"{proxy_info['host']}:{proxy_info['port']}",
            'status': 'unknown',
            'response_time': None,
            'ip': None,
            'error': None
        }

        try:
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxy_info['proxy_dict'],
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            response_time = round(time.time() - start_time, 2)

            if response.status_code == 200:
                try:
                    data = response.json()
                    result.update({
                        'status': 'success',
                        'response_time': response_time,
                        'ip': data.get('origin', 'unknown')
                    })
                except:
                    result.update({
                        'status': 'success',
                        'response_time': response_time,
                        'ip': 'unknown'
                    })
            else:
                result.update({
                    'status': 'http_error',
                    'error': f'HTTP {response.status_code}'
                })

        except requests.exceptions.ProxyError as e:
            error_str = str(e)
            if '402' in error_str:
                result.update({
                    'status': 'payment_required',
                    'error': '402 Payment Required - 账户余额不足或订阅过期'
                })
            elif '407' in error_str:
                result.update({
                    'status': 'auth_required',
                    'error': '407 Proxy Authentication Required - 认证失败'
                })
            else:
                result.update({
                    'status': 'proxy_error',
                    'error': f'代理错误: {error_str[:100]}'
                })
        except requests.exceptions.ConnectTimeout:
            result.update({
                'status': 'timeout',
                'error': '连接超时'
            })
        except requests.exceptions.ConnectionError as e:
            result.update({
                'status': 'connection_error',
                'error': f'连接错误: {str(e)[:100]}'
            })
        except Exception as e:
            result.update({
                'status': 'unknown_error',
                'error': f'未知错误: {str(e)[:100]}'
            })

        return result

    def test_multiple_proxies(self, count: int = 10, show_details: bool = True) -> Dict:
        """测试多个代理"""
        if not self.proxies:
            print("❌ 没有可用的代理进行测试")
            return {}

        test_count = min(count, len(self.proxies))
        print(f"\n🧪 开始测试 {test_count} 个代理...")
        print("=" * 60)

        # 随机选择代理进行测试
        test_proxies = random.sample(self.proxies, test_count)

        results = {
            'success': 0,
            'payment_required': 0,
            'auth_required': 0,
            'timeout': 0,
            'connection_error': 0,
            'proxy_error': 0,
            'http_error': 0,
            'unknown_error': 0,
            'details': []
        }

        for i, proxy_info in enumerate(test_proxies, 1):
            print(f"🔍 测试 {i}/{test_count}: {proxy_info['host']}:{proxy_info['port']}")

            result = self.test_single_proxy(proxy_info)
            results['details'].append(result)
            results[result['status']] += 1

            # 显示结果
            if result['status'] == 'success':
                print(f"   ✅ 成功 - IP: {result['ip']} - 响应时间: {result['response_time']}s")
            elif result['status'] == 'payment_required':
                print(f"   💳 {result['error']}")
            elif result['status'] == 'auth_required':
                print(f"   🔐 {result['error']}")
            elif result['status'] == 'timeout':
                print(f"   ⏰ {result['error']}")
            else:
                print(f"   ❌ {result['error']}")

            # 避免请求过于频繁
            if i < test_count:
                time.sleep(1)

        return results

    def test_webshare_status(self):
        """测试Webshare服务状态"""
        print("\n🌐 测试Webshare服务状态...")

        # 测试Webshare主域名
        try:
            response = requests.get("https://proxy.webshare.io", timeout=10)
            if response.status_code == 200:
                print("   ✅ Webshare官网可访问")
            else:
                print(f"   ⚠️ Webshare官网返回: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ Webshare官网无法访问: {e}")

        # 测试代理服务器域名
        try:
            response = requests.get("http://p.webshare.io", timeout=10)
            print(f"   ℹ️ 代理服务器响应: HTTP {response.status_code}")
        except Exception as e:
            print(f"   ❌ 代理服务器无法访问: {e}")

    def generate_report(self, results: Dict):
        """生成测试报告"""
        if not results:
            return

        total = sum(results[key] for key in results if key != 'details')

        print("\n" + "=" * 60)
        print("📊 代理测试报告")
        print("=" * 60)

        print(f"📈 测试统计:")
        print(f"   总测试数: {total}")
        print(f"   ✅ 成功: {results['success']} ({results['success']/total*100:.1f}%)")
        print(f"   💳 余额不足: {results['payment_required']} ({results['payment_required']/total*100:.1f}%)")
        print(f"   🔐 认证失败: {results['auth_required']} ({results['auth_required']/total*100:.1f}%)")
        print(f"   ⏰ 超时: {results['timeout']} ({results['timeout']/total*100:.1f}%)")
        print(f"   🔌 连接错误: {results['connection_error']} ({results['connection_error']/total*100:.1f}%)")
        print(f"   ❌ 其他错误: {results['proxy_error'] + results['http_error'] + results['unknown_error']} ({(results['proxy_error'] + results['http_error'] + results['unknown_error'])/total*100:.1f}%)")

        print(f"\n💡 问题诊断:")
        if results['payment_required'] > 0:
            print("   🚨 主要问题: 账户余额不足或订阅过期")
            print("   🔗 解决方案: https://proxy.webshare.io/dashboard")
            print("   💳 需要充值账户或续费订阅")
        elif results['auth_required'] > 0:
            print("   🚨 主要问题: 代理认证失败")
            print("   🔧 检查用户名和密码是否正确")
        elif results['success'] == 0:
            print("   🚨 主要问题: 所有代理都无法使用")
            print("   🔧 建议暂时使用直连模式")
        elif results['success'] > 0:
            print(f"   ✅ 有 {results['success']} 个代理可正常使用")
            print("   🔧 可以继续使用代理模式")

        print(f"\n🛠️ 建议操作:")
        if results['payment_required'] > total * 0.5:
            print("   1. 🔍 立即检查Webshare账户状态")
            print("   2. 💳 充值账户余额")
            print("   3. 📅 续费代理订阅")
            print("   4. 🔄 更新代理列表")
        else:
            print("   1. 🔄 重新运行爬虫")
            print("   2. 📊 监控代理使用情况")
            print("   3. 🔧 如问题持续，考虑直连模式")

def main():
    """主函数"""
    print("🚀 Webshare代理测试工具 - Google Colab版")
    print("=" * 60)

    # 创建测试器
    tester = ProxyTester()

    if not tester.proxies:
        print("\n❌ 没有找到有效的代理配置")
        print("💡 请确保 webshare_proxy.txt 文件已正确上传")
        return

    # 测试Webshare服务状态
    tester.test_webshare_status()

    # 测试代理
    results = tester.test_multiple_proxies(count=10)

    # 生成报告
    tester.generate_report(results)

    print(f"\n🎯 测试完成！")

if __name__ == "__main__":
    main()