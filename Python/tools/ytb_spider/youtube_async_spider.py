#!/usr/bin/env python3
"""
YouTube Twitter爬虫 - 异步高速版本
使用异步并发大幅提升爬取速度，支持SQLite自动保存

功能特点:
🚀 异步并发爬取 - 同时处理多个频道，速度提升5-10倍
💾 SQLite自动保存 - 实时保存所有Twitter发现结果
🌐 智能代理池 - 支持Webshare代理，自动故障恢复
🕷️ 蜘蛛式发现 - 自动发现相关频道
📊 实时统计 - 详细的爬取进度和结果统计

作者: GreenJoson
版本: Async v1.0 - 异步高速版本
创建时间: 2025-06-26
"""

import asyncio
import aiohttp
import sqlite3
import json
import time
import random
import re
import os
from typing import List, Dict, Optional, Set
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AsyncDatabaseManager:
    """异步SQLite数据库管理器"""
    
    def __init__(self, db_file: str = "youtube_twitter_async.db"):
        self.db_file = db_file
        self.executor = ThreadPoolExecutor(max_workers=2)
        self.init_database()
        print(f"💾 异步数据库初始化完成: {db_file}")
    
    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()
            
            # Twitter发现表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS twitter_discoveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_url TEXT NOT NULL,
                    twitter_link TEXT NOT NULL,
                    discovered_at TEXT NOT NULL,
                    proxy_used TEXT,
                    session_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_url, twitter_link)
                )
            ''')
            
            # 爬取会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_sessions (
                    session_id TEXT PRIMARY KEY,
                    started_at TEXT NOT NULL,
                    ended_at TEXT,
                    total_crawled INTEGER DEFAULT 0,
                    twitter_found INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'running'
                )
            ''')
            
            conn.commit()
    
    async def save_twitter_discovery(self, channel_url: str, twitter_links: List[str], 
                                   proxy_used: str, session_id: str):
        """异步保存Twitter发现结果"""
        def _save():
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    discovered_at = datetime.now().isoformat()
                    
                    for twitter_link in twitter_links:
                        cursor.execute('''
                            INSERT OR IGNORE INTO twitter_discoveries 
                            (channel_url, twitter_link, discovered_at, proxy_used, session_id)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (channel_url, twitter_link, discovered_at, proxy_used, session_id))
                    
                    conn.commit()
                    return len(twitter_links)
            except Exception as e:
                logger.error(f"保存Twitter发现失败: {e}")
                return 0
        
        loop = asyncio.get_event_loop()
        saved_count = await loop.run_in_executor(self.executor, _save)
        if saved_count > 0:
            print(f"💾 已保存: {channel_url} -> {saved_count} 个Twitter链接")
    
    async def get_stats(self) -> Dict:
        """异步获取统计信息"""
        def _get_stats():
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    
                    cursor.execute('SELECT COUNT(*) FROM twitter_discoveries')
                    total_twitter = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(DISTINCT twitter_link) FROM twitter_discoveries')
                    unique_twitter = cursor.fetchone()[0]
                    
                    cursor.execute('SELECT COUNT(DISTINCT channel_url) FROM twitter_discoveries')
                    channels_with_twitter = cursor.fetchone()[0]
                    
                    return {
                        'total_discoveries': total_twitter,
                        'unique_links': unique_twitter,
                        'channels_with_twitter': channels_with_twitter
                    }
            except Exception as e:
                logger.error(f"获取统计失败: {e}")
                return {}
        
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, _get_stats)

class AsyncProxyManager:
    """异步代理管理器"""
    
    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = self._load_proxies()
        self.failed_proxies = set()
        self.current_index = 0
        self.consecutive_failures = 0
        self.lock = asyncio.Lock()
        
        if self.proxies:
            print(f"✅ 异步代理池加载成功: {len(self.proxies)} 个代理")
        else:
            print("⚠️ 未加载到代理，将使用直连模式")
    
    def _load_proxies(self) -> List[Dict]:
        """加载代理列表"""
        proxies = []
        
        if not os.path.exists(self.proxy_file):
            return []
        
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            parts = line.split(':')
                            if len(parts) >= 4:
                                host, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{host}:{port}"
                                proxies.append({
                                    'proxy': proxy_url,
                                    'id': line_num,
                                    'username': username,
                                    'host': host
                                })
                        except Exception:
                            continue
        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")
            
        return proxies
    
    async def get_proxy(self) -> Optional[str]:
        """异步获取可用代理"""
        async with self.lock:
            if not self.proxies:
                return None
                
            available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]
            
            if not available_proxies:
                self.failed_proxies.clear()
                self.consecutive_failures = 0
                available_proxies = self.proxies
            
            if available_proxies:
                if self.consecutive_failures >= 3:
                    self.current_index += random.randint(5, 15)
                    self.consecutive_failures = 0
                
                proxy = available_proxies[self.current_index % len(available_proxies)]
                self.current_index += 1
                return proxy['proxy']
            
            return None
    
    async def mark_failed(self, proxy_id: int):
        """标记代理失败"""
        async with self.lock:
            if proxy_id:
                self.failed_proxies.add(proxy_id)
                self.consecutive_failures += 1
    
    async def mark_success(self):
        """标记代理成功"""
        async with self.lock:
            self.consecutive_failures = 0

class TwitterExtractor:
    """Twitter链接提取器"""
    
    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]
    
    def extract(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()
        
        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                if not match.startswith('http'):
                    match = f"https://{match}"
                
                clean_link = match.split('?')[0].split('#')[0]
                clean_link = clean_link.replace('x.com', 'twitter.com')
                twitter_links.add(clean_link)
        
        return sorted(list(twitter_links))

class AsyncYouTubeSpider:
    """异步YouTube蜘蛛爬虫"""
    
    def __init__(self, max_concurrent: int = 10):
        self.proxy_manager = AsyncProxyManager()
        self.twitter_extractor = TwitterExtractor()
        self.db = AsyncDatabaseManager()
        self.max_concurrent = max_concurrent
        
        # 爬取状态
        self.discovered_channels = set()
        self.crawled_channels = set()
        self.crawl_queue = asyncio.Queue()
        self.twitter_discoveries = []
        self.total_crawled = 0
        self.is_running = True
        
        # 会话ID
        self.session_id = f"async_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 统计锁
        self.stats_lock = asyncio.Lock()
        
        print(f"🚀 异步YouTube蜘蛛爬虫初始化完成 (并发数: {max_concurrent})")
    
    async def add_seeds(self, channels: List[str]):
        """添加种子频道"""
        for channel in channels:
            if channel not in self.discovered_channels:
                await self.crawl_queue.put(channel)
                self.discovered_channels.add(channel)
        
        print(f"🌱 添加种子频道: {len(channels)} 个")
    
    async def crawl_channel(self, session: aiohttp.ClientSession, channel_url: str) -> Dict:
        """异步爬取单个频道"""
        result = {
            'channel_url': channel_url,
            'twitter_links': [],
            'success': False,
            'error': None,
            'proxy_used': '直连'
        }
        
        try:
            proxy = await self.proxy_manager.get_proxy()
            about_url = f"{channel_url.rstrip('/')}/about"
            
            timeout = aiohttp.ClientTimeout(total=20)
            
            async with session.get(
                about_url,
                proxy=proxy,
                timeout=timeout,
                allow_redirects=True
            ) as response:
                
                if response.status == 200:
                    await self.proxy_manager.mark_success()
                    
                    html_content = await response.text()
                    twitter_links = self.twitter_extractor.extract(html_content)
                    result['twitter_links'] = twitter_links
                    result['success'] = True
                    result['proxy_used'] = proxy or '直连'
                    
                    if twitter_links:
                        print(f"✅ 发现Twitter: {channel_url}")
                        for link in twitter_links:
                            print(f"   🔗 {link}")
                        
                        # 异步保存到数据库
                        await self.db.save_twitter_discovery(
                            channel_url, twitter_links, result['proxy_used'], self.session_id
                        )
                        
                        async with self.stats_lock:
                            self.twitter_discoveries.append({
                                'channel_url': channel_url,
                                'twitter_links': twitter_links,
                                'discovered_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'proxy_used': result['proxy_used']
                            })
                    else:
                        print(f"⚪ 无Twitter: {channel_url}")
                        
                else:
                    if proxy:
                        # 从代理URL中提取ID进行标记
                        proxy_id = hash(proxy) % 100000
                        await self.proxy_manager.mark_failed(proxy_id)
                    result['error'] = f"HTTP {response.status}"
                    print(f"❌ 访问失败: {channel_url} ({response.status})")
                    
        except Exception as e:
            if proxy:
                proxy_id = hash(proxy) % 100000
                await self.proxy_manager.mark_failed(proxy_id)
            result['error'] = str(e)
            print(f"❌ 爬取错误: {channel_url} - {e}")
        
        return result
