#!/usr/bin/env python3
"""
YouTube Twitter爬虫 - 最终统一版本
集成所有功能的终极版本，支持Google Colab和本地运行

功能特点:
🕷️ 蜘蛛式频道发现 - 自动发现相关频道
🔄 持续爬取模式 - 不会停止，一直爬取
🌐 智能IP切换 - 自动切换代理，错误恢复
🐦 实时Twitter展示 - 立即显示发现的Twitter链接
📊 详细统计信息 - 实时监控爬取状态

作者: GreenJoson
版本: Final v1.0 - 最终统一版本
创建时间: 2025-06-26
"""

import requests
import json
import time
import random
import re
import os
import sqlite3
from typing import List, Dict, Optional
from datetime import datetime
import logging
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseManager:
    """SQLite数据库管理器 - 线程安全的自动保存Twitter发现结果"""

    def __init__(self, db_file: str = "youtube_twitter_discoveries.db"):
        self.db_file = db_file
        self.lock = threading.Lock()  # 线程锁
        self.init_database()
        print(f"💾 线程安全数据库初始化完成: {db_file}")

    def init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_file) as conn:
            cursor = conn.cursor()

            # 创建Twitter发现表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS twitter_discoveries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_url TEXT NOT NULL,
                    twitter_link TEXT NOT NULL,
                    discovered_at TEXT NOT NULL,
                    proxy_used TEXT,
                    session_id TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_url, twitter_link)
                )
            ''')

            # 创建爬取会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS crawl_sessions (
                    session_id TEXT PRIMARY KEY,
                    started_at TEXT NOT NULL,
                    ended_at TEXT,
                    total_crawled INTEGER DEFAULT 0,
                    total_discovered INTEGER DEFAULT 0,
                    twitter_found INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'running'
                )
            ''')

            # 创建频道信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_url TEXT UNIQUE NOT NULL,
                    has_twitter BOOLEAN DEFAULT FALSE,
                    last_crawled TEXT,
                    crawl_count INTEGER DEFAULT 0
                )
            ''')

            conn.commit()

    def save_twitter_discovery(self, channel_url: str, twitter_links: List[str],
                             proxy_used: str, session_id: str):
        """线程安全保存Twitter发现结果"""
        with self.lock:  # 线程锁保护
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    discovered_at = datetime.now().isoformat()

                    for twitter_link in twitter_links:
                        cursor.execute('''
                            INSERT OR IGNORE INTO twitter_discoveries
                            (channel_url, twitter_link, discovered_at, proxy_used, session_id)
                            VALUES (?, ?, ?, ?, ?)
                        ''', (channel_url, twitter_link, discovered_at, proxy_used, session_id))

                    # 更新频道信息
                    cursor.execute('''
                        INSERT OR REPLACE INTO channels
                        (channel_url, has_twitter, last_crawled, crawl_count)
                        VALUES (?, ?, ?, COALESCE((SELECT crawl_count FROM channels WHERE channel_url = ?) + 1, 1))
                    ''', (channel_url, True, discovered_at, channel_url))

                    conn.commit()
                    print(f"💾 已保存: {channel_url} -> {len(twitter_links)} 个Twitter链接")

            except Exception as e:
                logger.error(f"保存Twitter发现失败: {e}")

    def save_channel_crawled(self, channel_url: str, has_twitter: bool, session_id: str):
        """保存频道爬取记录"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                crawled_at = datetime.now().isoformat()

                cursor.execute('''
                    INSERT OR REPLACE INTO channels
                    (channel_url, has_twitter, last_crawled, crawl_count)
                    VALUES (?, ?, ?, COALESCE((SELECT crawl_count FROM channels WHERE channel_url = ?) + 1, 1))
                ''', (channel_url, has_twitter, crawled_at, channel_url))

                conn.commit()

        except Exception as e:
            logger.error(f"保存频道记录失败: {e}")

    def start_session(self, session_id: str):
        """开始新的爬取会话"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                started_at = datetime.now().isoformat()

                cursor.execute('''
                    INSERT OR REPLACE INTO crawl_sessions
                    (session_id, started_at, status)
                    VALUES (?, ?, 'running')
                ''', (session_id, started_at))

                conn.commit()

        except Exception as e:
            logger.error(f"创建会话失败: {e}")

    def end_session(self, session_id: str, total_crawled: int, total_discovered: int, twitter_found: int):
        """结束爬取会话"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                ended_at = datetime.now().isoformat()

                cursor.execute('''
                    UPDATE crawl_sessions
                    SET ended_at = ?, total_crawled = ?, total_discovered = ?,
                        twitter_found = ?, status = 'completed'
                    WHERE session_id = ?
                ''', (ended_at, total_crawled, total_discovered, twitter_found, session_id))

                conn.commit()

        except Exception as e:
            logger.error(f"结束会话失败: {e}")

    def get_all_twitter_links(self) -> List[Dict]:
        """获取所有Twitter发现记录"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT channel_url, twitter_link, discovered_at, proxy_used
                    FROM twitter_discoveries
                    ORDER BY created_at DESC
                ''')

                results = []
                for row in cursor.fetchall():
                    results.append({
                        'channel_url': row[0],
                        'twitter_link': row[1],
                        'discovered_at': row[2],
                        'proxy_used': row[3]
                    })

                return results

        except Exception as e:
            logger.error(f"获取Twitter链接失败: {e}")
            return []

    def get_stats(self) -> Dict:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_file) as conn:
                cursor = conn.cursor()

                # 总Twitter链接数
                cursor.execute('SELECT COUNT(*) FROM twitter_discoveries')
                total_twitter = cursor.fetchone()[0]

                # 总频道数
                cursor.execute('SELECT COUNT(*) FROM channels')
                total_channels = cursor.fetchone()[0]

                # 有Twitter的频道数
                cursor.execute('SELECT COUNT(*) FROM channels WHERE has_twitter = TRUE')
                channels_with_twitter = cursor.fetchone()[0]

                # 去重的Twitter链接数
                cursor.execute('SELECT COUNT(DISTINCT twitter_link) FROM twitter_discoveries')
                unique_twitter = cursor.fetchone()[0]

                return {
                    'total_twitter_discoveries': total_twitter,
                    'unique_twitter_links': unique_twitter,
                    'total_channels_crawled': total_channels,
                    'channels_with_twitter': channels_with_twitter,
                    'twitter_discovery_rate': f"{(channels_with_twitter/max(total_channels, 1))*100:.1f}%"
                }

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def is_channel_crawled(self, channel_url: str) -> bool:
        """检查频道是否已经爬取过"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT COUNT(*) FROM channels WHERE channel_url = ?', (channel_url,))
                    count = cursor.fetchone()[0]
                    return count > 0
            except Exception as e:
                logger.error(f"检查频道状态失败: {e}")
                return False

    def get_crawled_channels(self) -> set:
        """获取所有已爬取的频道"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT channel_url FROM channels')
                    results = cursor.fetchall()
                    return {row[0] for row in results}
            except Exception as e:
                logger.error(f"获取已爬取频道失败: {e}")
                return set()

    def get_discovered_twitter_links(self) -> set:
        """获取所有已发现的Twitter链接"""
        with self.lock:
            try:
                with sqlite3.connect(self.db_file) as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT DISTINCT twitter_link FROM twitter_discoveries')
                    results = cursor.fetchall()
                    return {row[0] for row in results}
            except Exception as e:
                logger.error(f"获取已发现Twitter链接失败: {e}")
                return set()

class ProxyManager:
    """代理管理器 - 支持Webshare代理池"""

    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = self._load_proxies()
        self.failed_proxies = set()
        self.current_index = 0
        self.consecutive_failures = 0

        if self.proxies:
            print(f"✅ 代理池加载成功: {len(self.proxies)} 个代理")
        else:
            print("⚠️ 未加载到代理，将使用直连模式")

    def _load_proxies(self) -> List[Dict]:
        """加载代理列表"""
        proxies = []

        if not os.path.exists(self.proxy_file):
            print(f"📁 代理文件不存在: {self.proxy_file}")
            return []

        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and not line.startswith('#'):
                        try:
                            # 解析格式: p.webshare.io:80:username:password
                            parts = line.split(':')
                            if len(parts) >= 4:
                                host, port, username, password = parts[0], parts[1], parts[2], parts[3]
                                proxy_url = f"http://{username}:{password}@{host}:{port}"
                                proxies.append({
                                    'http': proxy_url,
                                    'https': proxy_url,
                                    'id': line_num,
                                    'username': username,
                                    'host': host
                                })
                        except Exception as e:
                            logger.debug(f"解析代理失败 (行{line_num}): {e}")
        except Exception as e:
            logger.error(f"加载代理文件失败: {e}")

        return proxies

    def get_proxy(self) -> Optional[Dict]:
        """获取可用代理"""
        if not self.proxies:
            return None

        available_proxies = [p for p in self.proxies if p['id'] not in self.failed_proxies]

        if not available_proxies:
            print("🔄 重置失败代理列表")
            self.failed_proxies.clear()
            self.consecutive_failures = 0
            available_proxies = self.proxies

        if available_proxies:
            # 连续失败时跳过一些代理
            if self.consecutive_failures >= 3:
                skip_count = random.randint(5, 15)
                self.current_index += skip_count
                self.consecutive_failures = 0
                print(f"🔀 连续失败，跳过 {skip_count} 个代理")

            proxy = available_proxies[self.current_index % len(available_proxies)]
            self.current_index += 1
            return proxy

        return None

    def mark_failed(self, proxy_id: int):
        """标记代理失败"""
        if proxy_id:
            self.failed_proxies.add(proxy_id)
            self.consecutive_failures += 1

    def mark_success(self, proxy_id: int):
        """标记代理成功"""
        self.consecutive_failures = 0

    def get_stats(self) -> Dict:
        """获取代理统计"""
        if not self.proxies:
            return {'total': 0, 'active': 0, 'failed': 0, 'success_rate': '0%'}

        total = len(self.proxies)
        failed = len(self.failed_proxies)
        active = total - failed

        return {
            'total': total,
            'active': active,
            'failed': failed,
            'success_rate': f"{(active/total)*100:.1f}%" if total > 0 else "0%"
        }

class TwitterExtractor:
    """Twitter链接提取器"""

    def __init__(self):
        self.patterns = [
            r'https?://(?:www\.)?twitter\.com/[a-zA-Z0-9_]+',
            r'https?://(?:www\.)?x\.com/[a-zA-Z0-9_]+',
            r'twitter\.com/[a-zA-Z0-9_]+',
            r'x\.com/[a-zA-Z0-9_]+',
        ]
        self.compiled_patterns = [re.compile(p, re.IGNORECASE) for p in self.patterns]

    def extract(self, html_content: str) -> List[str]:
        """从HTML中提取Twitter链接"""
        twitter_links = set()

        for pattern in self.compiled_patterns:
            matches = pattern.findall(html_content)
            for match in matches:
                # 标准化链接
                if not match.startswith('http'):
                    match = f"https://{match}"

                # 清理链接
                clean_link = match.split('?')[0].split('#')[0]

                # 转换x.com为twitter.com
                clean_link = clean_link.replace('x.com', 'twitter.com')

                twitter_links.add(clean_link)

        return sorted(list(twitter_links))

class YouTubeSpider:
    """YouTube蜘蛛爬虫 - 多线程高速版本 + SQLite自动保存 + 持续运行增强"""

    def __init__(self, max_workers: int = 5):
        self.proxy_manager = ProxyManager()
        self.twitter_extractor = TwitterExtractor()
        self.db = DatabaseManager()  # 添加数据库管理器
        self.max_workers = max_workers  # 最大线程数

        # 爬取状态 - 线程安全，从数据库加载已有数据进行去重
        print("📂 正在加载历史数据进行去重...")
        self.discovered_channels = set()
        self.crawled_channels = self.db.get_crawled_channels()  # 从数据库加载已爬取频道
        self.discovered_twitter_links = self.db.get_discovered_twitter_links()  # 从数据库加载已发现Twitter
        self.crawl_queue = []
        self.twitter_discoveries = []
        self.total_crawled = 0
        self.is_running = True

        # 🚀 新增：备用种子频道池，用于持续补充
        self.backup_seeds = [
            # 科技频道
            "https://www.youtube.com/@LinusTechTips",
            "https://www.youtube.com/@MKBHD",
            "https://www.youtube.com/@UnboxTherapy",
            "https://www.youtube.com/@veritasium",
            "https://www.youtube.com/@TechLinked",
            "https://www.youtube.com/@iJustine",
            "https://www.youtube.com/@Austin",
            "https://www.youtube.com/@TechGumbo",

            # 游戏频道
            "https://www.youtube.com/@jacksepticeye",
            "https://www.youtube.com/@VanossGaming",
            "https://www.youtube.com/@Ninja",
            "https://www.youtube.com/@TheRadBrad",
            "https://www.youtube.com/@DanTDM",
            "https://www.youtube.com/@CoryxKenshin",
            "https://www.youtube.com/@8BitRyan",
            "https://www.youtube.com/@GameGrumps",

            # 娱乐频道
            "https://www.youtube.com/@smosh",
            "https://www.youtube.com/@GoodMythicalMorning",
            "https://www.youtube.com/@TeamCoco",
            "https://www.youtube.com/@SciShow",
            "https://www.youtube.com/@RyanHiga",
            "https://www.youtube.com/@TheEllenShow",
            "https://www.youtube.com/@TheLateLateShow",
            "https://www.youtube.com/@JimmyKimmelLive",

            # 音乐频道
            "https://www.youtube.com/@justinbieber",
            "https://www.youtube.com/@ArianaGrande",
            "https://www.youtube.com/@edsheeran",
            "https://www.youtube.com/@taylorswift",
            "https://www.youtube.com/@selenagomez",
            "https://www.youtube.com/@ddlovato",
            "https://www.youtube.com/@onedirection",
            "https://www.youtube.com/@billboard",

            # 体育频道
            "https://www.youtube.com/@espn",
            "https://www.youtube.com/@NFL",
            "https://www.youtube.com/@NBA",
            "https://www.youtube.com/@FIFAcom",
            "https://www.youtube.com/@ufc",
            "https://www.youtube.com/@wwe",
            "https://www.youtube.com/@Olympics",
            "https://www.youtube.com/@bleacherreport",

            # 新闻频道
            "https://www.youtube.com/@CNN",
            "https://www.youtube.com/@bbcnews",
            "https://www.youtube.com/@ABCNews",
            "https://www.youtube.com/@PhilipDeFranco",
            "https://www.youtube.com/@vox",
            "https://www.youtube.com/@vicenews",
            "https://www.youtube.com/@nytimes",
            "https://www.youtube.com/@washingtonpost",

            # 教育频道
            "https://www.youtube.com/@TED",
            "https://www.youtube.com/@khanacademy",
            "https://www.youtube.com/@CrashCourse",
            "https://www.youtube.com/@3Blue1Brown",
            "https://www.youtube.com/@Numberphile",
            "https://www.youtube.com/@MinutePhysics",
            "https://www.youtube.com/@AsapSCIENCE",
            "https://www.youtube.com/@Vsauce",

            # 🚀 新增：生活方式和美容
            "https://www.youtube.com/@jamescharles",
            "https://www.youtube.com/@NikkieTutorials",
            "https://www.youtube.com/@jeffreestar",
            "https://www.youtube.com/@emmachamberlain",
            "https://www.youtube.com/@EmmaChamberlin",
            "https://www.youtube.com/@zoella",
            "https://www.youtube.com/@tanya",
            "https://www.youtube.com/@michelle",

            # 🚀 新增：商业和创业
            "https://www.youtube.com/@garyvee",
            "https://www.youtube.com/@TEDxTalks",
            "https://www.youtube.com/@GaryVaynerchuk",
            "https://www.youtube.com/@TheTimFerrissShow",
            "https://www.youtube.com/@GrahamStephan",
            "https://www.youtube.com/@AndreJikh",
            "https://www.youtube.com/@MeetKevin",
            "https://www.youtube.com/@coffeezilla",

            # 🚀 新增：科学和技术深度
            "https://www.youtube.com/@ElectroBOOM",
            "https://www.youtube.com/@SmartEveryDay",
            "https://www.youtube.com/@engineerguy",
            "https://www.youtube.com/@SteveMould",
            "https://www.youtube.com/@styropyro",
            "https://www.youtube.com/@TheBackyardScientist",
            "https://www.youtube.com/@NileRed",
            "https://www.youtube.com/@CodeBullet",

            # 🚀 新增：旅行和冒险
            "https://www.youtube.com/@FunForLouis",
            "https://www.youtube.com/@krispysmore",
            "https://www.youtube.com/@yestheory",
            "https://www.youtube.com/@DamonAndJo",
            "https://www.youtube.com/@gypsyone",
            "https://www.youtube.com/@sorelle",
            "https://www.youtube.com/@vagabrothers",
            "https://www.youtube.com/@kararandlola",

            # 🚀 新增：汽车和机械
            "https://www.youtube.com/@topgear",
            "https://www.youtube.com/@TheGrandTour",
            "https://www.youtube.com/@ChrisFixit",
            "https://www.youtube.com/@DonutOperator",
            "https://www.youtube.com/@SavageGarage",
            "https://www.youtube.com/@MightCarMods",
            "https://www.youtube.com/@DougDeMuro",
            "https://www.youtube.com/@CarThrottle",

            # 🚀 新增：编程和开发
            "https://www.youtube.com/@CodeWithHarry",
            "https://www.youtube.com/@TechWithTim",
            "https://www.youtube.com/@ProgrammingWithMosh",
            "https://www.youtube.com/@CS50",
            "https://www.youtube.com/@freeCodeCamp",
            "https://www.youtube.com/@traversymedia",
            "https://www.youtube.com/@derekbanas",
            "https://www.youtube.com/@LearnCodeAcademy"
        ]

        # 🚀 新增：已使用的备用种子记录
        self.used_backup_seeds = set()

        # 🚀 新增：队列补充统计
        self.queue_refills = 0
        self.max_queue_refills = 50  # 最大补充次数，避免无限循环

        # 线程锁
        self.queue_lock = threading.Lock()
        self.stats_lock = threading.Lock()

        # 会话ID用于数据库记录
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.db.start_session(self.session_id)

        print(f"🚀 多线程YouTube蜘蛛爬虫初始化完成 (线程数: {max_workers})")
        print("💾 所有Twitter发现将自动保存到SQLite数据库")
        print(f"🔄 已加载历史数据: {len(self.crawled_channels)} 个已爬频道, {len(self.discovered_twitter_links)} 个已发现Twitter")
        print(f"🌱 备用种子池: {len(self.backup_seeds)} 个频道，确保持续运行")

    def add_seeds(self, channels: List[str]):
        """添加种子频道 - 带去重检查"""
        added_count = 0
        skipped_count = 0

        for channel in channels:
            if channel not in self.discovered_channels and channel not in self.crawled_channels:
                self.crawl_queue.append(channel)
                self.discovered_channels.add(channel)
                added_count += 1
            else:
                skipped_count += 1
                print(f"⏭️ 跳过已爬频道: {channel}")

        print(f"🌱 添加种子频道: {added_count} 个新频道")
        if skipped_count > 0:
            print(f"⏭️ 跳过重复频道: {skipped_count} 个")

    def refill_queue_with_backup_seeds(self, count: int = 5):
        """🚀 新增：从备用种子池补充队列"""
        if self.queue_refills >= self.max_queue_refills:
            print(f"⚠️ 已达到最大补充次数 ({self.max_queue_refills})，停止补充")
            return 0

        available_seeds = [seed for seed in self.backup_seeds
                          if seed not in self.used_backup_seeds
                          and seed not in self.crawled_channels
                          and seed not in self.discovered_channels]

        if not available_seeds:
            print("⚠️ 备用种子池已耗尽，重置使用记录")
            self.used_backup_seeds.clear()  # 重置已使用记录
            available_seeds = [seed for seed in self.backup_seeds
                              if seed not in self.crawled_channels]

        if not available_seeds:
            print("❌ 所有备用种子都已爬取完毕")
            return 0

        # 随机选择种子，增加多样性
        import random
        selected_seeds = random.sample(available_seeds, min(count, len(available_seeds)))

        added_count = 0
        for seed in selected_seeds:
            self.crawl_queue.append(seed)
            self.discovered_channels.add(seed)
            self.used_backup_seeds.add(seed)
            added_count += 1

        self.queue_refills += 1

        print(f"🔄 队列补充: 从备用种子池添加 {added_count} 个频道 (第{self.queue_refills}次补充)")
        print(f"   剩余备用种子: {len(available_seeds) - added_count} 个")

        return added_count

    def discover_channels_enhanced(self, channel_url: str, max_discover: int = 8) -> List[str]:
        """🚀 超级增强的频道发现 - 多种策略 + 搜索功能"""
        try:
            base_url = channel_url.rstrip('/')
            discovered = set()

            print(f"🔍 正在从 {channel_url} 发现新频道...")

            # 策略1: 从channels页面发现
            channels_page = f"{base_url}/channels"
            channels_from_page = self._extract_channels_from_url(channels_page)
            discovered.update(channels_from_page)
            print(f"   📄 从channels页面发现: {len(channels_from_page)} 个")

            # 策略2: 从主页面发现
            if len(discovered) < max_discover:
                channels_from_main = self._extract_channels_from_url(base_url)
                discovered.update(channels_from_main)
                print(f"   🏠 从主页面发现: {len(channels_from_main)} 个")

            # 策略3: 从featured页面发现
            if len(discovered) < max_discover:
                featured_page = f"{base_url}/featured"
                channels_from_featured = self._extract_channels_from_url(featured_page)
                discovered.update(channels_from_featured)
                print(f"   ⭐ 从featured页面发现: {len(channels_from_featured)} 个")

            # 策略4: 从videos页面发现
            if len(discovered) < max_discover:
                videos_page = f"{base_url}/videos"
                channels_from_videos = self._extract_channels_from_url(videos_page)
                discovered.update(channels_from_videos)
                print(f"   🎥 从videos页面发现: {len(channels_from_videos)} 个")

            # 🚀 新增策略5: 从shorts页面发现
            if len(discovered) < max_discover:
                shorts_page = f"{base_url}/shorts"
                channels_from_shorts = self._extract_channels_from_url(shorts_page)
                discovered.update(channels_from_shorts)
                print(f"   📱 从shorts页面发现: {len(channels_from_shorts)} 个")

            # 🚀 新增策略6: 从community页面发现
            if len(discovered) < max_discover:
                community_page = f"{base_url}/community"
                channels_from_community = self._extract_channels_from_url(community_page)
                discovered.update(channels_from_community)
                print(f"   👥 从community页面发现: {len(channels_from_community)} 个")

            # 🚀 新增策略7: 从playlists页面发现
            if len(discovered) < max_discover:
                playlists_page = f"{base_url}/playlists"
                channels_from_playlists = self._extract_channels_from_url(playlists_page)
                discovered.update(channels_from_playlists)
                print(f"   📋 从playlists页面发现: {len(channels_from_playlists)} 个")

            # 🚀 新增策略8: 从单个视频页面发现更多频道
            if len(discovered) < max_discover:
                video_channels = self._discover_from_recent_videos(base_url, max_videos=3)
                discovered.update(video_channels)
                print(f"   🎬 从视频详情页发现: {len(video_channels)} 个")

            # 🚀 新增策略9: 通过搜索相关频道发现
            if len(discovered) < max_discover:
                search_channels = self._discover_through_search(channel_url, max_results=5)
                discovered.update(search_channels)
                print(f"   🔍 通过搜索发现: {len(search_channels)} 个")

            print(f"   📊 总共发现: {len(discovered)} 个原始链接")

            # 过滤新频道 - 改进去重逻辑
            new_channels = []
            for url in discovered:
                if (len(new_channels) >= max_discover):
                    break
                # 更宽松的去重条件：只检查是否在当前会话中发现过
                if (url not in self.discovered_channels and
                    url != channel_url and
                    self._is_valid_channel_url(url)):
                    new_channels.append(url)
                    self.discovered_channels.add(url)

            print(f"   ✅ 有效新频道: {len(new_channels)} 个")
            for i, ch in enumerate(new_channels[:3], 1):  # 只显示前3个
                print(f"      {i}. {ch}")
            if len(new_channels) > 3:
                print(f"      ... 还有 {len(new_channels) - 3} 个")

            return new_channels

        except Exception as e:
            print(f"❌ 发现频道失败 {channel_url}: {e}")
            return []

    def _discover_from_recent_videos(self, channel_url: str, max_videos: int = 3) -> List[str]:
        """🚀 新增：从频道最新视频页面发现其他频道"""
        try:
            videos_page = f"{channel_url.rstrip('/')}/videos"
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            proxy = self.proxy_manager.get_proxy()
            response = session.get(videos_page, proxies=proxy, timeout=15)

            if response.status_code != 200:
                return []

            # 提取视频URL
            video_pattern = r'href="(/watch\?v=[a-zA-Z0-9_-]{11})"'
            video_matches = re.findall(video_pattern, response.text)

            discovered_channels = set()

            # 访问前几个视频页面，从评论和描述中发现频道
            for i, video_path in enumerate(video_matches[:max_videos]):
                try:
                    video_url = f"https://www.youtube.com{video_path}"
                    video_response = session.get(video_url, proxies=proxy, timeout=15)

                    if video_response.status_code == 200:
                        # 从视频页面提取频道链接
                        channels = self._extract_channel_urls_from_html(video_response.text)
                        discovered_channels.update(channels)

                    # 避免请求过于频繁
                    if i < max_videos - 1:
                        time.sleep(1)

                except Exception as e:
                    logger.debug(f"访问视频页面失败: {e}")
                    continue

            session.close()
            return list(discovered_channels)

        except Exception as e:
            logger.debug(f"从视频页面发现频道失败: {e}")
            return []

    def _discover_through_search(self, channel_url: str, max_results: int = 5) -> List[str]:
        """🚀 新增：通过搜索相关关键词发现频道"""
        try:
            # 从频道URL提取频道名或ID作为搜索关键词
            channel_name = self._extract_channel_name(channel_url)
            if not channel_name:
                return []

            # 生成搜索关键词
            search_keywords = self._generate_search_keywords(channel_name)
            discovered_channels = set()

            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })

            for keyword in search_keywords[:2]:  # 限制搜索2个关键词
                try:
                    search_url = f"https://www.youtube.com/results?search_query={keyword.replace(' ', '+')}"
                    proxy = self.proxy_manager.get_proxy()
                    response = session.get(search_url, proxies=proxy, timeout=15)

                    if response.status_code == 200:
                        # 从搜索结果页面提取频道链接
                        channels = self._extract_channel_urls_from_html(response.text)
                        discovered_channels.update(channels)

                        if len(discovered_channels) >= max_results:
                            break

                    time.sleep(2)  # 搜索间隔

                except Exception as e:
                    logger.debug(f"搜索失败 {keyword}: {e}")
                    continue

            session.close()
            return list(discovered_channels)[:max_results]

        except Exception as e:
            logger.debug(f"通过搜索发现频道失败: {e}")
            return []

    def _extract_channel_name(self, channel_url: str) -> str:
        """从频道URL提取频道名"""
        try:
            # 匹配不同类型的频道URL格式
            patterns = [
                r'/@([^/?]+)',  # @username格式
                r'/c/([^/?]+)',  # /c/channelname格式
                r'/user/([^/?]+)',  # /user/username格式
                r'/channel/([^/?]+)'  # /channel/id格式
            ]

            for pattern in patterns:
                match = re.search(pattern, channel_url)
                if match:
                    name = match.group(1)
                    # 清理特殊字符，只保留字母数字
                    clean_name = re.sub(r'[^a-zA-Z0-9]', '', name)
                    return clean_name

            return ""

        except Exception:
            return ""

    def _generate_search_keywords(self, channel_name: str) -> List[str]:
        """生成搜索关键词"""
        if not channel_name:
            return []

        keywords = []

        # 基础关键词
        keywords.append(channel_name)

        # 添加YouTube相关关键词
        keywords.append(f"{channel_name} youtube")
        keywords.append(f"{channel_name} channel")

        # 如果频道名包含数字或特殊模式，生成变体
        if any(c.isdigit() for c in channel_name):
            clean_name = re.sub(r'\d+', '', channel_name)
            if clean_name and len(clean_name) > 2:
                keywords.append(clean_name)

        # 限制关键词数量
        return keywords[:4]

    def crawl_channel(self, channel_url: str) -> Dict:
        """爬取单个频道 - 线程安全版本"""
        result = {
            'channel_url': channel_url,
            'twitter_links': [],
            'success': False,
            'error': None,
            'proxy_used': '直连'
        }

        # 为每个线程创建独立的session
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        try:
            proxy = self.proxy_manager.get_proxy()
            if proxy:
                result['proxy_used'] = f"{proxy['username']}@{proxy['host']}"

            # 访问频道about页面
            about_url = f"{channel_url.rstrip('/')}/about"

            response = session.get(
                about_url,
                proxies=proxy,
                timeout=25,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_manager.mark_success(proxy['id'])

                # 提取Twitter链接
                twitter_links = self.twitter_extractor.extract(response.text)
                result['twitter_links'] = twitter_links
                result['success'] = True

                if twitter_links:
                    # 过滤掉已发现的Twitter链接
                    new_twitter_links = []
                    duplicate_links = []

                    for link in twitter_links:
                        if link not in self.discovered_twitter_links:
                            new_twitter_links.append(link)
                            self.discovered_twitter_links.add(link)  # 立即添加到内存去重集合
                        else:
                            duplicate_links.append(link)

                    if new_twitter_links:
                        print(f"✅ 发现新Twitter: {channel_url}")
                        for link in new_twitter_links:
                            print(f"   🔗 {link}")

                        # 保存到数据库（线程安全）
                        self.db.save_twitter_discovery(
                            channel_url, new_twitter_links, result['proxy_used'], self.session_id
                        )

                        # 添加到内存记录（线程安全）
                        with self.stats_lock:
                            self.twitter_discoveries.append({
                                'channel_url': channel_url,
                                'twitter_links': new_twitter_links,
                                'discovered_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'proxy_used': result['proxy_used']
                            })

                    if duplicate_links:
                        print(f"🔄 发现重复Twitter: {channel_url}")
                        for link in duplicate_links:
                            print(f"   ⏭️ {link} (已存在)")

                    # 更新结果中的Twitter链接为新发现的
                    result['twitter_links'] = new_twitter_links
                else:
                    print(f"⚪ 无Twitter: {channel_url}")

            else:
                if proxy:
                    self.proxy_manager.mark_failed(proxy['id'])
                result['error'] = f"HTTP {response.status_code}"
                print(f"❌ 访问失败: {channel_url} ({response.status_code})")

        except Exception as e:
            if proxy:
                self.proxy_manager.mark_failed(proxy['id'])
            result['error'] = str(e)
            print(f"❌ 爬取错误: {channel_url} - {e}")
        finally:
            # 关闭session释放资源
            session.close()

        return result

    def _extract_channels_from_url(self, url: str) -> List[str]:
        """从URL提取频道链接 - 线程安全版本"""
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        try:
            proxy = self.proxy_manager.get_proxy()

            response = session.get(
                url,
                proxies=proxy,
                timeout=20,
                allow_redirects=True
            )

            if response.status_code == 200:
                if proxy:
                    self.proxy_manager.mark_success(proxy['id'])
                return self._extract_channel_urls_from_html(response.text)
            else:
                if proxy:
                    self.proxy_manager.mark_failed(proxy['id'])

        except Exception as e:
            if proxy:
                self.proxy_manager.mark_failed(proxy['id'])
            logger.debug(f"提取频道链接失败: {e}")
        finally:
            session.close()

        return []

    def _extract_channel_urls_from_html(self, html_content: str) -> List[str]:
        """从HTML中提取频道URL - 🚀 增强版正则表达式"""
        # 🚀 更全面的频道匹配模式
        channel_patterns = [
            # 标准格式
            r'https://www\.youtube\.com/@[a-zA-Z0-9_.-]+',
            r'https://www\.youtube\.com/c/[a-zA-Z0-9_.-]+',
            r'https://www\.youtube\.com/channel/[a-zA-Z0-9_-]+',
            r'https://www\.youtube\.com/user/[a-zA-Z0-9_.-]+',

            # 相对路径格式
            r'/(@[a-zA-Z0-9_.-]+)',
            r'/(c/[a-zA-Z0-9_.-]+)',
            r'/(channel/[a-zA-Z0-9_-]+)',
            r'/(user/[a-zA-Z0-9_.-]+)',

            # 🚀 新增：JSON格式中的频道ID（常见于API响应中）
            r'"channelId":"([a-zA-Z0-9_-]{24})"',
            r'"browseEndpoint":{"browseId":"([a-zA-Z0-9_-]{24})"',
            r'"externalChannelId":"([a-zA-Z0-9_-]{24})"',

            # 🚀 新增：href属性中的频道链接（更宽松匹配）
            r'href="(/[@a-zA-Z0-9_.-]+)"',
            r'href="(/@[a-zA-Z0-9_.-]+)"',
            r'href="(/c/[a-zA-Z0-9_.-]+)"',
            r'href="(/user/[a-zA-Z0-9_.-]+)"',
            r'href="(/channel/[a-zA-Z0-9_-]+)"',

            # 🚀 新增：JavaScript变量中的频道链接
            r'channelUrl["\']?\s*:\s*["\']([^"\']+)["\']',
            r'channel["\']?\s*:\s*["\']([^"\']+)["\']',

            # 🚀 新增：@username格式（各种引号）
            r'[@]([a-zA-Z0-9_.-]+)',
            r'"@([a-zA-Z0-9_.-]+)"',
            r"'@([a-zA-Z0-9_.-]+)'",

            # 🚀 新增：YouTube短链接格式
            r'youtu\.be/c/([a-zA-Z0-9_.-]+)',
            r'youtube\.com/c/([a-zA-Z0-9_.-]+)',

            # 🚀 新增：移动端链接
            r'm\.youtube\.com/@([a-zA-Z0-9_.-]+)',
            r'm\.youtube\.com/c/([a-zA-Z0-9_.-]+)',
            r'm\.youtube\.com/channel/([a-zA-Z0-9_-]+)',

            # 🚀 新增：嵌入式iframe中的频道链接
            r'embed/([a-zA-Z0-9_-]{11})', # 从视频ID可能推导出频道
        ]

        channels = set()

        for pattern in channel_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                # 处理不同的匹配结果
                if pattern.startswith(r'https://'):
                    # 完整URL
                    clean_url = match
                elif pattern.startswith(r'"/') or pattern.startswith(r"'/") or pattern.startswith(r'/'):
                    # 相对路径
                    if match.startswith('/'):
                        clean_url = f"https://www.youtube.com{match}"
                    else:
                        clean_url = f"https://www.youtube.com/{match}"
                elif 'channelId' in pattern or 'browseId' in pattern or 'externalChannelId' in pattern:
                    # 频道ID格式
                    clean_url = f"https://www.youtube.com/channel/{match}"
                elif pattern.startswith(r'[@]') or pattern.startswith(r'"@') or pattern.startswith(r"'@"):
                    # @username格式
                    username = match.replace('@', '')
                    clean_url = f"https://www.youtube.com/@{username}"
                elif 'youtu.be' in pattern:
                    # YouTube短链接
                    clean_url = f"https://www.youtube.com/c/{match}"
                elif 'm.youtube.com' in pattern:
                    # 移动端链接转换
                    if '/@' in pattern:
                        clean_url = f"https://www.youtube.com/@{match}"
                    elif '/c/' in pattern:
                        clean_url = f"https://www.youtube.com/c/{match}"
                    elif '/channel/' in pattern:
                        clean_url = f"https://www.youtube.com/channel/{match}"
                else:
                    # 其他格式
                    if not match.startswith('http'):
                        clean_url = f"https://www.youtube.com/{match}"
                    else:
                        clean_url = match

                # 验证并添加有效URL
                if self._is_valid_channel_url(clean_url):
                    channels.add(clean_url)

        return list(channels)

    def _is_valid_channel_url(self, url: str) -> bool:
        """验证YouTube频道URL"""
        if not url or not isinstance(url, str):
            return False

        valid_patterns = [
            r'https://www\.youtube\.com/@[a-zA-Z0-9_.-]+$',
            r'https://www\.youtube\.com/c/[a-zA-Z0-9_.-]+$',
            r'https://www\.youtube\.com/channel/[a-zA-Z0-9_-]+$',
            r'https://www\.youtube\.com/user/[a-zA-Z0-9_.-]+$'
        ]

        return any(re.match(pattern, url) for pattern in valid_patterns)

    def run_multithreaded(self, max_total: int = 200, max_discover_per_channel: int = 8):
        """多线程高速爬取模式 - 🚀 增强持续运行能力"""
        print(f"🚀 开始多线程爬取 (目标: {max_total} 个频道, 线程数: {self.max_workers})")
        print("💡 按 Ctrl+C 可随时停止")
        print("💾 所有结果将自动保存到SQLite数据库")
        print("🔄 队列为空时将自动补充新种子，确保持续运行")

        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                while self.is_running and self.total_crawled < max_total:
                    # 🚀 改进：检查队列状态，提前补充
                    with self.queue_lock:
                        if len(self.crawl_queue) < self.max_workers:
                            print(f"🔄 队列频道不足 ({len(self.crawl_queue)} < {self.max_workers})，开始补充...")
                            added = self.refill_queue_with_backup_seeds(count=10)
                            if added == 0:
                                print("📋 无法补充更多频道，爬取将结束")
                                break

                    # 准备批次任务
                    batch_urls = []
                    with self.queue_lock:
                        batch_size = min(self.max_workers * 2, len(self.crawl_queue), max_total - self.total_crawled)
                        for _ in range(batch_size):
                            if self.crawl_queue:
                                url = self.crawl_queue.pop(0)
                                if url not in self.crawled_channels:
                                    batch_urls.append(url)
                                    self.crawled_channels.add(url)

                    if not batch_urls:
                        print("📋 队列为空，尝试最后一次补充...")
                        added = self.refill_queue_with_backup_seeds(count=5)
                        if added == 0:
                            print("📋 队列为空且无法补充，爬取完成")
                            break
                        else:
                            continue  # 继续循环，使用新补充的频道

                    print(f"\n🔄 提交批次任务: {len(batch_urls)} 个频道")

                    # 提交任务到线程池
                    future_to_url = {
                        executor.submit(self.crawl_channel, url): url
                        for url in batch_urls
                    }

                    # 处理完成的任务
                    for future in as_completed(future_to_url):
                        url = future_to_url[future]
                        try:
                            result = future.result()

                            with self.stats_lock:
                                self.total_crawled += 1

                            print(f"🔍 完成 [{self.total_crawled}/{max_total}]: {url}")

                            # 发现新频道 - 🚀 使用增强版发现
                            if result['success']:
                                new_channels = self.discover_channels_enhanced(url, max_discover_per_channel)
                                if new_channels:
                                    with self.queue_lock:
                                        added_new_channels = 0
                                        for new_url in new_channels:
                                            # 🚀 改进：更宽松的去重检查
                                            if (new_url not in self.crawled_channels):
                                                self.crawl_queue.append(new_url)
                                                added_new_channels += 1

                                    if added_new_channels > 0:
                                        print(f"🕷️ 发现新频道: {added_new_channels} 个 (跳过 {len(new_channels) - added_new_channels} 个重复)")
                                else:
                                    # 🚀 如果没有发现新频道，从备用池补充
                                    print("🔄 该频道未发现新频道，从备用池补充...")
                                    with self.queue_lock:
                                        self.refill_queue_with_backup_seeds(count=2)

                            # 显示统计
                            if self.total_crawled % 10 == 0:
                                self.show_stats()

                        except Exception as e:
                            print(f"❌ 任务执行失败 {url}: {e}")

                    # 检查是否达到目标
                    if self.total_crawled >= max_total:
                        break

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断爬取")
            self.is_running = False
        except Exception as e:
            print(f"❌ 多线程爬取错误: {e}")
        finally:
            self.show_final_summary()

    def run_continuous(self, max_total: int = 200, max_discover_per_channel: int = 8):
        """持续爬取模式 - 🚀 增强持续运行能力"""
        print(f"🚀 开始持续爬取 (目标: {max_total} 个频道)")
        print("💡 按 Ctrl+C 可随时停止")
        print("🔄 队列为空时将自动补充新种子，确保持续运行")

        try:
            while self.is_running and self.total_crawled < max_total:
                # 🚀 改进：检查队列，提前补充
                if not self.crawl_queue:
                    print("📋 队列为空，从备用种子池补充...")
                    added = self.refill_queue_with_backup_seeds(count=5)
                    if added == 0:
                        print("📋 无法补充更多频道，爬取完成")
                        break

                # 获取下一个频道
                channel_url = self.crawl_queue.pop(0)
                self.crawled_channels.add(channel_url)

                print(f"\n🔍 爬取频道 [{self.total_crawled + 1}/{max_total}]: {channel_url}")

                # 爬取频道
                result = self.crawl_channel(channel_url)
                self.total_crawled += 1

                # 发现新频道 - 🚀 使用增强版发现
                if result['success']:
                    new_channels = self.discover_channels_enhanced(channel_url, max_discover_per_channel)
                    if new_channels:
                        added_new_channels = 0
                        for new_url in new_channels:
                            # 🚀 改进：更宽松的去重检查
                            if new_url not in self.crawled_channels:
                                self.crawl_queue.append(new_url)
                                added_new_channels += 1

                        if added_new_channels > 0:
                            print(f"🕷️ 发现新频道: {added_new_channels} 个 (跳过 {len(new_channels) - added_new_channels} 个重复)")
                    else:
                        # 🚀 如果没有发现新频道，从备用池补充
                        print("🔄 该频道未发现新频道，从备用池补充...")
                        self.refill_queue_with_backup_seeds(count=2)

                # 显示统计
                if self.total_crawled % 10 == 0:
                    self.show_stats()

                # 延迟
                time.sleep(random.uniform(2, 4))

        except KeyboardInterrupt:
            print("\n⏹️ 用户中断爬取")
            self.is_running = False
        except Exception as e:
            print(f"\n❌ 爬取出错: {e}")

        print("\n🏁 爬取结束")
        self.show_final_summary()

    def show_stats(self):
        """显示统计信息 - 🚀 增加队列补充统计"""
        proxy_stats = self.proxy_manager.get_stats()

        print(f"\n📊 爬取统计:")
        print(f"   🕷️ 已发现频道: {len(self.discovered_channels)}")
        print(f"   ✅ 已爬取频道: {self.total_crawled}")
        print(f"   📋 队列中频道: {len(self.crawl_queue)}")
        print(f"   🔄 队列补充次数: {self.queue_refills}")
        print(f"   🌱 剩余备用种子: {len(self.backup_seeds) - len(self.used_backup_seeds)}")
        print(f"   🐦 发现Twitter: {len(self.twitter_discoveries)} 个")
        if self.total_crawled > 0:
            print(f"   📈 Twitter发现率: {(len(self.twitter_discoveries)/self.total_crawled)*100:.1f}%")
        print(f"   🌐 代理状态: {proxy_stats['active']}/{proxy_stats['total']} 可用 ({proxy_stats['success_rate']})")

    # 🚀 为了向后兼容，保留原来的discover_channels方法
    def discover_channels(self, channel_url: str, max_discover: int = 6) -> List[str]:
        """向后兼容的频道发现方法"""
        return self.discover_channels_enhanced(channel_url, max_discover)

    def show_final_summary(self):
        """显示最终总结"""
        print(f"\n🎉 爬取总结:")
        print("=" * 60)

        self.show_stats()

        if self.twitter_discoveries:
            print(f"\n🐦 发现的Twitter链接 (共 {len(self.twitter_discoveries)} 个):")
            print("-" * 60)

            all_twitter_links = set()
            for i, discovery in enumerate(self.twitter_discoveries, 1):
                print(f"\n{i}. YouTube频道: {discovery['channel_url']}")
                print(f"   发现时间: {discovery['discovered_at']}")
                print(f"   使用代理: {discovery['proxy_used']}")
                print("   Twitter链接:")
                for link in discovery['twitter_links']:
                    print(f"   🔗 {link}")
                    all_twitter_links.add(link)
                print("   " + "-" * 58)

            print(f"\n📋 所有Twitter链接汇总 (去重后 {len(all_twitter_links)} 个):")
            for i, link in enumerate(sorted(all_twitter_links), 1):
                print(f"   {i:2d}. {link}")
        else:
            print("\n🐦 未发现任何Twitter链接")

# 快速启动函数
def quick_start():
    """快速启动函数"""
    print("🚀 YouTube Twitter爬虫 - 多线程高速版本 + SQLite自动保存")
    print("=" * 60)

    print("\n选择爬虫模式:")
    print("1. 🐌 单线程模式 (稳定但较慢)")
    print("2. 🚀 多线程模式 (高速并发，推荐)")

    try:
        mode_choice = input("\n请选择模式 (1-2): ").strip()

        if mode_choice == "2":
            # 多线程模式
            print("\n选择线程数:")
            print("1. 3线程 (保守)")
            print("2. 5线程 (推荐)")
            print("3. 8线程 (激进)")
            print("4. 自定义")

            thread_choice = input("\n请选择 (1-4): ").strip()

            if thread_choice == "1":
                max_workers = 3
            elif thread_choice == "2":
                max_workers = 5
            elif thread_choice == "3":
                max_workers = 8
            elif thread_choice == "4":
                try:
                    max_workers = int(input("请输入线程数 (1-10): "))
                    max_workers = max(1, min(10, max_workers))
                except:
                    max_workers = 5
            else:
                max_workers = 5

            spider = YouTubeSpider(max_workers=max_workers)
        else:
            # 单线程模式
            spider = YouTubeSpider(max_workers=1)

        # 默认种子频道
        seed_channels = [
            "https://www.youtube.com/@MrBeast",
            "https://www.youtube.com/@PewDiePie",
            "https://www.youtube.com/@Markiplier"
        ]

        spider.add_seeds(seed_channels)

        print("\n选择爬取规模:")
        print("1. 快速测试 (20个频道)")
        print("2. 中等规模 (100个频道)")
        print("3. 大规模爬取 (200个频道)")
        print("4. 自定义数量")

        choice = input("\n请选择 (1-4): ").strip()

        if choice == "1":
            target = 20
        elif choice == "2":
            target = 100
        elif choice == "3":
            target = 200
        elif choice == "4":
            try:
                target = int(input("请输入爬取数量: "))
                target = max(1, target)
            except:
                target = 50
        else:
            target = 50

        # 根据模式选择运行方法
        if mode_choice == "2" and spider.max_workers > 1:
            print(f"\n🚀 启动多线程爬取 (线程数: {spider.max_workers})")
            spider.run_multithreaded(max_total=target)
        else:
            print(f"\n🐌 启动单线程爬取")
            spider.run_continuous(max_total=target)

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        print("程序已安全退出")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        print("请检查网络连接和代理设置")



if __name__ == "__main__":
    quick_start()
