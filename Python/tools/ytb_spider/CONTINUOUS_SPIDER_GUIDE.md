# YouTube持续蜘蛛式爬虫 v3.0 使用指南

## 🚀 快速开始

### 1. 基本使用
```python
from youtube_continuous_spider_v3 import ContinuousSpider

# 创建爬虫实例
spider = ContinuousSpider()

# 添加种子频道
spider.add_seed_channels([
    "https://www.youtube.com/@MrBeast",
    "https://www.youtube.com/@PewDiePie"
])

# 开始持续爬取
spider.continuous_crawl(
    max_total_crawl=100,  # 爬取100个频道
    max_discover_per_channel=8  # 每个频道发现8个新频道
)
```

### 2. 测试版本使用
```bash
# 运行测试脚本
python test_continuous_spider_v3.py

# 选择运行模式:
# 1. 快速测试 (10个频道)
# 2. 完整爬取 (200个频道)  
# 3. 仅测试代理池
```

## 🔧 核心功能

### 持续爬取模式
- **无限制爬取**: 不再受固定数量限制，可以持续运行
- **智能队列管理**: 自动管理爬取队列，避免重复
- **实时统计**: 每10个频道显示一次统计信息
- **优雅中断**: 支持Ctrl+C优雅停止

### 智能IP切换
- **61270个代理池**: 使用你的webshare代理文件
- **自动故障检测**: 连续失败3次自动切换IP段
- **代理统计**: 实时监控代理成功率和状态
- **智能跳转**: 失败时跳过5-15个代理避免问题IP段

### Twitter链接展示
```
🐦 发现的Twitter链接 (共 9 个):

1. YouTube频道: https://www.youtube.com/@MrBeast
   Twitter链接:
   🔗 https://twitter.com/MrBeast
   发现时间: 2025-06-26T18:30:45.123456
   使用代理: <EMAIL>
   ------------------------------------------------------------
```

## 📊 统计信息

### 实时统计显示
```
📊 爬取统计:
   🕷️  已发现频道: 45
   ✅ 已爬取频道: 20
   📋 队列中频道: 25
   🐦 发现Twitter: 9 个链接
   📈 发现率: 45.0%
   🌐 代理状态: 61250/61270 可用 (99.9%)
```

## ⚙️ 配置参数

### 爬取参数
```python
spider.continuous_crawl(
    max_total_crawl=200,        # 最大爬取频道数
    max_discover_per_channel=8  # 每个频道最大发现数
)
```

### 代理配置
```python
from youtube_continuous_spider_v3 import ProxyConfig

config = ProxyConfig(
    proxy_file="/path/to/webshare_proxy.txt",
    max_failures=5,           # 最大失败次数
    timeout=25,              # 请求超时时间
    switch_threshold=3       # 连续失败阈值
)
```

## 🛡️ 错误处理

### 自动恢复机制
- **代理失败**: 自动标记失败代理，切换到可用代理
- **网络错误**: 自动重试，智能延迟
- **HTTP错误**: 记录错误状态，继续下一个频道
- **连续失败**: 跳转IP段，避免问题区域

### 日志记录
```python
# 日志文件: youtube_continuous_spider.log
# 包含详细的爬取过程和错误信息
```

## 🎯 最佳实践

### 1. 种子频道选择
- 选择活跃的、有Twitter链接的频道作为种子
- 建议3-5个不同类型的频道
- 避免选择已被封禁或不活跃的频道

### 2. 参数调优
```python
# 快速测试
max_total_crawl=20, max_discover_per_channel=5

# 中等规模
max_total_crawl=100, max_discover_per_channel=8

# 大规模爬取
max_total_crawl=500, max_discover_per_channel=10
```

### 3. 代理管理
- 确保webshare_proxy.txt文件路径正确
- 定期检查代理池状态
- 监控代理成功率，低于80%时考虑更新代理

## 🚨 注意事项

### 使用限制
- 仅用于学习和研究目的
- 遵守YouTube的服务条款
- 合理控制爬取频率，避免对服务器造成压力

### 性能建议
- 建议在稳定的网络环境下运行
- 长时间运行时监控内存使用情况
- 定期保存爬取结果，避免数据丢失

### 故障排除
1. **代理连接失败**: 检查代理文件格式和网络连接
2. **爬取停止**: 检查是否达到最大爬取数量
3. **Twitter链接不显示**: 检查频道是否真的包含Twitter链接
4. **内存占用过高**: 重启爬虫或减少并发数

## 📈 性能指标

### 预期表现
- **爬取速度**: 2-5秒/频道（含发现时间）
- **发现效率**: 每个频道发现5-8个新频道
- **Twitter发现率**: 30-50%的频道包含Twitter链接
- **代理成功率**: 95%+（使用高质量代理）

### 资源消耗
- **内存**: 约100-200MB
- **网络**: 每个频道约1-2MB流量
- **CPU**: 低CPU占用，主要是网络I/O

## 🔄 版本更新

当前版本: **v3.0.0**
- 持续爬取模式
- 智能IP切换
- 实时Twitter展示
- 强化错误恢复

下一版本计划: **v3.1.0**
- 数据库存储支持
- Web界面管理
- 更多统计图表
- 定时任务支持
