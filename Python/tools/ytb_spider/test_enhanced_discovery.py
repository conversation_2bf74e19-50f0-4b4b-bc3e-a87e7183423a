#!/usr/bin/env python3
"""
测试增强版频道发现功能
验证9种策略是否正常工作

作者: GreenJoson
版本: v1.0
创建时间: 2025-06-26 21:00
"""

from youtube_final_spider import YouTubeSpider
import time

def test_enhanced_discovery():
    """测试增强版频道发现功能"""
    print("🧪 测试增强版频道发现功能")
    print("=" * 50)

    # 创建爬虫实例
    spider = YouTubeSpider(max_workers=2)

    # 测试频道列表
    test_channels = [
        "https://www.youtube.com/@PewDiePie",
        "https://www.youtube.com/@MKBHD",
        "https://www.youtube.com/@MrBeast"
    ]

    total_discovered = 0

    for i, channel in enumerate(test_channels, 1):
        print(f"\n🔍 测试 {i}/{len(test_channels)}: {channel}")
        print("-" * 40)

        try:
            # 使用增强版发现功能
            discovered = spider.discover_channels_enhanced(channel, max_discover=12)

            print(f"✅ 发现频道数量: {len(discovered)}")

            if discovered:
                print(f"📋 发现的频道 (前5个):")
                for j, ch in enumerate(discovered[:5], 1):
                    print(f"   {j}. {ch}")

                if len(discovered) > 5:
                    print(f"   ... 还有 {len(discovered) - 5} 个")

            total_discovered += len(discovered)

            # 稍作延迟，避免请求过于频繁
            if i < len(test_channels):
                print("⏳ 等待2秒...")
                time.sleep(2)

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print(f"   🔍 测试频道数: {len(test_channels)}")
    print(f"   ✅ 总发现频道: {total_discovered}")
    print(f"   📈 平均发现数: {total_discovered/len(test_channels):.1f} 个/频道")
    print(f"   🌱 备用种子池: {len(spider.backup_seeds)} 个")

    if total_discovered > len(test_channels) * 8:
        print("🎉 测试通过！频道发现功能运行良好")
    else:
        print("⚠️ 发现数量较少，可能需要检查网络或代理")

if __name__ == "__main__":
    test_enhanced_discovery()