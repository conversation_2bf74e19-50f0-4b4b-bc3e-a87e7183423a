# 🧪 Google Colab 代理测试指南

## 快速开始

### 1. 上传文件到Colab

在Google Colab中运行以下代码上传代理文件：

```python
from google.colab import files
import os

# 上传代理文件
print("📁 请选择并上传 webshare_proxy.txt 文件")
uploaded = files.upload()

# 检查文件
for filename in uploaded.keys():
    print(f"✅ 已上传: {filename} ({len(uploaded[filename])} bytes)")
```

### 2. 安装依赖

```python
# 安装必要的包
!pip install requests

print("✅ 依赖安装完成")
```

### 3. 运行代理测试脚本

```python
# 创建代理测试脚本
proxy_test_code = '''
#!/usr/bin/env python3
"""
代理测试脚本 - Google Colab专用版本
"""

import requests
import time
import random
from typing import List, Dict, Tuple
import json

class ProxyTester:
    """代理测试器"""

    def __init__(self, proxy_file: str = "webshare_proxy.txt"):
        self.proxy_file = proxy_file
        self.proxies = []
        self.load_proxies()

    def load_proxies(self):
        """加载代理列表"""
        try:
            with open(self.proxy_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            print(f"📁 代理文件: {self.proxy_file}")
            print(f"📊 文件包含: {len(lines)} 行")

            valid_count = 0
            for line in lines:
                line = line.strip()
                if line and ':' in line:
                    parts = line.split(':')
                    if len(parts) == 4:
                        host, port, username, password = parts
                        proxy_info = {
                            'host': host,
                            'port': int(port),
                            'username': username,
                            'password': password,
                            'proxy_dict': {
                                'http': f'http://{username}:{password}@{host}:{port}',
                                'https': f'http://{username}:{password}@{host}:{port}'
                            }
                        }
                        self.proxies.append(proxy_info)
                        valid_count += 1

            print(f"✅ 有效代理: {valid_count} 个")

        except FileNotFoundError:
            print(f"❌ 代理文件不存在: {self.proxy_file}")
            print("💡 请确保代理文件已上传到Colab")
        except Exception as e:
            print(f"❌ 加载代理失败: {e}")

    def test_single_proxy(self, proxy_info: Dict, test_url: str = "http://httpbin.org/ip", timeout: int = 15) -> Dict:
        """测试单个代理"""
        result = {
            'proxy': f"{proxy_info['host']}:{proxy_info['port']}",
            'status': 'unknown',
            'response_time': None,
            'ip': None,
            'error': None
        }

        try:
            start_time = time.time()
            response = requests.get(
                test_url,
                proxies=proxy_info['proxy_dict'],
                timeout=timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            response_time = round(time.time() - start_time, 2)

            if response.status_code == 200:
                try:
                    data = response.json()
                    result.update({
                        'status': 'success',
                        'response_time': response_time,
                        'ip': data.get('origin', 'unknown')
                    })
                except:
                    result.update({
                        'status': 'success',
                        'response_time': response_time,
                        'ip': 'unknown'
                    })
            else:
                result.update({
                    'status': 'http_error',
                    'error': f'HTTP {response.status_code}'
                })

        except requests.exceptions.ProxyError as e:
            error_str = str(e)
            if '402' in error_str:
                result.update({
                    'status': 'payment_required',
                    'error': '402 Payment Required - 账户余额不足或订阅过期'
                })
            elif '407' in error_str:
                result.update({
                    'status': 'auth_required',
                    'error': '407 Proxy Authentication Required - 认证失败'
                })
            else:
                result.update({
                    'status': 'proxy_error',
                    'error': f'代理错误: {error_str[:100]}'
                })
        except requests.exceptions.ConnectTimeout:
            result.update({
                'status': 'timeout',
                'error': '连接超时'
            })
        except requests.exceptions.ConnectionError as e:
            result.update({
                'status': 'connection_error',
                'error': f'连接错误: {str(e)[:100]}'
            })
        except Exception as e:
            result.update({
                'status': 'unknown_error',
                'error': f'未知错误: {str(e)[:100]}'
            })

        return result

    def test_multiple_proxies(self, count: int = 10) -> Dict:
        """测试多个代理"""
        if not self.proxies:
            print("❌ 没有可用的代理进行测试")
            return {}

        test_count = min(count, len(self.proxies))
        print(f"\\n🧪 开始测试 {test_count} 个代理...")
        print("=" * 60)

        # 随机选择代理进行测试
        test_proxies = random.sample(self.proxies, test_count)

        results = {
            'success': 0,
            'payment_required': 0,
            'auth_required': 0,
            'timeout': 0,
            'connection_error': 0,
            'proxy_error': 0,
            'http_error': 0,
            'unknown_error': 0,
            'details': []
        }

        for i, proxy_info in enumerate(test_proxies, 1):
            print(f"🔍 测试 {i}/{test_count}: {proxy_info['host']}:{proxy_info['port']}")

            result = self.test_single_proxy(proxy_info)
            results['details'].append(result)
            results[result['status']] += 1

            # 显示结果
            if result['status'] == 'success':
                print(f"   ✅ 成功 - IP: {result['ip']} - 响应时间: {result['response_time']}s")
            elif result['status'] == 'payment_required':
                print(f"   💳 {result['error']}")
            elif result['status'] == 'auth_required':
                print(f"   🔐 {result['error']}")
            elif result['status'] == 'timeout':
                print(f"   ⏰ {result['error']}")
            else:
                print(f"   ❌ {result['error']}")

            # 避免请求过于频繁
            if i < test_count:
                time.sleep(1)

        return results

    def generate_report(self, results: Dict):
        """生成测试报告"""
        if not results:
            return

        total = sum(results[key] for key in results if key != 'details')

        print("\\n" + "=" * 60)
        print("📊 代理测试报告")
        print("=" * 60)

        print(f"📈 测试统计:")
        print(f"   总测试数: {total}")
        print(f"   ✅ 成功: {results['success']} ({results['success']/total*100:.1f}%)")
        print(f"   💳 余额不足: {results['payment_required']} ({results['payment_required']/total*100:.1f}%)")
        print(f"   🔐 认证失败: {results['auth_required']} ({results['auth_required']/total*100:.1f}%)")
        print(f"   ⏰ 超时: {results['timeout']} ({results['timeout']/total*100:.1f}%)")
        print(f"   🔌 连接错误: {results['connection_error']} ({results['connection_error']/total*100:.1f}%)")
        print(f"   ❌ 其他错误: {results['proxy_error'] + results['http_error'] + results['unknown_error']} ({(results['proxy_error'] + results['http_error'] + results['unknown_error'])/total*100:.1f}%)")

        print(f"\\n💡 问题诊断:")
        if results['payment_required'] > 0:
            print("   🚨 主要问题: 账户余额不足或订阅过期")
            print("   🔗 解决方案: https://proxy.webshare.io/dashboard")
            print("   💳 需要充值账户或续费订阅")
        elif results['auth_required'] > 0:
            print("   🚨 主要问题: 代理认证失败")
            print("   🔧 检查用户名和密码是否正确")
        elif results['success'] == 0:
            print("   🚨 主要问题: 所有代理都无法使用")
            print("   🔧 建议暂时使用直连模式")
        elif results['success'] > 0:
            print(f"   ✅ 有 {results['success']} 个代理可正常使用")
            print("   🔧 可以继续使用代理模式")

# 运行测试
print("🚀 Webshare代理测试工具 - Google Colab版")
print("=" * 60)

tester = ProxyTester()

if tester.proxies:
    results = tester.test_multiple_proxies(count=10)
    tester.generate_report(results)
    print("\\n🎯 测试完成！")
else:
    print("\\n❌ 没有找到有效的代理配置")
    print("💡 请确保 webshare_proxy.txt 文件已正确上传")
'''

# 保存并运行脚本
with open('proxy_test_colab.py', 'w', encoding='utf-8') as f:
    f.write(proxy_test_code)

print("✅ 代理测试脚本已创建")

# 运行测试
exec(open('proxy_test_colab.py').read())
```

## 🔍 测试结果解读

### 常见错误类型

1. **💳 402 Payment Required**
   - 问题：账户余额不足或订阅过期
   - 解决：访问 https://proxy.webshare.io/dashboard 充值

2. **🔐 407 Proxy Authentication Required**
   - 问题：代理认证失败
   - 解决：检查用户名密码是否正确

3. **⏰ 连接超时**
   - 问题：网络连接问题
   - 解决：检查网络环境或更换代理

4. **✅ 成功**
   - 代理正常工作
   - 可以继续使用

### 📊 报告分析

- **成功率 > 80%**: 代理状态良好，可正常使用
- **余额不足 > 50%**: 需要立即充值账户
- **成功率 = 0%**: 建议暂时使用直连模式

## 🛠️ 后续操作

根据测试结果：

1. **如果代理正常**: 继续使用YouTube爬虫
2. **如果余额不足**: 充值Webshare账户
3. **如果全部失败**: 使用直连模式或更换代理服务商

## 📞 技术支持

如果遇到问题，请提供：
- 测试报告截图
- 代理文件格式示例
- 错误信息详情