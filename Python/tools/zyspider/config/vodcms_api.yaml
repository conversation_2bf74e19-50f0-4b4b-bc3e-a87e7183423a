# VodCMS API配置
# 修改记录：
# - 2025-01-19: 更新API配置，准备测试数据库到API的发送功能
# - 2025-01-19: 根据实际VodCMS API控制器调整API地址和验证方式
# - 2025-01-19: 修正API路径格式为正确的VodCMS格式
# - 2025-01-19: 添加本地VodCMS配置选项
api:
  # 🔧 方案1：本地VodCMS (推荐用于测试)
  base_url: "http://vod123.com"              # 本地VodCMS地址
  # 🔧 方案2：远程VodCMS (生产环境)
  # base_url: "http://your-domain.com"             # 您的VodCMS域名 - 请修改为实际域名

  api_password: "xoxo123"                         # API密码(明文，非MD5) - 请修改为实际密码
  api_path: "/admin/index.php?u=api-index"  # VodCMS API路径
  timeout: 30

# 🔧 使用前请修改：
# 1. base_url: 选择本地或远程VodCMS地址
# 2. api_password: 修改为您的VodCMS API密码(从api_control.class.php第16行获取)
#
# 注意：
# - 本地测试建议使用 http://localhost/vodcms 或 http://127.0.0.1/vodcms
# - 确保VodCMS已启动并可访问
# - API密码在 /Website/vodcms/admin/control/api_control.class.php 第16行定义

# VodCMS API实际参数说明（根据api_control.class.php）
api_params:
  pw: "API密码明文"                      # 必填，通过GET参数传递
  cid: "分类ID"                         # 必填，根据cid.txt映射
  title: "影片标题"                      # 必填
  subtitle: "副标题"                     # 可选
  year: "年份"                          # 推荐填写
  daoyan: "导演"                        # 可选，多个用/或,分隔
  yanyuan: "演员"                       # 可选，多个用/或,分隔
  language: "语言"                      # 可选
  dbid: "豆瓣ID"                        # 可选
  alias: "别名"                         # 可选，最多50字符
  flag: "属性标记"                       # 可选，如hot,rec等，逗号分隔
  content: "剧情简介"                    # 必填，最少5字符
  intro: "简介"                         # 可选
  media: "播放地址"                      # 格式：第1集$url##第2集$url
  olmedia: "在线播放组"                  # 格式：资源播放器名##第1集$url\n第2集$url#tw#
  clouddisk: "云盘地址"                 # 可选
  pic: "封面图片URL"                    # 推荐填写
  tags: "标签"                          # 可选，逗号分隔，最多5个
  isremote: "是否下载远程图片"            # 1=是 0=否
  dateline: "时间戳"                    # 自动生成

batch_config:
  create_batch_size: 20                # VodCMS API较慢，减少批量大小
  update_batch_size: 50
  concurrent_requests: 3               # 降低并发，避免VodCMS服务器压力
  request_delay: 1.0                   # 每次请求间隔1秒

retry_config:
  max_retries: 3
  backoff_factor: 2
  retry_delay: 1

# VodCMS分类映射（从cid.txt获取）
category_mapping:
  动漫: 4
  短剧: 5
  动作片: 7
  科幻片: 8
  爱情片: 9
  恐怖片: 10
  剧情片: 11
  战争片: 12
  喜剧片: 13
  纪录片: 14
  国产剧: 15
  欧美剧: 16
  香港剧: 17
  台湾剧: 18
  韩剧: 19
  日剧: 20
  马泰剧: 21
  预告片: 22

# VodCMS播放器名称映射
player_mapping:
  lz: "lzvod"      # 量子 -> lzvod
  ff: "ffvod"      # 非凡 -> ffvod
  jy: "jyvod"      # 金鹰 -> jyvod
  wj: "wjvod"      # 无尽 -> wjvod
  nn: "nnvod"      # 牛牛 -> nnvod
  bf: "bfvod"      # 暴风 -> bfvod
