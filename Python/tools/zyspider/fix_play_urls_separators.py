#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复播放链接分隔符脚本
功能：将数据库中play_urls字段的单个#替换为##，但不影响已存在的##
方法：三步替换法
1. ## -> @@ (临时标记)
2. # -> ## (替换单个#)
3. @@ -> ## (恢复原来的##)

作者：AI Assistant
创建时间：2025-01-19
修改记录：
- 2025-01-19: 创建三步替换法脚本，安全处理播放链接分隔符
"""

import sqlite3
import logging
import re
from typing import Dict, List

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/fix_separators.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PlayUrlsSeparatorFixer:
    """播放链接分隔符修复器"""

    def __init__(self, db_path: str = 'data/spider.db'):
        self.db_path = db_path
        self.temp_marker = '@@'  # 临时标记

    def analyze_current_separators(self) -> Dict[str, int]:
        """分析当前分隔符使用情况"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT play_urls FROM play_sources WHERE play_urls IS NOT NULL AND play_urls != ""')
            rows = cursor.fetchall()

            stats = {
                'total_records': len(rows),
                'has_single_hash': 0,
                'has_double_hash': 0,
                'has_both': 0,
                'examples': []
            }

            for i, (play_urls,) in enumerate(rows):
                has_double = '##' in play_urls
                # 检查是否有单独的#（不是##的一部分）
                temp_without_double = play_urls.replace('##', '@@')  # 临时替换##
                has_single = '#' in temp_without_double  # 检查是否还有单独的#

                if has_single and has_double:
                    stats['has_both'] += 1
                elif has_single:
                    stats['has_single_hash'] += 1
                elif has_double:
                    stats['has_double_hash'] += 1

                # 收集前10个示例
                if i < 10:
                    stats['examples'].append({
                        'play_urls': play_urls[:100] + '...' if len(play_urls) > 100 else play_urls,
                        'has_single': has_single,
                        'has_double': has_double
                    })

            conn.close()
            return stats

        except Exception as e:
            logger.error(f"分析分隔符失败: {e}")
            return {}

    def preview_changes(self, limit: int = 5) -> List[Dict]:
        """预览将要进行的更改"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查找包含单个#的记录
            cursor.execute('''
                SELECT id, site_name, player_name, play_urls
                FROM play_sources
                WHERE play_urls LIKE '%#%'
                AND play_urls IS NOT NULL
                AND play_urls != ""
                LIMIT ?
            ''', (limit,))

            rows = cursor.fetchall()
            previews = []

            for row_id, site_name, player_name, play_urls in rows:
                original = play_urls

                # 模拟三步替换
                step1 = original.replace('##', self.temp_marker)  # ## -> @@
                step2 = step1.replace('#', '##')                  # # -> ##
                step3 = step2.replace(self.temp_marker, '##')     # @@ -> ##

                if original != step3:
                    previews.append({
                        'id': row_id,
                        'site_name': site_name,
                        'player_name': player_name,
                        'original': original[:200] + '...' if len(original) > 200 else original,
                        'modified': step3[:200] + '...' if len(step3) > 200 else step3,
                        'changed': True
                    })
                else:
                    previews.append({
                        'id': row_id,
                        'site_name': site_name,
                        'player_name': player_name,
                        'original': original[:100] + '...' if len(original) > 100 else original,
                        'modified': '无变化',
                        'changed': False
                    })

            conn.close()
            return previews

        except Exception as e:
            logger.error(f"预览更改失败: {e}")
            return []

    def fix_separators(self, dry_run: bool = True) -> Dict[str, int]:
        """修复分隔符 - 三步替换法"""
        stats = {'processed': 0, 'modified': 0, 'errors': 0, 'skipped': 0}

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取所有需要处理的记录
            cursor.execute('''
                SELECT id, play_urls FROM play_sources
                WHERE play_urls IS NOT NULL AND play_urls != ""
            ''')

            rows = cursor.fetchall()
            logger.info(f"找到 {len(rows)} 条播放源记录需要处理")

            if not dry_run:
                conn.execute('BEGIN TRANSACTION')

            for row_id, play_urls in rows:
                try:
                    stats['processed'] += 1
                    original = play_urls

                    # 检查是否需要修改
                    if '#' not in original:
                        stats['skipped'] += 1
                        continue

                    # 三步替换法
                    step1 = original.replace('##', self.temp_marker)  # ## -> @@
                    step2 = step1.replace('#', '##')                  # # -> ##
                    step3 = step2.replace(self.temp_marker, '##')     # @@ -> ##

                    if original != step3:
                        stats['modified'] += 1

                        if not dry_run:
                            cursor.execute('''
                                UPDATE play_sources SET play_urls = ? WHERE id = ?
                            ''', (step3, row_id))

                        # 记录详细的更改日志
                        if stats['modified'] <= 10:  # 只记录前10个详细日志
                            logger.info(f"修改记录 {row_id}:")
                            logger.info(f"  原始: {original[:100]}...")
                            logger.info(f"  修改: {step3[:100]}...")
                    else:
                        stats['skipped'] += 1

                    # 显示进度
                    if stats['processed'] % 1000 == 0:
                        logger.info(f"已处理 {stats['processed']} 条记录...")

                except Exception as e:
                    logger.error(f"处理记录 {row_id} 失败: {e}")
                    stats['errors'] += 1

            if not dry_run:
                conn.commit()
                logger.info("✅ 数据库更改已提交")
            else:
                logger.info("🔍 这是预览模式，数据库未被修改")

            conn.close()

            return stats

        except Exception as e:
            logger.error(f"修复分隔符失败: {e}")
            stats['errors'] += 1
            return stats

    def verify_results(self) -> Dict[str, int]:
        """验证修复结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 统计修复后的情况
            cursor.execute('''
                SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN play_urls LIKE '%#%' AND play_urls NOT LIKE '%##%' THEN 1 ELSE 0 END) as single_hash_only,
                    SUM(CASE WHEN play_urls LIKE '%##%' THEN 1 ELSE 0 END) as has_double_hash
                FROM play_sources
                WHERE play_urls IS NOT NULL AND play_urls != ""
            ''')

            result = cursor.fetchone()
            conn.close()

            return {
                'total_records': result[0],
                'single_hash_only': result[1],
                'has_double_hash': result[2]
            }

        except Exception as e:
            logger.error(f"验证结果失败: {e}")
            return {}

def main():
    """主函数"""
    print("🔧 播放链接分隔符修复工具")
    print("使用三步替换法：## -> @@ -> # -> ## -> @@ -> ##")
    print("-" * 50)

    fixer = PlayUrlsSeparatorFixer()

    # 1. 分析当前情况
    print("📊 分析当前分隔符使用情况...")
    stats = fixer.analyze_current_separators()

    if stats:
        print(f"总记录数: {stats['total_records']:,}")
        print(f"仅有单个#: {stats['has_single_hash']:,}")
        print(f"包含##: {stats['has_double_hash']:,}")
        print(f"同时包含#和##: {stats['has_both']:,}")

        print(f"\n📋 数据示例:")
        for i, example in enumerate(stats['examples'][:5], 1):
            print(f"  {i}. {example['play_urls']}")
            print(f"     单个#: {example['has_single']}, 双个##: {example['has_double']}")

    # 2. 预览更改
    print(f"\n🔍 预览将要进行的更改...")
    previews = fixer.preview_changes(10)

    for i, preview in enumerate(previews[:5], 1):
        if preview['changed']:
            print(f"  {i}. 【{preview['site_name']}】将被修改:")
            print(f"     原始: {preview['original']}")
            print(f"     修改: {preview['modified']}")
        else:
            print(f"  {i}. 【{preview['site_name']}】无需修改")

    # 3. 询问是否执行
    print(f"\n⚠️  准备修复 {len([p for p in previews if p['changed']])} 条记录")

    while True:
        choice = input("\n选择操作:\n1. 预览模式 (不修改数据库)\n2. 正式执行修复\n3. 退出\n请选择 (1-3): ").strip()

        if choice == '1':
            print("\n🔍 执行预览模式...")
            result = fixer.fix_separators(dry_run=True)
            print(f"\n📊 预览结果:")
            print(f"  处理记录: {result['processed']:,}")
            print(f"  将被修改: {result['modified']:,}")
            print(f"  跳过记录: {result['skipped']:,}")
            print(f"  错误记录: {result['errors']:,}")

        elif choice == '2':
            print("\n⚡ 执行正式修复...")
            result = fixer.fix_separators(dry_run=False)
            print(f"\n✅ 修复完成:")
            print(f"  处理记录: {result['processed']:,}")
            print(f"  已修改: {result['modified']:,}")
            print(f"  跳过记录: {result['skipped']:,}")
            print(f"  错误记录: {result['errors']:,}")

            # 验证结果
            print("\n🔍 验证修复结果...")
            verify_stats = fixer.verify_results()
            if verify_stats:
                print(f"  总记录: {verify_stats['total_records']:,}")
                print(f"  仅单个#: {verify_stats['single_hash_only']:,}")
                print(f"  包含##: {verify_stats['has_double_hash']:,}")
            break

        elif choice == '3':
            print("👋 退出程序")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()