#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库升级脚本：为films表添加subtitle字段
功能：
1. 为films表添加subtitle字段
2. 从play_sources表的vod_remarks聚合数据到films表的subtitle字段
3. 确保API发送时有正确的subtitle信息

作者：AI Assistant
创建时间：2025-01-19
修改记录：
- 2025-01-19: 初始创建，修复vod_remarks丢失的问题，添加subtitle字段
"""

import sqlite3
import json
import logging
from typing import Dict, List, Optional, Set
from collections import Counter

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/db_upgrade_subtitle.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SubtitleFieldUpgrader:
    """subtitle字段升级器"""

    def __init__(self, db_path: str = 'data/spider.db'):
        self.db_path = db_path

    def check_subtitle_field_exists(self) -> bool:
        """检查subtitle字段是否已存在"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("PRAGMA table_info(films)")
            columns = cursor.fetchall()

            conn.close()

            for col in columns:
                if col[1] == 'subtitle':
                    return True
            return False
        except Exception as e:
            logger.error(f"检查subtitle字段失败: {e}")
            return False

    def add_subtitle_field(self) -> bool:
        """为films表添加subtitle字段"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 添加subtitle字段
            cursor.execute('ALTER TABLE films ADD COLUMN subtitle TEXT DEFAULT ""')
            conn.commit()
            conn.close()

            logger.info("✅ 成功为films表添加subtitle字段")
            return True
        except Exception as e:
            logger.error(f"添加subtitle字段失败: {e}")
            return False

    def aggregate_vod_remarks_to_subtitle(self) -> Dict[str, int]:
        """从play_sources聚合vod_remarks到films.subtitle"""
        stats = {'updated': 0, 'no_remarks': 0, 'errors': 0}

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 修复SQLite GROUP_CONCAT语法
            query = """
            SELECT
                f.id as film_id,
                f.title,
                GROUP_CONCAT(p.vod_remarks, ' | ') as all_remarks
            FROM films f
            LEFT JOIN play_sources p ON f.id = p.film_id
            WHERE p.vod_remarks IS NOT NULL AND p.vod_remarks != ''
            GROUP BY f.id, f.title
            """

            cursor.execute(query)
            films_with_remarks = cursor.fetchall()

            logger.info(f"找到 {len(films_with_remarks)} 部影片有vod_remarks数据")

            # 更新每部影片的subtitle
            for film_id, title, all_remarks in films_with_remarks:
                try:
                    # 智能处理remarks：选择最有价值的信息
                    subtitle = self._smart_process_remarks(all_remarks)

                    if subtitle:
                        cursor.execute('''
                            UPDATE films SET subtitle = ? WHERE id = ?
                        ''', (subtitle, film_id))
                        stats['updated'] += 1

                        if stats['updated'] % 1000 == 0:
                            logger.info(f"已处理 {stats['updated']} 部影片...")
                    else:
                        stats['no_remarks'] += 1

                except Exception as e:
                    logger.error(f"处理影片 {title} (ID: {film_id}) 失败: {e}")
                    stats['errors'] += 1

            conn.commit()
            conn.close()

            logger.info(f"✅ subtitle聚合完成: 更新 {stats['updated']} 部，无备注 {stats['no_remarks']} 部，错误 {stats['errors']} 部")
            return stats

        except Exception as e:
            logger.error(f"聚合vod_remarks失败: {e}")
            stats['errors'] += 1
            return stats

    def _smart_process_remarks(self, all_remarks: str) -> str:
        """智能处理remarks，选择最有价值的信息"""
        if not all_remarks:
            return ""

        # 分割并清理
        remarks_list = [r.strip() for r in all_remarks.split('|') if r.strip()]

        if not remarks_list:
            return ""

        # 优先级规则：
        # 1. 包含"完结"的优先
        # 2. 包含"更新至"、"全集"的优先
        # 3. 包含清晰度信息(HD、1080P、4K等)的优先
        # 4. 较短的优先（去除冗余信息）

        priority_keywords = [
            ('完结', 10),
            ('全集', 9),
            ('更新至', 8),
            ('第.*集', 7),
            ('HD', 5),
            ('1080P', 5),
            ('4K', 5),
            ('超清', 4),
            ('高清', 3)
        ]

        scored_remarks = []
        for remark in remarks_list:
            score = 0
            for keyword, weight in priority_keywords:
                if keyword in remark:
                    score += weight

            # 长度惩罚（太长的备注通常信息冗余）
            if len(remark) > 20:
                score -= 2

            scored_remarks.append((remark, score))

        # 按分数排序，取最高分的
        scored_remarks.sort(key=lambda x: x[1], reverse=True)

        # 如果最高分的多个，选择最短的
        best_score = scored_remarks[0][1]
        best_remarks = [r for r, s in scored_remarks if s == best_score]

        final_remark = min(best_remarks, key=len)

        return final_remark

    def show_subtitle_examples(self, limit: int = 10):
        """显示subtitle字段的示例数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT title, subtitle, category
                FROM films
                WHERE subtitle IS NOT NULL AND subtitle != ''
                ORDER BY RANDOM()
                LIMIT ?
            ''', (limit,))

            examples = cursor.fetchall()
            conn.close()

            logger.info(f"\n📋 subtitle字段示例数据 (随机{len(examples)}条):")
            for i, (title, subtitle, category) in enumerate(examples, 1):
                logger.info(f"  {i}. 【{category}】{title} -> {subtitle}")

        except Exception as e:
            logger.error(f"显示subtitle示例失败: {e}")

    def get_subtitle_statistics(self) -> Dict[str, int]:
        """获取subtitle字段的统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总数统计
            cursor.execute('SELECT COUNT(*) FROM films')
            total_films = cursor.fetchone()[0]

            # 有subtitle的统计
            cursor.execute('SELECT COUNT(*) FROM films WHERE subtitle IS NOT NULL AND subtitle != ""')
            has_subtitle = cursor.fetchone()[0]

            # 按分类统计
            cursor.execute('''
                SELECT category, COUNT(*) as count
                FROM films
                WHERE subtitle IS NOT NULL AND subtitle != ""
                GROUP BY category
                ORDER BY count DESC
                LIMIT 10
            ''')
            category_stats = cursor.fetchall()

            conn.close()

            stats = {
                'total_films': total_films,
                'has_subtitle': has_subtitle,
                'no_subtitle': total_films - has_subtitle,
                'subtitle_rate': (has_subtitle / total_films * 100) if total_films > 0 else 0,
                'category_stats': category_stats
            }

            logger.info(f"\n📊 subtitle字段统计:")
            logger.info(f"  总影片数: {stats['total_films']:,}")
            logger.info(f"  有subtitle: {stats['has_subtitle']:,} ({stats['subtitle_rate']:.1f}%)")
            logger.info(f"  无subtitle: {stats['no_subtitle']:,}")

            logger.info(f"\n📈 分类subtitle分布 (前10):")
            for category, count in category_stats:
                logger.info(f"  {category}: {count:,}")

            return stats

        except Exception as e:
            logger.error(f"获取subtitle统计失败: {e}")
            return {}

def main():
    """主函数"""
    print("🚀 开始数据库升级：添加subtitle字段...")

    upgrader = SubtitleFieldUpgrader()

    # 1. 检查是否已有subtitle字段
    if upgrader.check_subtitle_field_exists():
        print("✅ subtitle字段已存在，跳过添加步骤")
    else:
        print("🔧 添加subtitle字段...")
        if not upgrader.add_subtitle_field():
            print("❌ 添加subtitle字段失败，退出")
            return

    # 2. 聚合vod_remarks数据
    print("🔄 聚合vod_remarks到subtitle字段...")
    stats = upgrader.aggregate_vod_remarks_to_subtitle()

    # 3. 显示统计信息
    print("📊 获取统计信息...")
    upgrader.get_subtitle_statistics()

    # 4. 显示示例数据
    print("📋 显示示例数据...")
    upgrader.show_subtitle_examples()

    print("\n✅ 数据库升级完成！")
    print("💡 现在可以使用test_api_sync.py测试API发送，subtitle字段将包含正确的备注信息")

if __name__ == "__main__":
    main()