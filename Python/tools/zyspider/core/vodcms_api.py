#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VodCMS API接口模块
负责将处理后的数据推送到VodCMS系统
"""

import asyncio
import httpx
import yaml
import hashlib
from typing import Dict, List, Optional, Any
import logging
from urllib.parse import urlencode
import json

logger = logging.getLogger(__name__)

class VodCMSAPI:
    """VodCMS API客户端"""

    def __init__(self, config_path: str = "config/vodcms_api.yaml"):
        self.config = self._load_config(config_path)
        self.api_config = self.config.get('api', {})
        self.base_url = self.api_config.get('base_url', '')
        self.api_password = self.api_config.get('api_password', '')

        # 分类映射
        self.category_mapping = {
            '电影': 1,
            '电视剧': 2,
            '综艺': 3,
            '动漫': 4,
            '动画': 4,
            '动画片': 4
        }

        # 统计信息
        self.stats = {
            'total_submitted': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            return {}

    def _get_category_id(self, category_name: str) -> int:
        """根据分类名称获取分类ID"""
        return self.category_mapping.get(category_name, 1)  # 默认为电影

    def _get_player_name(self, site_name: str) -> str:
        """根据资源站名称获取播放器名称"""
        player_mapping = {
            'lz': 'lzvod',
            'ff': 'ffvod',
            'jy': 'jyvod',
            'wj': 'wjvod',
            'bf': 'bfvod',
            'nn': 'nnvod'
        }
        return player_mapping.get(site_name, f"{site_name}vod")

    def _merge_play_sources(self, sources: Dict[str, Dict]) -> str:
        """合并多个播放源 - 修复VodCMS的isdata逻辑，避免空播放器bug"""
        if not sources:
            return ""

        # 按集数排序，优先选择集数最多的源
        sorted_sources = sorted(
            sources.items(),
            key=lambda x: x[1].get('latest_episode', 0),
            reverse=True
        )

        merged_data = []

        for site_name, source_data in sorted_sources:
            player_name = self._get_player_name(site_name)
            play_urls = source_data.get('play_urls', {})

            if not play_urls:  # 跳过空播放源
                continue

            # 构建播放地址字符串
            episodes = []
            for episode_name, episode_url in play_urls.items():
                if episode_url.strip():  # 确保URL不为空
                    episodes.append(f"{episode_name}${episode_url}")

            if episodes:  # 只有非空集数时才添加
                episode_str = "##".join(episodes)
                merged_data.append(f"{player_name}${episode_str}")

        return "$$$".join(merged_data) if merged_data else ""

    def _prepare_film_data(self, film: Dict) -> Dict[str, Any]:
        """准备提交给VodCMS的数据"""
        # 合并播放源
        media_data = self._merge_play_sources(film.get('sources', {}))

        # 如果没有有效播放源，跳过
        if not media_data:
            logger.warning(f"影片 {film.get('title')} 没有有效播放源，跳过提交")
            return {}

        # 构建提交数据
        submit_data = {
            'pw': self.api_password,
            'cid': self._get_category_id(film.get('category', '电影')),
            'title': film.get('title', ''),
            'subtitle': '',  # 副标题
            'year': film.get('year', ''),
            'daoyan': film.get('director', ''),
            'yanyuan': film.get('actors', ''),
            'language': '国语',  # 默认语言
            'dbid': '',  # 豆瓣ID
            'alias': ','.join(film.get('aliases', [])),
            'flag': 'hot' if film.get('is_hot', False) else '',
            'content': film.get('enhanced_content', film.get('content', '')),
            'intro': '',  # 简介
            'media': media_data,
            'poster': film.get('poster', ''),
            'area': film.get('area', ''),
        }

        # 移除空值
        return {k: v for k, v in submit_data.items() if v}

    async def submit_film(self, film: Dict) -> bool:
        """提交单部影片到VodCMS"""
        self.stats['total_submitted'] += 1

        # 准备数据
        submit_data = self._prepare_film_data(film)

        if not submit_data:
            self.stats['skipped'] += 1
            return False

        try:
            async with httpx.AsyncClient(timeout=30) as client:
                # 构建API URL
                api_url = f"{self.base_url}/api.php"

                # 发送POST请求
                response = await client.post(
                    api_url,
                    data=submit_data,
                    headers={
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'Python VodCMS API Client'
                    }
                )

                if response.status_code == 200:
                    result = response.text

                    # 解析VodCMS响应
                    if "成功" in result or "success" in result.lower():
                        logger.info(f"成功提交影片: {film.get('title')}")
                        self.stats['successful'] += 1
                        return True
                    else:
                        logger.error(f"VodCMS返回错误: {result}")
                        self.stats['failed'] += 1
                        return False
                else:
                    logger.error(f"HTTP错误 {response.status_code}: {response.text}")
                    self.stats['failed'] += 1
                    return False

        except Exception as e:
            logger.error(f"提交影片 {film.get('title')} 时出错: {e}")
            self.stats['failed'] += 1
            return False

    async def batch_submit(self, film_list: List[Dict], max_concurrent: int = 3) -> Dict[str, int]:
        """批量提交影片"""
        logger.info(f"开始批量提交 {len(film_list)} 部影片到VodCMS")

        # 过滤有效影片
        valid_films = [film for film in film_list if self._prepare_film_data(film)]
        logger.info(f"有效影片数量: {len(valid_films)}")

        # 创建并发任务
        semaphore = asyncio.Semaphore(max_concurrent)

        async def submit_with_semaphore(film):
            async with semaphore:
                success = await self.submit_film(film)
                # 提交间隔，避免过快请求
                await asyncio.sleep(1)
                return success

        # 并发执行
        tasks = [submit_with_semaphore(film) for film in valid_films]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 统计结果
        success_count = sum(1 for result in results if result is True)
        failed_count = sum(1 for result in results if result is False or isinstance(result, Exception))

        logger.info(f"批量提交完成: 成功 {success_count}, 失败 {failed_count}")

        return {
            'total': len(valid_films),
            'successful': success_count,
            'failed': failed_count
        }

    def _print_stats(self):
        """输出统计信息"""
        stats = self.stats
        logger.info("=== VodCMS提交统计 ===")
        logger.info(f"总提交: {stats['total_submitted']}")
        logger.info(f"成功: {stats['successful']}")
        logger.info(f"失败: {stats['failed']}")
        logger.info(f"跳过: {stats['skipped']}")

        if stats['total_submitted'] > 0:
            success_rate = (stats['successful'] / stats['total_submitted']) * 100
            logger.info(f"成功率: {success_rate:.1f}%")

    def validate_api_connection(self) -> bool:
        """验证API连接"""
        if not self.base_url or not self.api_password:
            logger.error("VodCMS API配置不完整")
            return False

        # 这里可以添加实际的连接测试
        logger.info("VodCMS API配置验证通过")
        return True

    def get_test_data(self) -> Dict:
        """获取测试数据"""
        return {
            'film_id': 1001,
            'title': '测试影片',
            'aliases': ['测试别名'],
            'year': '2024',
            'area': '中国大陆',
            'category': '电影',
            'director': '测试导演',
            'actors': '测试演员1,测试演员2',
            'content': '这是一个测试影片的剧情简介，用于验证VodCMS API接口的功能是否正常工作。',
            'enhanced_content': '这是一个经过AI增强的测试影片剧情简介，具有更好的可读性和吸引力。',
            'poster': 'https://example.com/poster.jpg',
            'is_commentary': False,
            'sources': {
                'test': {
                    'latest_episode': 2,
                    'play_urls': {
                        '第1集': 'https://example.com/ep1.m3u8',
                        '第2集': 'https://example.com/ep2.m3u8'
                    },
                    'last_update': '2024-01-01T00:00:00',
                    'vod_remarks': '更新至第2集',
                    'player_name': 'testvod'
                }
            }
        }


# 测试代码
def test_vodcms_api():
    """测试VodCMS API功能"""
    api = VodCMSAPI()

    print("=== VodCMS API测试 ===")

    # 验证配置
    is_valid = api.validate_api_connection()
    print(f"API配置验证: {'通过' if is_valid else '失败'}")

    # 测试数据准备
    test_film = api.get_test_data()
    submit_data = api._prepare_film_data(test_film)

    print(f"测试影片: {test_film['title']}")
    print(f"提交数据字段数: {len(submit_data)}")

    # 测试播放源合并
    media = api._merge_play_sources(test_film['sources'])
    print(f"合并播放源: {media[:100]}..." if len(media) > 100 else f"合并播放源: {media}")

    # 测试分类映射
    for category in ['电影', '电视剧', '动漫', '综艺']:
        cid = api._get_category_id(category)
        print(f"分类映射 {category}: {cid}")


if __name__ == "__main__":
    test_vodcms_api()
