import asyncio
import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import json
import yaml

from core.database import SpiderDatabase
from core.fetcher import ResourceFetcher
from core.matcher import SmartMatcher
from core.ai_enhancer import AIPlotEnhancer
from core.vodcms_api import VodCMSAPI
from core.tag_generator import AITagGenerator
from utils.pinyin_converter import PinyinConverter
from core.dto import RawVodData, CleanVodData

logger = logging.getLogger(__name__)

class SpiderControllerV2:
    """
    爬虫控制器 V2 - 基于SQLite数据库的版本

    新的工作流程：
    1. 采集原始数据 -> 保存到SQLite
    2. 从SQLite读取未处理数据 -> 智能匹配和合并
    3. 影片数据清洗完成 -> 标记为待同步
    4. 从SQLite获取待同步影片 -> 提交到API
    5. API成功响应 -> 标记为已同步
    """

    def __init__(self, config_path: str = "config"):
        """
        初始化控制器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = Path(config_path)

        # 初始化组件
        self.database = SpiderDatabase()
        self.fetcher = ResourceFetcher(str(self.config_path / "sites.yaml"))
        self.matcher = SmartMatcher()
        self.ai_enhancer = AIPlotEnhancer(
            str(self.config_path / "ai_config.yaml"),
            str(self.config_path / "prompts.yaml")
        )
        self.vodcms_api = VodCMSAPI(self.config_path / "vodcms_api.yaml")
        self.tag_generator = AITagGenerator()  # 新增标签生成器

        # 加载aliases配置
        aliases_config = self._load_aliases_config()
        enabled_formats = aliases_config.get('alias_generation', {}).get('enabled_formats', ['full', 'underscored', 'abbreviated'])
        self.pinyin_converter = PinyinConverter(alias_formats=enabled_formats)  # 新增拼音转换器

        # 统计信息
        self.stats = {
            'raw_data_saved': 0,
            'films_processed': 0,
            'films_synced': 0,
            'ai_enhanced': 0,
            'errors': 0
        }

        logger.info("SpiderController V2 初始化完成")

    def _load_aliases_config(self) -> Dict:
        """加载aliases配置文件"""
        aliases_config_path = self.config_path / "aliases_config.yaml"
        try:
            with open(aliases_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"Aliases配置加载成功: {config.get('alias_generation', {}).get('enabled_formats', [])}")
                return config
        except FileNotFoundError:
            logger.warning(f"Aliases配置文件未找到: {aliases_config_path}，使用默认配置")
            return {'alias_generation': {'enabled_formats': ['full', 'underscored', 'abbreviated']}}

    def _clean_html_content(self, content: str) -> str:
        """清理HTML内容，移除HTML标签和网址"""
        if not content:
            return ''

        # 将换行标签替换为空格，保持文本连贯性
        content = re.sub(r'<br\s*/?>', ' ', content, flags=re.IGNORECASE)
        content = re.sub(r'<p\s*/?>', ' ', content, flags=re.IGNORECASE)
        content = re.sub(r'</p>', ' ', content, flags=re.IGNORECASE)

        # 移除其他HTML标签
        content = re.sub(r'<[^>]+>', '', content)

        # 处理HTML实体
        content = re.sub(r'&nbsp;', ' ', content)
        content = re.sub(r'&lt;', '<', content)
        content = re.sub(r'&gt;', '>', content)
        content = re.sub(r'&amp;', '&', content)
        content = re.sub(r'&quot;', '"', content)
        content = re.sub(r'&#\d+;', '', content)  # 移除数字HTML实体

        # 移除网址 (http/https/www)
        content = re.sub(r'https?://[^\s]+', '', content)
        content = re.sub(r'www\.[^\s]+', '', content)

        # 移除邮箱地址
        content = re.sub(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', '', content)

        # 清理多余的空白字符
        content = re.sub(r'\s+', ' ', content)
        content = content.strip()

        return content

    async def run_crawl_pipeline(self, site_names: Optional[List[str]] = None,
                                pages_per_site: int = 10, enable_ai: bool = True) -> Dict:
        """
        运行完整的采集流水线

        Args:
            site_names: 要采集的站点名称列表，None表示所有站点
            pages_per_site: 每个站点采集的页数
            enable_ai: 是否启用AI增强

        Returns:
            执行统计信息
        """
        logger.info("开始执行采集流水线...")
        start_time = datetime.now()

        try:
            # 第一步：采集原始数据并保存到数据库
            await self._crawl_and_save_raw_data(site_names, pages_per_site)

            # 第二步：处理未处理的原始数据
            await self._process_raw_data()

            # 第三步：AI增强（可选）
            if enable_ai:
                await self._ai_enhance_films()

            # 第四步：同步到API
            await self._sync_to_api()

            # 生成统计报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            final_stats = {
                **self.stats,
                'duration_seconds': duration,
                'database_stats': self.database.get_statistics(),
                'execution_time': end_time.isoformat()
            }

            logger.info(f"采集流水线执行完成，耗时 {duration:.2f} 秒")
            logger.info(f"最终统计: {final_stats}")

            return final_stats

        except Exception as e:
            logger.error(f"采集流水线执行失败: {e}")
            self.stats['errors'] += 1
            raise

    async def _crawl_and_save_raw_data(self, site_names: Optional[List[str]],
                                      pages_per_site: int):
        """第一步：采集原始数据并保存到数据库"""
        logger.info("步骤1: 开始采集原始数据...")

                # 获取要采集的站点列表
        if site_names is None:
            site_names = list(self.fetcher.sites_config.get('sites', {}).keys())

        total_saved = 0

        for site_name in site_names:
            try:
                logger.info(f"开始采集站点: {site_name}")

                # 根据页数决定采集模式
                if pages_per_site == -1:
                    # 超级全量模式：采集所有数据
                    raw_films = await self.fetcher.super_full_crawl_mode(site_name)
                    if raw_films:
                        save_stats = self.database.save_raw_data(site_name, raw_films)
                        total_saved += save_stats['new'] + save_stats['updated']
                elif pages_per_site > 1000:
                    # 全量模式：使用分批保存
                    site_saved = 0

                    async def batch_save_callback(site_name: str, batch_data: List[Dict]) -> Dict:
                        """分批保存回调函数"""
                        nonlocal site_saved
                        save_stats = self.database.save_raw_data(site_name, batch_data)
                        site_saved += save_stats['new'] + save_stats['updated']
                        return save_stats

                    # 使用分批保存的全量采集
                    await self.fetcher.full_crawl_mode(
                        site_name,
                        max_pages=None,
                        batch_callback=batch_save_callback,
                        batch_size=50  # 每50页保存一次
                    )
                    total_saved += site_saved

                    logger.info(f"{site_name} 分批采集完成，共保存 {site_saved} 条数据")
                else:
                    # 增量模式：指定页数
                    raw_films = await self.fetcher.incremental_crawl_mode(site_name, pages_per_site)
                    if raw_films:
                        save_stats = self.database.save_raw_data(site_name, raw_films)
                        total_saved += save_stats['new'] + save_stats['updated']

                # 记录采集结果（非分批模式）
                if pages_per_site != -1 and pages_per_site <= 1000:
                    if raw_films:
                        logger.info(f"{site_name} 采集完成: "
                                  f"原始数据 {len(raw_films)} 条, "
                                  f"新增 {save_stats['new']} 条, "
                                  f"更新 {save_stats['updated']} 条")
                    else:
                        logger.warning(f"{site_name} 未获取到数据")

            except Exception as e:
                logger.error(f"{site_name} 采集失败: {e}")
                self.stats['errors'] += 1

        self.stats['raw_data_saved'] = total_saved
        logger.info(f"原始数据采集完成，共保存 {total_saved} 条记录")

    async def _process_raw_data(self):
        """第二步：处理未处理的原始数据"""
        logger.info("步骤2: 开始处理原始数据...")

        # 获取未处理的原始数据
        unprocessed_data = self.database.get_unprocessed_raw_data()
        logger.info(f"发现 {len(unprocessed_data)} 条未处理数据")

        processed_count = 0

        for raw_data in unprocessed_data:
            try:
                # 转换为标准格式
                raw_vod = RawVodData(**raw_data)

                # 标准化数据
                clean_data = self._convert_to_clean_data(raw_vod)

                # 智能匹配现有影片 - 增强版，支持多维度匹配
                existing_film = self.database.find_existing_film(
                    clean_data.title,
                    clean_data.year,
                    clean_data.area,
                    clean_data.director,
                    clean_data.actors,
                    clean_data.plot
                )

                if existing_film:
                    # 合并到现有影片
                    merged_film = self._merge_film_data(existing_film, clean_data, raw_data['_site_name'])
                else:
                    # 创建新影片（异步调用）
                    merged_film = await self._create_new_film(clean_data, raw_data['_site_name'])

                # 保存影片数据
                film_id = self.database.save_film(merged_film)

                # 标记原始数据为已处理
                self.database.mark_raw_data_processed(raw_data['_raw_id'], film_id)

                processed_count += 1

            except Exception as e:
                logger.error(f"处理原始数据失败: {e}, 数据: {raw_data}")
                self.stats['errors'] += 1

        self.stats['films_processed'] = processed_count
        logger.info(f"原始数据处理完成，共处理 {processed_count} 条")

    def _convert_to_clean_data(self, raw_vod: RawVodData) -> CleanVodData:
        """将原始数据转换为清洗后的数据"""
        # 解析播放地址
        play_urls = self.matcher._parse_play_urls(raw_vod.vod_play_url or '')
        latest_episode = self.matcher._get_latest_episode(play_urls)

        # 标准化标题
        normalized_title = self.matcher.normalize_title(raw_vod.vod_name)

        # 检测是否为解说类
        is_commentary = self.matcher.is_commentary(raw_vod.vod_name)

        # 分类映射
        category_mapping = {
            '电影': 1, '连续剧': 2, '综艺': 3, '动漫': 4,
            '电视剧': 2, '动作片': 1, '喜剧片': 1, '爱情片': 1,
            '科幻片': 1, '恐怖片': 1, '剧情片': 1, '战争片': 1,
            '国产剧': 2, '港台剧': 2, '日韩剧': 2, '欧美剧': 2
        }

        category_id = category_mapping.get(raw_vod.type_name, 1)

        # 清理简介内容
        clean_plot = self._clean_html_content(raw_vod.vod_content or '')

        return CleanVodData(
            title=raw_vod.vod_name,
            title_std=normalized_title,
            year=raw_vod.vod_year or '',
            category=raw_vod.type_name or '电影',
            category_id=category_id,
            area=raw_vod.vod_area,
            lang=raw_vod.vod_lang,
            director=raw_vod.vod_director,
            actors=raw_vod.vod_actor,
            plot=clean_plot,
            pic_url=raw_vod.vod_pic,
            is_commentary=is_commentary,
            latest_episode=latest_episode,
            play_urls={'play_urls': play_urls}
        )

    def _merge_film_data(self, existing_film: Dict, new_data: CleanVodData,
                        site_name: str) -> Dict:
        """合并影片数据"""
        # 基础信息以现有为准，播放源进行合并
        merged = existing_film.copy()

        # 更新最新集数
        if new_data.latest_episode > merged.get('latest_episode', 0):
            merged['latest_episode'] = new_data.latest_episode

        # 合并播放源
        if 'play_sources' not in merged:
            merged['play_sources'] = {}

        # 获取配置文件中的播放器名称
        player_name = self.fetcher.sites_config.get('sites', {}).get(site_name, {}).get('player_name', '默认播放器')

        merged['play_sources'][site_name] = {
            'player_name': player_name,
            'play_urls': new_data.play_urls.get('play_urls', {}),
            'latest_episode': new_data.latest_episode
        }

        # 更新资源站列表
        source_sites = json.loads(merged.get('source_sites', '[]'))
        if site_name not in source_sites:
            source_sites.append(site_name)
        merged['source_sites'] = source_sites

        return merged

    async def _create_new_film(self, clean_data: CleanVodData, site_name: str) -> Dict:
        """创建新影片数据（不包含AI处理，AI处理留到后续步骤）"""
        return {
            'title': clean_data.title,
            'title_normalized': clean_data.title_std,
            'year': clean_data.year,
            'category': clean_data.category,
            'category_id': clean_data.category_id,
            'area': clean_data.area,
            'lang': clean_data.lang,
            'director': clean_data.director,
            'actors': clean_data.actors,
            'plot': clean_data.plot,
            'pic_url': clean_data.pic_url,
            'is_commentary': clean_data.is_commentary,
            'latest_episode': clean_data.latest_episode,
            'tags': '',  # 空标签，后续AI处理时填充
            'aliases': '',  # 空别名，后续AI处理时填充
            'source_sites': [site_name],
            'play_sources': {
                site_name: {
                    'player_name': self.fetcher.sites_config.get('sites', {}).get(site_name, {}).get('player_name', '默认播放器'),
                    'play_urls': clean_data.play_urls.get('play_urls', {}),
                    'latest_episode': clean_data.latest_episode
                }
            }
        }

    async def _ai_enhance_films(self):
        """第三步：AI增强影片数据（标签生成、别名生成、剧情增强）"""
        logger.info("步骤3: 开始AI增强...")

        # 获取需要AI增强的影片
        pending_films = self.database.get_pending_sync_films()

        # 需要标签生成的影片（tags为空）
        tag_candidates = [
            film for film in pending_films
            if not film.get('tags') or film.get('tags', '').strip() == ''
        ]

        # 需要别名生成的影片（aliases为空）
        alias_candidates = [
            film for film in pending_films
            if not film.get('aliases') or film.get('aliases', '').strip() == ''
        ]

        # 需要剧情增强的影片（ai_plot为空且原始剧情较短）
        plot_candidates = [
            film for film in pending_films
            if not film.get('ai_plot') and len(film.get('plot', '')) < 100
        ]

        logger.info(f"发现需要处理的影片: 标签生成 {len(tag_candidates)} 部, 别名生成 {len(alias_candidates)} 部, 剧情增强 {len(plot_candidates)} 部")

        enhanced_count = 0

        # 处理所有需要AI增强的影片（取并集）
        all_candidates = list(set(tag_candidates + alias_candidates + plot_candidates))

        for film in all_candidates[:50]:  # 每次处理50部
            try:
                film_updated = False

                # 1. 生成标签（如果需要）
                if film in tag_candidates:
                    film_data_for_tags = {
                        'name': film['title'],
                        'type': film.get('category', ''),
                        'area': film.get('area', ''),
                        'year': film.get('year', ''),
                        'des': self._clean_html_content(film.get('plot', ''))
                    }

                    try:
                        tags_list = await self.tag_generator.generate_tags(film_data_for_tags)
                        tags = ', '.join(tags_list) if tags_list else ''
                        if tags:
                            film['tags'] = tags
                            film_updated = True
                            logger.info(f"为 {film['title']} 生成标签: {tags}")
                    except Exception as e:
                        logger.error(f"标签生成失败 {film['title']}: {e}")

                # 2. 生成拼音别名（如果需要）
                if film in alias_candidates:
                    try:
                        pinyin_aliases = self.pinyin_converter.convert_to_pinyin(film['title'])
                        aliases = ', '.join(pinyin_aliases) if pinyin_aliases else ''
                        if aliases:
                            film['aliases'] = aliases
                            film_updated = True
                            logger.info(f"为 {film['title']} 生成拼音别名: {aliases}")
                    except Exception as e:
                        logger.error(f"拼音转换失败 {film['title']}: {e}")

                # 3. 生成AI剧情（如果需要）
                if film in plot_candidates:
                    film_data = {
                        'title': film['title'],
                        'year': film.get('year', ''),
                        'area': film.get('area', ''),
                        'category': film.get('category', '电影'),
                        'director': film.get('director', ''),
                        'actors': film.get('actors', ''),
                        'content': film.get('plot', '')
                    }

                    try:
                        ai_plot = await self.ai_enhancer.enhance_plot(film_data)
                        if ai_plot and ai_plot != film.get('plot', ''):
                            film['ai_plot'] = ai_plot
                            film_updated = True
                            logger.info(f"AI剧情增强完成: {film['title']}")
                    except Exception as e:
                        logger.error(f"AI剧情增强失败 {film['title']}: {e}")

                # 保存更新
                if film_updated:
                    self.database.save_film(film)
                    enhanced_count += 1

            except Exception as e:
                logger.error(f"AI增强失败: {film['title']}, 错误: {e}")
                self.stats['errors'] += 1

        self.stats['ai_enhanced'] = enhanced_count
        logger.info(f"AI增强完成，共增强 {enhanced_count} 部影片")

    async def _sync_to_api(self):
        """第四步：同步到API"""
        logger.info("步骤4: 开始同步到API...")

        # 获取待同步的影片
        pending_films = self.database.get_pending_sync_films(limit=50)  # 每批处理50部
        logger.info(f"发现 {len(pending_films)} 部影片待同步")

        synced_count = 0

        for film in pending_films:
            try:
                # 转换为API格式
                api_data = self._convert_to_api_format(film)

                # 提交到API - submit_film返回布尔值
                success = await self.vodcms_api.submit_film(api_data)

                if success:
                    # 标记为已同步
                    self.database.mark_film_synced(
                        film['id'], 'create', True,
                        'API提交成功', ''
                    )
                    synced_count += 1
                    logger.info(f"同步成功: {film['title']}")
                else:
                    # 标记同步失败
                    self.database.mark_film_synced(
                        film['id'], 'create', False,
                        '', 'API提交失败'
                    )
                    logger.error(f"同步失败: {film['title']}")

            except Exception as e:
                logger.error(f"同步异常: {film['title']}, 错误: {e}")
                self.database.mark_film_synced(
                    film['id'], 'create', False, '', str(e)
                )
                self.stats['errors'] += 1

        self.stats['films_synced'] = synced_count
        logger.info(f"API同步完成，成功同步 {synced_count} 部影片")

    def _convert_to_api_format(self, film: Dict) -> Dict:
        """转换为API格式"""
        # 合并播放源
        merged_play_data = {}
        for site_name, source_data in film.get('play_sources', {}).items():
            for player_name, urls in source_data.get('play_urls', {}).items():
                if isinstance(urls, list) and urls:
                    merged_play_data[f"{site_name}_{player_name}"] = urls

        # 构建播放地址字符串
        play_url_parts = []
        for player_name, episodes in merged_play_data.items():
            episode_strs = [f"第{i+1}集${url}" for i, url in enumerate(episodes)]
            play_url_parts.append(f"{player_name}${'##'.join(episode_strs)}")

        vod_play_url = "$$$".join(play_url_parts)

        # 格式化标签 - tags字段已经是逗号分隔的字符串
        tags_str = film.get('tags', '')
        if tags_str:
            # 如果tags已经是字符串，直接使用
            vod_tag = tags_str
        else:
            # 兼容旧格式（列表）
            tags_list = film.get('tags', [])
            vod_tag = ','.join(tags_list[:5]) if isinstance(tags_list, list) and tags_list else ''

        return {
            'vod_name': film['title'],
            'vod_year': film.get('year', ''),
            'vod_area': film.get('area', ''),
            'vod_lang': film.get('lang', ''),
            'vod_director': film.get('director', ''),
            'vod_actor': film.get('actors', ''),
            'vod_content': film.get('ai_plot') or film.get('plot', ''),
            'vod_pic': film.get('pic_url', ''),
            'vod_remarks': f"更新至第{film.get('latest_episode', 1)}集",
            'vod_play_url': vod_play_url,
            'vod_tag': vod_tag,  # 添加标签字段
            'type_id': film.get('category_id', 1),
            'vod_class': film.get('category', '电影')
        }

    async def run_incremental_update(self, pages_per_site: int = 5) -> Dict:
        """运行增量更新"""
        logger.info("开始增量更新...")
        return await self.run_crawl_pipeline(
            site_names=None,
            pages_per_site=pages_per_site,
            enable_ai=False
        )

    async def run_sync_only(self) -> Dict:
        """仅运行同步到API的步骤"""
        logger.info("开始仅同步模式...")
        await self._sync_to_api()
        return {
            'films_synced': self.stats['films_synced'],
            'errors': self.stats['errors'],
            'database_stats': self.database.get_statistics()
        }

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return {
            **self.stats,
            'database_stats': self.database.get_statistics()
        }

    def export_pending_films(self, output_file: Optional[str] = None) -> str:
        """导出待同步影片"""
        return self.database.export_films_to_json(status=0, output_file=output_file)

    async def run_super_full_crawl(self, site_names: Optional[List[str]] = None,
                                  enable_ai: bool = False) -> Dict:
        """
        运行超级全量采集：自动获取所有数据

        Args:
            site_names: 要采集的站点名称列表，None表示所有站点
            enable_ai: 是否启用AI增强（全量时建议关闭节省成本）

        Returns:
            执行统计信息
        """
        logger.info("🚀 开始超级全量采集模式...")
        logger.warning("注意：此模式会采集所有可用数据，可能需要数小时完成！")

        start_time = datetime.now()

        try:
            # 使用特殊值 -1 表示超级全量模式
            await self._crawl_and_save_raw_data(site_names, pages_per_site=-1)

            # 处理数据
            await self._process_raw_data()

            # AI增强（可选，全量时建议关闭）
            if enable_ai:
                logger.warning("全量模式启用AI增强，可能产生大量API费用！")
                await self._ai_enhance_films()

            # 同步到API
            await self._sync_to_api()

            # 生成统计报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            final_stats = {
                **self.stats,
                'mode': 'super_full_crawl',
                'duration_seconds': duration,
                'duration_hours': duration / 3600,
                'database_stats': self.database.get_statistics(),
                'execution_time': end_time.isoformat()
            }

            logger.info(f"🎉 超级全量采集完成！")
            logger.info(f"总耗时: {duration/3600:.2f} 小时")
            logger.info(f"最终统计: {final_stats}")

            return final_stats

        except Exception as e:
            logger.error(f"超级全量采集失败: {e}")
            self.stats['errors'] += 1
            raise

    async def process_single_raw_data(self, raw_id: int, site_name: str, raw_data_json: str) -> Dict:
        """
        处理单条原始数据

        Args:
            raw_id: 原始数据ID
            site_name: 站点名称
            raw_data_json: 原始数据JSON字符串

        Returns:
            处理结果字典，包含success状态和相关信息
        """
        try:
            # 解析JSON数据
            raw_data = json.loads(raw_data_json)
            raw_data['_raw_id'] = raw_id
            raw_data['_site_name'] = site_name

            # 转换为标准格式
            raw_vod = RawVodData(**raw_data)

            # 标准化数据
            clean_data = self._convert_to_clean_data(raw_vod)

            # 智能匹配现有影片 - 增强版，支持多维度匹配
            existing_film = self.database.find_existing_film(
                clean_data.title,
                clean_data.year,
                clean_data.area,
                clean_data.director,
                clean_data.actors,
                clean_data.plot
            )

            if existing_film:
                # 合并到现有影片
                merged_film = self._merge_film_data(existing_film, clean_data, site_name)
                operation = "merged"
            else:
                # 创建新影片（异步调用，包含AI标签生成）
                merged_film = await self._create_new_film(clean_data, site_name)
                operation = "created"

            # 保存影片数据
            film_id = self.database.save_film(merged_film)

            return {
                'success': True,
                'operation': operation,
                'film_id': film_id,
                'title': clean_data.title,
                'site_name': site_name,
                'tags': merged_film.get('tags', ''),
                'aliases': merged_film.get('aliases', '')
            }

        except Exception as e:
            logger.error(f"处理单条原始数据失败 (ID:{raw_id}): {e}")
            return {
                'success': False,
                'error': str(e),
                'raw_id': raw_id,
                'site_name': site_name
            }

    async def run_auto_full_crawl(self, site_names: Optional[List[str]] = None,
                                 enable_ai: bool = True) -> Dict:
        """
        运行自动全量采集：自动获取总页数并采集

        Args:
            site_names: 要采集的站点名称列表
            enable_ai: 是否启用AI增强

        Returns:
            执行统计信息
        """
        logger.info("🎯 开始自动全量采集模式...")
        start_time = datetime.now()

        try:
            # 使用大数值触发全量模式
            await self._crawl_and_save_raw_data(site_names, pages_per_site=99999)

            # 处理数据
            await self._process_raw_data()

            # AI增强
            if enable_ai:
                await self._ai_enhance_films()

            # 同步到API
            await self._sync_to_api()

            # 生成统计报告
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            final_stats = {
                **self.stats,
                'mode': 'auto_full_crawl',
                'duration_seconds': duration,
                'duration_hours': duration / 3600,
                'database_stats': self.database.get_statistics(),
                'execution_time': end_time.isoformat()
            }

            logger.info(f"🎉 自动全量采集完成！")
            logger.info(f"总耗时: {duration/3600:.2f} 小时")

            return final_stats

        except Exception as e:
            logger.error(f"自动全量采集失败: {e}")
            self.stats['errors'] += 1
            raise

    async def get_site_info(self, site_name: str) -> Dict:
        """获取站点信息，包括总页数等"""
        try:
            total_pages = await self.fetcher.get_total_pages(site_name)
            first_page = await self.fetcher.fetch_page(site_name, 1)

            info = {
                'site_name': site_name,
                'total_pages': total_pages,
                'status': 'online' if first_page else 'offline'
            }

            if first_page and 'page_info' in first_page:
                page_info = first_page['page_info']
                info.update({
                    'total_records': page_info.get('total_records', 0),
                    'page_size': page_info.get('page_size', 20),
                    'estimated_videos': page_info.get('total_records', 0)
                })

            return info
        except Exception as e:
            logger.error(f"获取站点 {site_name} 信息失败: {e}")
            return {
                'site_name': site_name,
                'status': 'error',
                'error': str(e)
            }

    async def get_all_sites_info(self) -> Dict[str, Dict]:
        """获取所有站点信息"""
        site_names = list(self.fetcher.sites_config.get('sites', {}).keys())
        sites_info = {}

        logger.info("正在获取所有站点信息...")

        # 并发获取站点信息
        tasks = [self.get_site_info(site_name) for site_name in site_names]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        for site_name, result in zip(site_names, results):
            if isinstance(result, Exception):
                sites_info[site_name] = {
                    'site_name': site_name,
                    'status': 'error',
                    'error': str(result)
                }
            else:
                sites_info[site_name] = result

        # 汇总统计
        total_estimated = sum(
            info.get('estimated_videos', 0)
            for info in sites_info.values()
            if info.get('status') == 'online'
        )
        total_pages = sum(
            info.get('total_pages', 0)
            for info in sites_info.values()
            if info.get('status') == 'online'
        )

        summary = {
            'total_sites': len(site_names),
            'online_sites': len([s for s in sites_info.values() if s.get('status') == 'online']),
            'total_estimated_videos': total_estimated,
            'total_estimated_pages': total_pages,
            'sites_detail': sites_info
        }

        logger.info(f"站点信息汇总: {summary['online_sites']}/{summary['total_sites']} 站点在线")
        logger.info(f"预估总数据量: {total_estimated:,} 条视频, {total_pages:,} 页")

        return summary