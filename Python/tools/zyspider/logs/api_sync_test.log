2025-06-28 02:16:02,135 - INFO - 加载了 53 个分类映射
2025-06-28 02:16:02,135 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:02,135 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 02:16:17,272 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 02:16:17,340 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 02:16:17,406 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 02:16:17,406 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 02:16:17,406 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[运行警告] : Undefined array key "pw"</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 29 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#25</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$this-&gt;err&nbsp;=&nbsp;1;
</td><tr><td width="40">#26</td><td>
</td><tr><td width="40">#27</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*****自定义密码验证****/
</td><tr><td width="40">#28</td><td>
</td><tr style="background:#faa;"><td width="40">#29</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;($this-&gt;password&nbsp;!=&nbsp;$_GET[&#039;pw&#039;])&nbsp;exit(&#039;验证密码错误&#039;);&nbsp;&nbsp;&nbsp;//安全检测,密码不符则退出
</td><tr><td width="40">#30</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*****是否管理员登陆****/
</td><tr><td width="40">#31</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$this-&gt;check_isadmin();
</td><tr><td width="40">#32</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$this-&gt;check_user_group();
</td><tr><td width="40">#33</td><td>&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#34</td><td>
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(29): debug::error_handler(2, &#039;Undefined array...&#039;, &#039;/Users/<USER>/...&#039;, 29)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(254): api_control-&gt;__construct()</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#5 {main}</li></ul>

	<h1>SQL</h1>
	<ul></ul>

	<h1>$_GET</h1>
	<ul><li>#control => api</li><li class="even">#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 02:16:17</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 0.0158</li>
		<li><span>内存开销:</span> 206.32 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 02:16:39,401 - INFO - 加载了 53 个分类映射
2025-06-28 02:16:39,401 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:39,401 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 02:16:43,546 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 02:16:43,625 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 02:16:43,640 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?control=api&action=index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 02:16:43,640 - INFO - API响应: <html><body><script>top.location="index.php?u=index-login"</script></body></html>...
2025-06-28 02:16:43,640 - WARNING - ⚠️ 未知响应: <html><body><script>top.location="index.php?u=index-login"</script></body></html>
2025-06-28 02:16:55,118 - INFO - 加载了 53 个分类映射
2025-06-28 02:16:55,118 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:16:55,118 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 02:17:02,296 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 02:17:02,358 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 02:17:02,377 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?control=api&action=index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 02:17:02,377 - INFO - API响应: <html><body><script>top.location="index.php?u=index-login"</script></body></html>...
2025-06-28 02:17:02,377 - WARNING - ⚠️ 未知响应: <html><body><script>top.location="index.php?u=index-login"</script></body></html>
2025-06-28 02:21:22,082 - INFO - 加载了 53 个分类映射
2025-06-28 02:21:22,082 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:21:22,082 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 02:21:32,701 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 02:21:32,758 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 02:21:32,822 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 02:21:32,822 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 02:21:32,822 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 87 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#83</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$uid&nbsp;=&nbsp;$this-&gt;_user[&#039;uid&#039;];
</td><tr><td width="40">#84</td><td>
</td><tr><td width="40">#85</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$cid&nbsp;=&nbsp;intval(R(&#039;cid&#039;,&nbsp;&#039;P&#039;));
</td><tr><td width="40">#86</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$title&nbsp;=&nbsp;trim(strip_tags(R(&#039;title&#039;,&nbsp;&#039;P&#039;)));
</td><tr style="background:#faa;"><td width="40">#87</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$subtitle&nbsp;=&nbsp;trim(strip_tags(R(&#039;subtitle&#039;,&nbsp;&#039;P&#039;)));&nbsp;//添加副标题
</td><tr><td width="40">#88</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$year&nbsp;=&nbsp;trim(strip_tags(R(&#039;year&#039;,&nbsp;&#039;P&#039;)));&nbsp;//添加年份
</td><tr><td width="40">#89</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$daoyan&nbsp;=&nbsp;$this-&gt;filterstr(trim(strip_tags(R(&#039;daoyan&#039;,&nbsp;&#039;P&#039;))));&nbsp;//添加导演
</td><tr><td width="40">#90</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$yanyuan&nbsp;=&nbsp;$this-&gt;filterstr(trim(strip_tags(R(&#039;yanyuan&#039;,&nbsp;&#039;P&#039;))));&nbsp;//添加演员
</td><tr><td width="40">#91</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$language&nbsp;=&nbsp;trim(R(&#039;language&#039;,&nbsp;&#039;P&#039;));&nbsp;//添加语言
</td><tr><td width="40">#92</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dbid&nbsp;=&nbsp;trim(strip_tags(R(&#039;dbid&#039;,&nbsp;&#039;P&#039;)));&nbsp;//添加豆瓣id
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;strip_tags(): P...&#039;, &#039;/Users/<USER>/...&#039;, 87)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(87): strip_tags(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0061s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0112s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0025s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 02:21:32</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 0.0563</li>
		<li><span>内存开销:</span> 291.96 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 02:22:06,997 - INFO - 加载了 53 个分类映射
2025-06-28 02:22:06,998 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:22:06,998 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 02:32:38,451 - INFO - 加载了 53 个分类映射
2025-06-28 02:32:38,451 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 02:32:38,451 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 14:32:47,411 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 14:32:47,412 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 14:32:47,412 - INFO -   subtitle: 更新至中字
2025-06-28 14:32:47,478 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 14:32:47,511 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 14:32:47,511 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 14:32:47,511 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : trim(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 118 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#114</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#115</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#116</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#117</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$intro&nbsp;=&nbsp;trim(R(&#039;intro&#039;,&nbsp;&#039;P&#039;));
</td><tr style="background:#faa;"><td width="40">#118</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$media&nbsp;=&nbsp;trim(R(&#039;media&#039;,&nbsp;&#039;P&#039;));&nbsp;//添加媒体字段
</td><tr><td width="40">#119</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(strpos($media,&nbsp;&#039;##&#039;)&nbsp;!==&nbsp;false&nbsp;||&nbsp;strpos($media,&nbsp;&quot;\n&quot;)&nbsp;!==&nbsp;false)&nbsp;{&nbsp;&nbsp;//多行数据时候
</td><tr><td width="40">#120</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(strpos($media,&nbsp;&#039;##&#039;)&nbsp;!==&nbsp;false)&nbsp;{
</td><tr><td width="40">#121</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$media_arr&nbsp;=&nbsp;explode(&quot;##&quot;,&nbsp;strip_tags($media,&nbsp;&#039;##\n&#039;));&nbsp;//多个数据的时候
</td><tr><td width="40">#122</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#123</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(strpos($media,&nbsp;&#039;\n&#039;)&nbsp;!==&nbsp;false)&nbsp;{
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;trim(): Passing...&#039;, &#039;/Users/<USER>/...&#039;, 118)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(118): trim(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0027s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0027s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0004s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 14:32:47</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 0.0214</li>
		<li><span>内存开销:</span> 292.9 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 14:33:35,391 - INFO - 加载了 53 个分类映射
2025-06-28 14:33:35,391 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 14:33:35,391 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 14:35:37,375 - INFO - 加载了 53 个分类映射
2025-06-28 14:35:37,375 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 14:35:37,375 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 15:39:35,886 - INFO - 加载了 53 个分类映射
2025-06-28 15:39:35,886 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 15:39:35,886 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 16:19:21,666 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 16:19:21,667 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 16:19:21,667 - INFO -   subtitle: 更新至中字
2025-06-28 16:19:21,667 - INFO -   olmedia: bfvod###第01集$https://p.b8bf.com/video/huanyu/第01集/index.m3u8##第02集$https://p.b8bf.com/video/huanyu/第...
2025-06-28 16:19:21,741 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 16:19:21,776 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 16:19:21,776 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 16:19:21,776 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : trim(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 143 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#139</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$media&nbsp;=&nbsp;$this-&gt;filteurl($media);
</td><tr><td width="40">#140</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#141</td><td>
</td><tr><td width="40">#142</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$olmedia&nbsp;=&nbsp;R(&#039;olmedia&#039;,&nbsp;&#039;P&#039;);&nbsp;//在线媒体资源组
</td><tr style="background:#faa;"><td width="40">#143</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$clouddisk&nbsp;=&nbsp;trim(R(&#039;clouddisk&#039;,&nbsp;&#039;P&#039;));&nbsp;//添加云盘字段
</td><tr><td width="40">#144</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dateline&nbsp;=&nbsp;trim(R(&#039;dateline&#039;,&nbsp;&#039;P&#039;));
</td><tr><td width="40">#145</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;(empty($dateline))&nbsp;{
</td><tr><td width="40">#146</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$dateline&nbsp;=&nbsp;time();&nbsp;//更新时间&nbsp;POST时间&nbsp;取当前的时间戳
</td><tr><td width="40">#147</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;}
</td><tr><td width="40">#148</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$this-&gt;isremote&nbsp;=&nbsp;intval(R(&#039;isremote&#039;,&nbsp;&#039;P&#039;));&nbsp;//是否本地化图片
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;trim(): Passing...&#039;, &#039;/Users/<USER>/...&#039;, 143)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(143): trim(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0010s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0030s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0003s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 16:19:21</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 0.0193</li>
		<li><span>内存开销:</span> 293.12 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 16:20:03,934 - INFO - 加载了 53 个分类映射
2025-06-28 16:20:03,934 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:03,934 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 16:20:19,665 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 16:20:19,666 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 16:20:19,666 - INFO -   subtitle: 更新至中字
2025-06-28 16:20:19,666 - INFO -   olmedia: bfvod###第01集$https://p.b8bf.com/video/huanyu/第01集/index.m3u8##第02集$https://p.b8bf.com/video/huanyu/第...
2025-06-28 16:20:19,729 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 16:20:22,315 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 16:20:22,315 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 16:20:22,315 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : trim(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 227 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#223</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;daoyan&#039;&nbsp;=&gt;&nbsp;$daoyan,&nbsp;//添加导演
</td><tr><td width="40">#224</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;yanyuan&#039;&nbsp;=&gt;&nbsp;$yanyuan,&nbsp;//添加演员
</td><tr><td width="40">#225</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;dbid&#039;&nbsp;=&gt;&nbsp;$dbid,&nbsp;//添加豆瓣ID
</td><tr><td width="40">#226</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;language&#039;&nbsp;=&gt;&nbsp;$language,&nbsp;//添加语言
</td><tr style="background:#faa;"><td width="40">#227</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;color&#039;&nbsp;=&gt;&nbsp;trim(R(&#039;color&#039;,&nbsp;&#039;P&#039;)),
</td><tr><td width="40">#228</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;alias&#039;&nbsp;=&gt;&nbsp;$alias,
</td><tr><td width="40">#229</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;tags&#039;&nbsp;=&gt;&nbsp;_json_encode($tags),
</td><tr><td width="40">#230</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;intro&#039;&nbsp;=&gt;&nbsp;$intro,
</td><tr><td width="40">#231</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;media&#039;&nbsp;=&gt;&nbsp;$media,&nbsp;//添加媒体字段
</td><tr><td width="40">#232</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;olmedia&#039;&nbsp;=&gt;&nbsp;$olmedia,&nbsp;//添加在线播放组
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;trim(): Passing...&#039;, &#039;/Users/<USER>/...&#039;, 227)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(227): trim(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0008s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0023s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0007s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#3  <font color="red">[time:0.0020s]</font> SELECT * FROM vod_category WHERE `cid`=15 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#4  <font color="red">[time:0.0005s]</font> SELECT * FROM vod_models WHERE `mid`=2 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#5  <font color="red">[time:0.0011s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#6  <font color="red">[time:0.0067s]</font> UPDATE vod_framework_maxid SET maxid=&#039;15&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#7  <font color="red">[time:0.0008s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#8  <font color="red">[time:0.0009s]</font> UPDATE vod_framework_count SET count=&#039;15&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#9  <font color="red">[time:0.0014s]</font> SELECT * FROM vod_cms_article_attach WHERE `aid`=15 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#10  <font color="red">[time:0.0023s]</font> INSERT INTO vod_cms_article_attach SET `cid`=15,`uid`=1,`id`=0,`filename`=&#039;e0493456f06ab6af0d6a791820897e28.jpg&#039;,`filetype`=&#039;jpg&#039;,`filesize`=26821,`filepath`=&#039;202506/28/162022685fa5c644dc8MMVAmR.jpg&#039;,`dateline`=1751098819,`downloads`=0,`isimage`=1,`aid`=15</li><li class="even">#11  <font color="red">[time:0.0010s]</font> SELECT alias FROM vod_only_alias WHERE `alias` = &#039;huanyu&#039; <font color="blue">[explain type:  | rows: ]</font></li><li>#12  <font color="red">[time:0.0030s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;国产剧&#039; LIMIT 0,1 <font color="blue">[explain type:  | rows: ]</font></li><li class="even">#13  <font color="red">[time:0.0001s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#14  <font color="red">[time:0.0011s]</font> SELECT MAX(tagid) FROM vod_cms_article_tag <font color="blue">[explain type:  | rows: ]</font></li><li class="even">#15  <font color="red">[time:0.0004s]</font> INSERT INTO vod_framework_maxid SET name=&#039;cms_article_tag&#039;, maxid=&#039;5&#039;</li><li>#16  <font color="red">[time:0.0003s]</font> UPDATE vod_framework_maxid SET maxid=&#039;6&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#17  <font color="red">[time:0.0001s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#18  <font color="red">[time:0.0002s]</font> UPDATE vod_framework_count SET count=&#039;6&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#19  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=6 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#20  <font color="red">[time:0.0004s]</font> INSERT INTO vod_cms_article_tag SET `name`=&#039;国产剧&#039;,`count`=0,`content`=&#039;&#039;,`tagid`=6</li><li class="even">#21  <font color="red">[time:0.0003s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=6 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#22  <font color="red">[time:0.0000s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;大陆&#039; LIMIT 0,1 <font color="blue">[explain type:  | rows: ]</font></li><li class="even">#23  <font color="red">[time:0.0000s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#24  <font color="red">[time:0.0005s]</font> UPDATE vod_framework_maxid SET maxid=&#039;7&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#25  <font color="red">[time:0.0000s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#26  <font color="red">[time:0.0002s]</font> UPDATE vod_framework_count SET count=&#039;7&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#27  <font color="red">[time:0.0000s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=7 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#28  <font color="red">[time:0.0004s]</font> INSERT INTO vod_cms_article_tag SET `name`=&#039;大陆&#039;,`count`=0,`content`=&#039;&#039;,`tagid`=7</li><li class="even">#29  <font color="red">[time:0.0000s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=7 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#30  <font color="red">[time:0.0003s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = 2025 LIMIT 0,1 <font color="blue">[explain type: index | rows: 7]</font></li><li class="even">#31  <font color="red">[time:0.0000s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#32  <font color="red">[time:0.0002s]</font> UPDATE vod_framework_maxid SET maxid=&#039;8&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#33  <font color="red">[time:0.0000s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_tag&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#34  <font color="red">[time:0.0002s]</font> UPDATE vod_framework_count SET count=&#039;8&#039; WHERE name=&#039;cms_article_tag&#039; LIMIT 1</li><li class="even">#35  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=8 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#36  <font color="red">[time:0.0004s]</font> INSERT INTO vod_cms_article_tag SET `name`=2025,`count`=0,`content`=&#039;&#039;,`tagid`=8</li><li class="even">#37  <font color="red">[time:0.0000s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=8 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#38  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 1 <font color="blue">[explain type: ref | rows: 10]</font></li><li class="even">#39  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 0 <font color="blue">[explain type: ref | rows: 10]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/category_model.class.php</li><li class="even">#25 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/models_model.class.php</li><li>#26 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_attach_model.class.php</li><li class="even">#27 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_alias_model.class.php</li><li>#28 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_tag_model.class.php</li><li class="even">#29 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/ext/utf8.class.php</li><li>#30 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 16:20:19</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 2.5765</li>
		<li><span>内存开销:</span> 474.59 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 16:20:51,416 - INFO - 加载了 53 个分类映射
2025-06-28 16:20:51,417 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 16:20:51,417 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 16:20:51,419 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 16:20:51,419 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 16:20:51,419 - INFO -   subtitle: 更新至中字
2025-06-28 16:20:51,419 - INFO -   olmedia: bfvod###第01集$https://p.b8bf.com/video/huanyu/第01集/index.m3u8##第02集$https://p.b8bf.com/video/huanyu/第...
2025-06-28 16:20:51,470 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 16:20:55,297 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 16:20:55,297 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 16:20:55,298 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : trim(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 227 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#223</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;daoyan&#039;&nbsp;=&gt;&nbsp;$daoyan,&nbsp;//添加导演
</td><tr><td width="40">#224</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;yanyuan&#039;&nbsp;=&gt;&nbsp;$yanyuan,&nbsp;//添加演员
</td><tr><td width="40">#225</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;dbid&#039;&nbsp;=&gt;&nbsp;$dbid,&nbsp;//添加豆瓣ID
</td><tr><td width="40">#226</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;language&#039;&nbsp;=&gt;&nbsp;$language,&nbsp;//添加语言
</td><tr style="background:#faa;"><td width="40">#227</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;color&#039;&nbsp;=&gt;&nbsp;trim(R(&#039;color&#039;,&nbsp;&#039;P&#039;)),
</td><tr><td width="40">#228</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;alias&#039;&nbsp;=&gt;&nbsp;$alias,
</td><tr><td width="40">#229</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;tags&#039;&nbsp;=&gt;&nbsp;_json_encode($tags),
</td><tr><td width="40">#230</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;intro&#039;&nbsp;=&gt;&nbsp;$intro,
</td><tr><td width="40">#231</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;media&#039;&nbsp;=&gt;&nbsp;$media,&nbsp;//添加媒体字段
</td><tr><td width="40">#232</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;olmedia&#039;&nbsp;=&gt;&nbsp;$olmedia,&nbsp;//添加在线播放组
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;trim(): Passing...&#039;, &#039;/Users/<USER>/...&#039;, 227)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(227): trim(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0008s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0012s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0002s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#3  <font color="red">[time:0.0005s]</font> SELECT * FROM vod_category WHERE `cid`=15 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#4  <font color="red">[time:0.0002s]</font> SELECT * FROM vod_models WHERE `mid`=2 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#5  <font color="red">[time:0.0006s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#6  <font color="red">[time:0.0015s]</font> UPDATE vod_framework_maxid SET maxid=&#039;16&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#7  <font color="red">[time:0.0002s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#8  <font color="red">[time:0.0006s]</font> UPDATE vod_framework_count SET count=&#039;16&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#9  <font color="red">[time:0.0010s]</font> SELECT * FROM vod_cms_article_attach WHERE `aid`=16 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#10  <font color="red">[time:0.0007s]</font> INSERT INTO vod_cms_article_attach SET `cid`=15,`uid`=1,`id`=0,`filename`=&#039;e0493456f06ab6af0d6a791820897e28.jpg&#039;,`filetype`=&#039;jpg&#039;,`filesize`=26821,`filepath`=&#039;202506/28/162055685fa5e7452f7aX44aH.jpg&#039;,`dateline`=1751098851,`downloads`=0,`isimage`=1,`aid`=16</li><li class="even">#11  <font color="red">[time:0.0009s]</font> SELECT alias FROM vod_only_alias WHERE `alias` = &#039;huanyu&#039; <font color="blue">[explain type:  | rows: ]</font></li><li>#12  <font color="red">[time:0.0005s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;国产剧&#039; LIMIT 0,1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#13  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=6 <font color="blue">[explain type: const | rows: 1]</font></li><li>#14  <font color="red">[time:0.0000s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;大陆&#039; LIMIT 0,1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#15  <font color="red">[time:0.0000s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=7 <font color="blue">[explain type: const | rows: 1]</font></li><li>#16  <font color="red">[time:0.0001s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = 2025 LIMIT 0,1 <font color="blue">[explain type: index | rows: 8]</font></li><li class="even">#17  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=8 <font color="blue">[explain type: const | rows: 1]</font></li><li>#18  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 1 <font color="blue">[explain type: ref | rows: 11]</font></li><li class="even">#19  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 0 <font color="blue">[explain type: ref | rows: 11]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/category_model.class.php</li><li class="even">#25 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/models_model.class.php</li><li>#26 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_attach_model.class.php</li><li class="even">#27 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_alias_model.class.php</li><li>#28 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_tag_model.class.php</li><li class="even">#29 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/ext/utf8.class.php</li><li>#30 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 16:20:51</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 3.8178</li>
		<li><span>内存开销:</span> 468.32 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 16:21:12,797 - INFO - 加载了 53 个分类映射
2025-06-28 16:21:12,798 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:12,798 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 16:21:12,799 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 16:21:12,799 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 16:21:12,799 - INFO -   subtitle: 更新至中字
2025-06-28 16:21:12,799 - INFO -   olmedia: bfvod###第01集$https://p.b8bf.com/video/huanyu/第01集/index.m3u8##第02集$https://p.b8bf.com/video/huanyu/第...
2025-06-28 16:21:12,846 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 16:21:14,770 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 16:21:14,771 - INFO - API响应: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kong...
2025-06-28 16:21:14,771 - ERROR - ❌ API返回错误: <!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>出错啦！</title>
<style type="text/css">
body,div,ul,li,h1{margin:0;padding:0}
.kongcont h1,.kongcont ul,.kongcont ul li,.kongcont ul li span,.kongcont ul table tr td{font:14px/1.6 'Microsoft YaHei',Verdana,Arial,sans-serif}
.kongcont{width:98%;margin:8px auto;overflow:hidden;color:#000;border-radius:5px;box-shadow:0 0 20px #555;background:#fff;min-width:300px}
.kongcont h1{font-size:18px;height:26px;line-height:26px;padding:10px 3px 0;border-bottom:1px solid #dbdbdb;font-weight:700}
.kongcont ul,.kongcont h1{width:98%;margin:0 auto;overflow:hidden}
.kongcont ul{list-style:none;padding:3px;word-break:break-all}
.kongcont ul li,.kongcont ul table tr td{padding:0 3px}
.kongcont ul li span{float:left;display:inline;width:70px}
.kongcont ul li.even{background:#ddd}
.kongcont .fo{border-top:1px solid #dbdbdb;padding:5px 3px 10px;color:#666;text-align:right}
</style>
</head>
<body style="background:#aaa;padding:8px 0">
<div class="kongcont">
	<h1>错误信息</h1>
	<ul>
		<li><span>消息:</span> <font color="red">[代码警告] : strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated</font></li>
		<li><span>文件:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li>
		<li><span>位置:</span> 第 248 行</li>
	</ul>

	<h1>错误位置</h1>
	<ul><table cellspacing="0" width="100%"><tr><td width="40">#244</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;comments&#039;&nbsp;=&gt;&nbsp;0,
</td><tr><td width="40">#245</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;imagenum&#039;&nbsp;=&gt;&nbsp;$imagenum,
</td><tr><td width="40">#246</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;filenum&#039;&nbsp;=&gt;&nbsp;$filenum,
</td><tr><td width="40">#247</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;flags&#039;&nbsp;=&gt;&nbsp;implode(&#039;,&#039;,&nbsp;$flags),
</td><tr style="background:#faa;"><td width="40">#248</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;seo_title&#039;&nbsp;=&gt;&nbsp;trim(strip_tags(R(&#039;seo_title&#039;,&nbsp;&#039;P&#039;))),
</td><tr><td width="40">#249</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;seo_keywords&#039;&nbsp;=&gt;&nbsp;trim(strip_tags(R(&#039;seo_keywords&#039;,&nbsp;&#039;P&#039;))),
</td><tr><td width="40">#250</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&#039;seo_description&#039;&nbsp;=&gt;&nbsp;trim(strip_tags(R(&#039;seo_description&#039;,&nbsp;&#039;P&#039;))),
</td><tr><td width="40">#251</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;);
</td><tr><td width="40">#252</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$this-&gt;cms_content-&gt;table&nbsp;=&nbsp;&#039;cms_&#039;&nbsp;.&nbsp;$table;
</td><tr><td width="40">#253</td><td>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;$id&nbsp;=&nbsp;$this-&gt;cms_content-&gt;create($data);
</td></table></ul>

	<h1>基本信息</h1>
	<ul>
		<li><span>模型目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/model/</li>
		<li><span>视图目录:</span> /Users/<USER>/Projects/Website/vodcms/admin/view/default/</li>
		<li><span>控制器:</span> /Users/<USER>/Projects/Website/vodcms/admin/control/<font color="red">api_control.class.php</font></li>
		<li><span>日志目录:</span> /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/logs/</li>
	</ul>

	<h1>程序流程</h1>
	<ul><li>#0 [internal function]: debug::error_handler(8192, &#039;strip_tags(): P...&#039;, &#039;/Users/<USER>/...&#039;, 248)</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(248): strip_tags(NULL)</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php(75): api_control-&gt;post_add()</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(263): api_control-&gt;index()</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php(20): core::init_control()</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php(66): core::start()</li><li>#6 /Users/<USER>/Projects/Website/vodcms/admin/index.php(22): require(&#039;/Users/<USER>/...&#039;)</li><li class="even">#7 {main}</li></ul>

	<h1>SQL</h1>
	<ul><li>#0  <font color="red">[time:0.0007s]</font> SET character_set_connection=utf8, character_set_results=utf8, character_set_client=binary, sql_mode=&#039;&#039;</li><li class="even">#1  <font color="red">[time:0.0019s]</font> SELECT id FROM vod_cms_article WHERE `title` = &#039;焕羽&#039; <font color="blue">[explain type: ALL | rows: 12]</font></li><li>#2  <font color="red">[time:0.0006s]</font> SELECT * FROM vod_runtime WHERE `k`=&#039;cfg&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#3  <font color="red">[time:0.0002s]</font> SELECT * FROM vod_category WHERE `cid`=15 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#4  <font color="red">[time:0.0009s]</font> SELECT * FROM vod_models WHERE `mid`=2 LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#5  <font color="red">[time:0.0009s]</font> SELECT maxid FROM vod_framework_maxid WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#6  <font color="red">[time:0.0008s]</font> UPDATE vod_framework_maxid SET maxid=&#039;17&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#7  <font color="red">[time:0.0002s]</font> SELECT count FROM vod_framework_count WHERE name=&#039;cms_article_attach&#039; LIMIT 1 <font color="blue">[explain type: const | rows: 1]</font></li><li>#8  <font color="red">[time:0.0004s]</font> UPDATE vod_framework_count SET count=&#039;17&#039; WHERE name=&#039;cms_article_attach&#039; LIMIT 1</li><li class="even">#9  <font color="red">[time:0.0002s]</font> SELECT * FROM vod_cms_article_attach WHERE `aid`=17 LIMIT 1 <font color="blue">[explain type:  | rows: ]</font></li><li>#10  <font color="red">[time:0.0013s]</font> INSERT INTO vod_cms_article_attach SET `cid`=15,`uid`=1,`id`=0,`filename`=&#039;e0493456f06ab6af0d6a791820897e28.jpg&#039;,`filetype`=&#039;jpg&#039;,`filesize`=26821,`filepath`=&#039;202506/28/162114685fa5fab8b24KGivvU.jpg&#039;,`dateline`=1751098872,`downloads`=0,`isimage`=1,`aid`=17</li><li class="even">#11  <font color="red">[time:0.0007s]</font> SELECT alias FROM vod_only_alias WHERE `alias` = &#039;huanyu&#039; <font color="blue">[explain type:  | rows: ]</font></li><li>#12  <font color="red">[time:0.0010s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;国产剧&#039; LIMIT 0,1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#13  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=6 <font color="blue">[explain type: const | rows: 1]</font></li><li>#14  <font color="red">[time:0.0001s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = &#039;大陆&#039; LIMIT 0,1 <font color="blue">[explain type: const | rows: 1]</font></li><li class="even">#15  <font color="red">[time:0.0001s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=7 <font color="blue">[explain type: const | rows: 1]</font></li><li>#16  <font color="red">[time:0.0001s]</font> SELECT tagid FROM vod_cms_article_tag WHERE `name` = 2025 LIMIT 0,1 <font color="blue">[explain type: index | rows: 8]</font></li><li class="even">#17  <font color="red">[time:0.0000s]</font> SELECT * FROM vod_cms_article_tag WHERE `tagid`=8 <font color="blue">[explain type: const | rows: 1]</font></li><li>#18  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 1 <font color="blue">[explain type: ref | rows: 12]</font></li><li class="even">#19  <font color="red">[time:0.0001s]</font> SELECT COUNT(*) AS num FROM vod_cms_article_attach  WHERE `id` = 0 AND `uid` = 1 AND `isimage` = 0 <font color="blue">[explain type: ref | rows: 12]</font></li></ul>

	<h1>$_GET</h1>
	<ul><li>#pw => xoxo123</li><li class="even">#control => api</li><li>#action => index</li></ul>

	<h1>$_POST</h1>
	<ul style="white-space:pre"></ul>

	<h1>$_COOKIE</h1>
	<ul></ul>

	<h1>包含文件</h1>
	<ul><li>#0 /Users/<USER>/Projects/Website/vodcms/admin/index.php</li><li class="even">#1 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/kongphp.php</li><li>#2 /Users/<USER>/Projects/Website/vodcms/kbcms/config/config.inc.php</li><li class="even">#3 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/base.func.php</li><li>#4 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/core.class.php</li><li class="even">#5 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/debug.class.php</li><li>#6 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/log.class.php</li><li class="even">#7 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/model.class.php</li><li>#8 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/view.class.php</li><li class="even">#9 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/base/control.class.php</li><li>#10 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db.interface.php</li><li class="even">#11 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/db/db_mysqli.class.php</li><li>#12 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache.interface.php</li><li class="even">#13 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/cache/cache_memcache.class.php</li><li>#14 /Users/<USER>/Projects/Website/vodcms/kbcms/config/plugin.inc.php</li><li class="even">#15 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/apisource/conf.php</li><li>#16 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/dbcomment/conf.php</li><li class="even">#17 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/editor_tinymce/conf.php</li><li>#18 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/sitemap_data/conf.php</li><li class="even">#19 /Users/<USER>/Projects/Website/vodcms/kbcms/plugin/tw_links/conf.php</li><li>#20 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/api_control.class.php</li><li class="even">#21 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_admin_control/admin_control.class.php</li><li>#22 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_title_model.class.php</li><li class="even">#23 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/runtime_model.class.php</li><li>#24 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/category_model.class.php</li><li class="even">#25 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/models_model.class.php</li><li>#26 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_attach_model.class.php</li><li class="even">#27 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/only_alias_model.class.php</li><li>#28 /Users/<USER>/Projects/Website/vodcms/kbcms/runtime/kbcms_model/cms_content_tag_model.class.php</li><li class="even">#29 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/ext/utf8.class.php</li><li>#30 /Users/<USER>/Projects/Website/vodcms/kbcms/kongphp/tpl/exception.php</li></ul>

	<h1>其他信息</h1>
	<ul>
		<li><span>请求路径:</span> /admin/index.php?u=api-index&pw=xoxo123</li>
		<li><span>当前时间:</span> 2025-06-28 16:21:12</li>
		<li><span>当前网协:</span> 127.0.0.1</li>
		<li><span>运行时间:</span> 1.9111</li>
		<li><span>内存开销:</span> 468.16 KB</li>
	</ul>

	<ul class="fo">&lt;?php echo 'KongPHP, Road to Jane.'; ?&gt;</ul>
</div>
</body>
</html>

2025-06-28 16:21:31,408 - INFO - 加载了 53 个分类映射
2025-06-28 16:21:31,408 - INFO - 映射示例 1: 中国动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 2: 日本动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 3: 欧美动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 4: 国产动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 5: 海外动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 6: 日韩动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 7: 港台动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 8: 动画片 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 9: 有声动漫 -> 动漫 (cid: 4)
2025-06-28 16:21:31,408 - INFO - 映射示例 10: 记录片 -> 纪录片 (cid: 14)
2025-06-28 16:21:31,409 - INFO - 从数据库获取了 1 部影片用于测试
2025-06-28 16:21:31,409 - INFO - 准备API数据: 焕羽 (分类: 国产剧 -> 15)
2025-06-28 16:21:31,409 - INFO -   subtitle: 更新至中字
2025-06-28 16:21:31,409 - INFO -   olmedia: bfvod###第01集$https://p.b8bf.com/video/huanyu/第01集/index.m3u8##第02集$https://p.b8bf.com/video/huanyu/第...
2025-06-28 16:21:31,475 - INFO - 测试发送影片: 焕羽 (分类: 国产剧 -> cid: 15)
2025-06-28 16:21:34,286 - INFO - HTTP Request: POST http://vod123.com/admin/index.php?u=api-index&pw=xoxo123 "HTTP/1.1 200 OK"
2025-06-28 16:21:34,287 - INFO - API响应: {"err":0, "msg":"发布完成", "name":""}...
2025-06-28 16:21:34,287 - INFO - ✅ 成功发送: 焕羽
