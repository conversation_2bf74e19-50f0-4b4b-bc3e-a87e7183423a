#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API同步脚本
根据cid.txt的分类映射，测试数据库数据到VodCMS API的发送
功能：
1. 读取cid.txt中的分类映射关系
2. 从数据库中读取影片数据
3. 根据映射关系转换分类ID
4. 测试发送到VodCMS API
5. 记录发送结果和统计信息

作者：AI Assistant
创建时间：2025-01-19
修改记录：
- 2025-01-19: 初始创建，实现基础的API测试发送功能
- 2025-01-19: 根据VodCMS实际API实现调整URL构建和参数传递
"""

import asyncio
import sqlite3
import json
import re
import logging
import yaml
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import httpx
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/api_sync_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class CategoryMapper:
    """分类映射器 - 根据cid.txt进行分类映射"""

    def __init__(self, cid_file_path: str = "cid.txt"):
        self.category_mapping = {}
        self.load_category_mapping(cid_file_path)

    def load_category_mapping(self, file_path: str):
        """加载cid.txt中的分类映射关系"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析API分类（目标分类）
            api_categories = {}
            if '[' in content and ']' in content:
                start = content.find('[')
                end = content.find(']', start) + 1
                json_str = content[start:end]
                categories_list = json.loads(json_str)
                for cat in categories_list:
                    api_categories[cat['name']] = cat['cid']

            # 解析映射关系
            mapping_section = content.split('统一分类：')[1] if '统一分类：' in content else ''

            # 解析每一行映射
            for line in mapping_section.split('\n'):
                if '->' in line and 'cid' in line:
                    # 提取源分类和目标分类
                    parts = line.split('->')
                    if len(parts) >= 2:
                        source_categories = parts[0].strip()
                        target_part = parts[1].strip()

                        # 提取目标分类名和cid
                        target_name = target_part.split('cid')[0].strip()
                        cid_match = re.search(r'cid[：:]\s*(\d+)', target_part)

                        if cid_match:
                            cid = int(cid_match.group(1))

                            # 处理多个源分类（用逗号和中文逗号分隔）
                            source_cats = re.split(r'[，,]', source_categories)
                            for source_cat in source_cats:
                                source_cat = source_cat.strip()
                                if source_cat:
                                    self.category_mapping[source_cat] = {
                                        'cid': cid,
                                        'target_name': target_name
                                    }

            logger.info(f"加载了 {len(self.category_mapping)} 个分类映射")

            # 打印部分映射用于验证
            for i, (source, target) in enumerate(list(self.category_mapping.items())[:10]):
                logger.info(f"映射示例 {i+1}: {source} -> {target['target_name']} (cid: {target['cid']})")

        except Exception as e:
            logger.error(f"加载分类映射失败: {e}")
            # 使用默认映射
            self.category_mapping = {
                '国产剧': {'cid': 15, 'target_name': '国产剧'},
                '电视剧': {'cid': 15, 'target_name': '国产剧'},
                '内地剧': {'cid': 15, 'target_name': '国产剧'},
                '动漫': {'cid': 4, 'target_name': '动漫'},
                '动画片': {'cid': 4, 'target_name': '动漫'},
                '剧情片': {'cid': 11, 'target_name': '剧情片'},
                '电影': {'cid': 11, 'target_name': '剧情片'},
            }

    def get_category_id(self, source_category: str) -> int:
        """根据源分类获取目标分类ID"""
        # 直接匹配
        if source_category in self.category_mapping:
            return self.category_mapping[source_category]['cid']

        # 模糊匹配
        for key, value in self.category_mapping.items():
            if key in source_category or source_category in key:
                return value['cid']

        # 默认返回国产剧
        logger.warning(f"未找到分类映射: {source_category}，使用默认分类ID: 15 (国产剧)")
        return 15

    def get_category_name(self, source_category: str) -> str:
        """根据源分类获取目标分类名称"""
        cid = self.get_category_id(source_category)
        for key, value in self.category_mapping.items():
            if value['cid'] == cid:
                return value['target_name']
        return '国产剧'

class VodCMSAPITester:
    """VodCMS API测试器"""

    def __init__(self, config_path: str = "config/vodcms_api.yaml"):
        self.config = self._load_config(config_path)
        self.api_config = self.config.get('api', {})
        self.base_url = self.api_config.get('base_url', '')
        self.api_password = self.api_config.get('api_password', '')
        self.api_path = self.api_config.get('api_path', '/admin/index.php?api-index')

        # 初始化分类映射器
        self.category_mapper = CategoryMapper()

        # 统计信息
        self.stats = {
            'total_tested': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0,
            'category_stats': {}
        }

    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"配置文件未找到: {config_path}")
            return {}

    def get_films_from_db(self, limit: int = 10) -> List[Dict]:
        """从数据库获取影片数据用于测试"""
        conn = sqlite3.connect('data/spider.db')
        cursor = conn.cursor()

        # 获取影片基本信息和播放源 - 添加subtitle字段
        query = """
        SELECT
            f.id, f.title, f.subtitle, f.year, f.category, f.area, f.lang,
            f.director, f.actors, f.plot, f.ai_plot, f.pic_url,
            f.tags, f.aliases, f.latest_episode, f.is_commentary,
            p.play_urls, p.site_name, p.player_name
        FROM films f
        LEFT JOIN play_sources p ON f.id = p.film_id
        WHERE f.synced_at IS NULL  -- 只获取未同步的数据
        ORDER BY f.id
        LIMIT ?
        """

        cursor.execute(query, (limit * 3,))  # 多获取一些，因为需要合并播放源
        rows = cursor.fetchall()
        conn.close()

        # 按影片ID分组，合并播放源
        films_dict = {}

        for row in rows:
            film_id = row[0]
            if film_id not in films_dict:
                films_dict[film_id] = {
                    'id': row[0],
                    'title': row[1],
                    'subtitle': row[2],  # 添加subtitle字段
                    'year': row[3],
                    'category': row[4],
                    'area': row[5],
                    'lang': row[6],
                    'director': row[7],
                    'actors': row[8],
                    'plot': row[9],
                    'ai_plot': row[10],
                    'pic_url': row[11],
                    'tags': row[12],
                    'aliases': row[13],
                    'latest_episode': row[14],
                    'is_commentary': row[15],
                    'play_sources': []
                }

            # 添加播放源
            if row[16]:  # play_urls存在
                films_dict[film_id]['play_sources'].append({
                    'play_urls': row[16],
                    'site_name': row[17],
                    'player_name': row[18]
                })

        # 转换为列表并限制数量
        films_list = list(films_dict.values())[:limit]

        logger.info(f"从数据库获取了 {len(films_list)} 部影片用于测试")
        return films_list

    def prepare_api_data(self, film: Dict) -> Optional[Dict]:
        """准备发送给VodCMS API的数据"""
        try:
            # 获取分类ID
            cid = self.category_mapper.get_category_id(film['category'])

            # 合并播放源
            olmedia = self._merge_play_sources(film.get('play_sources', []))

            if not olmedia:
                logger.warning(f"影片 {film['title']} 没有有效播放源，跳过")
                return None

            # 准备API数据 - 根据VodCMS API控制器要求
            api_data = {
                'cid': cid,
                'title': film.get('title', '').strip() or '未知标题',
                'subtitle': film.get('subtitle', '').strip() or '',  # 使用从数据库读取的subtitle
                'year': str(film.get('year', '')).strip() or '',
                'daoyan': (film.get('director', '') or '').strip() or '',
                'yanyuan': (film.get('actors', '') or '').strip() or '',
                'language': (film.get('lang', '') or '').strip() or '国语',
                'dbid': '',  # 豆瓣ID暂时为空
                'alias': (film.get('aliases', '') or '').strip() or '',
                'flag': 'hot' if film.get('is_commentary') else '',
                'content': (film.get('ai_plot') or film.get('plot', '') or '精彩内容，敬请期待...').strip(),
                'intro': '',  # 简介暂时为空
                'media': '',  # VodCMS要求的media字段，设为空字符串避免null
                'olmedia': olmedia,  # 使用合并后的播放源
                'pic': (film.get('pic_url', '') or '').strip() or '',
                'area': (film.get('area', '') or '').strip() or '',
                'tags': (film.get('tags', '') or '').strip() or '',
                'isremote': '1',  # 字符串格式，下载远程图片
                'dateline': str(int(datetime.now().timestamp())),
                'author': '自动采集',  # 添加作者字段
                'source': '资源站',  # 添加来源字段
                'views': '0',  # 初始播放次数
                'iscomment': '1',  # 允许评论
                'ismedia': '0',  # 不屏蔽资源
                'isreview': '0',   # 已审核状态
                'clouddisk': '',  # 云盘资源，避免null错误
                'color': '',  # 标题颜色，避免null错误
                'seo_title': '',  # SEO标题，避免null错误
                'seo_keywords': '',  # SEO关键词，避免null错误
                'seo_description': ''  # SEO描述，避免null错误
            }

            # 确保content字段最少5个字符（VodCMS要求）
            if len(api_data['content']) < 5:
                api_data['content'] = f"{film.get('title', '精彩影片')}，精彩内容敬请期待..."

            logger.info(f"准备API数据: {film['title']} (分类: {film['category']} -> {cid})")
            logger.info(f"  subtitle: {api_data['subtitle']}")  # 记录subtitle信息
            logger.info(f"  olmedia: {olmedia[:100]}..." if len(olmedia) > 100 else f"  olmedia: {olmedia}")
            return api_data

        except Exception as e:
            logger.error(f"准备API数据失败: {e}")
            return None

    def _merge_play_sources(self, play_sources: List[Dict]) -> str:
        """合并播放源为VodCMS格式"""
        if not play_sources:
            return ""

        # VodCMS olmedia格式: 播放器名称###播放内容#tw#播放器名称###播放内容
        merged_sources = []

        for source in play_sources:
            play_urls = source.get('play_urls', '')
            site_name = source.get('site_name', 'unknown')
            player_name = source.get('player_name', f'{site_name}vod')

            if not play_urls or not play_urls.strip():
                continue

            try:
                # 尝试解析JSON格式的播放链接
                if play_urls.startswith('{') or play_urls.startswith('['):
                    urls_data = json.loads(play_urls)
                    if isinstance(urls_data, dict):
                        # 转换JSON格式到VodCMS格式
                        episodes = []
                        for key, url in urls_data.items():
                            if url and url.strip():
                                episodes.append(f"{key}${url}")
                        if episodes:
                            play_content = "##".join(episodes)
                            merged_sources.append(f"{player_name}###{play_content}")
                    continue
            except (json.JSONDecodeError, TypeError):
                pass

            # 处理原始字符串格式的播放链接
            if isinstance(play_urls, str):
                # 移除播放器名称前缀 (如: "bfvod$$$")
                if '$$$' in play_urls:
                    parts = play_urls.split('$$$', 1)
                    if len(parts) == 2:
                        player_name = parts[0]
                        play_content = parts[1]
                    else:
                        play_content = play_urls
                else:
                    play_content = play_urls

                # 标准化分隔符：将 ## 和 # 统一为 ##
                play_content = play_content.replace('#', '##')
                # 避免重复的 ####，替换为 ##
                play_content = play_content.replace('####', '##')

                # 确保集数格式正确 (第XX集$URL)
                episodes = []
                for episode in play_content.split('##'):
                    if episode.strip() and '$' in episode:
                        episodes.append(episode.strip())
                    elif episode.strip():
                        # 如果没有$分隔符，尝试添加默认集数
                        episode_num = len(episodes) + 1
                        episodes.append(f"第{episode_num:02d}集${episode.strip()}")

                if episodes:
                    final_content = "##".join(episodes)
                    merged_sources.append(f"{player_name}###{final_content}")

        # 用#tw#连接多个播放源 (VodCMS格式)
        result = '#tw#'.join(merged_sources) if merged_sources else ''

        # 记录合并结果用于调试
        if result:
            logger.debug(f"合并播放源结果: {len(merged_sources)} 个播放源")
            logger.debug(f"结果长度: {len(result)} 字符")

        return result

    async def test_single_film(self, film: Dict) -> bool:
        """测试发送单部影片"""
        self.stats['total_tested'] += 1

        # 准备API数据
        api_data = self.prepare_api_data(film)
        if not api_data:
            self.stats['skipped'] += 1
            return False

        # 统计分类
        category = film.get('category', 'unknown')
        if category not in self.stats['category_stats']:
            self.stats['category_stats'][category] = {'tested': 0, 'success': 0}
        self.stats['category_stats'][category]['tested'] += 1

        try:
            async with httpx.AsyncClient(timeout=30) as client:
                # 构建API URL - 根据VodCMS实际格式
                api_url = f"{self.base_url}{self.api_path}&pw={self.api_password}"

                logger.info(f"测试发送影片: {film['title']} (分类: {category} -> cid: {api_data['cid']})")
                logger.debug(f"API URL: {api_url}")
                logger.debug(f"API数据: {json.dumps(api_data, ensure_ascii=False, indent=2)}")

                # 发送POST请求
                response = await client.post(
                    api_url,
                    data=api_data,
                    headers={
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'Python VodCMS API Test Client'
                    }
                )

                # 解析响应
                if response.status_code == 200:
                    result = response.text
                    logger.info(f"API响应: {result[:200]}...")

                    # 判断是否成功 - 根据VodCMS API控制器的响应
                    success_keywords = ['发布完成', '更新成功', '成功']
                    error_keywords = ['验证密码错误', '亲，', '错误', '失败']

                    if any(keyword in result for keyword in success_keywords):
                        logger.info(f"✅ 成功发送: {film['title']}")
                        self.stats['successful'] += 1
                        self.stats['category_stats'][category]['success'] += 1

                        # 记录到数据库
                        self._record_sync_result(film['id'], True, result)
                        return True
                    elif any(keyword in result for keyword in error_keywords):
                        logger.error(f"❌ API返回错误: {result}")
                        self.stats['failed'] += 1
                        self._record_sync_result(film['id'], False, result)
                        return False
                    else:
                        # 未知响应，记录但当作失败处理
                        logger.warning(f"⚠️ 未知响应: {result}")
                        self.stats['failed'] += 1
                        self._record_sync_result(film['id'], False, result)
                        return False
                else:
                    error_msg = f"HTTP错误 {response.status_code}: {response.text}"
                    logger.error(f"❌ {error_msg}")
                    self.stats['failed'] += 1
                    self._record_sync_result(film['id'], False, error_msg)
                    return False

        except Exception as e:
            error_msg = f"发送异常: {str(e)}"
            logger.error(f"❌ 影片 {film['title']} {error_msg}")
            self.stats['failed'] += 1
            self._record_sync_result(film['id'], False, error_msg)
            return False

    def _record_sync_result(self, film_id: int, success: bool, response: str):
        """记录同步结果到数据库"""
        try:
            conn = sqlite3.connect('data/spider.db')
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO sync_records (film_id, sync_type, api_response, success, synced_at)
                VALUES (?, ?, ?, ?, ?)
            """, (film_id, 'api_test', response, success, datetime.now()))

            # 如果成功，更新films表的synced_at
            if success:
                cursor.execute("""
                    UPDATE films SET synced_at = ? WHERE id = ?
                """, (datetime.now(), film_id))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"记录同步结果失败: {e}")

    async def test_batch_sync(self, film_count: int = 5, delay: float = 2.0):
        """批量测试同步"""
        logger.info(f"开始批量测试 API 同步，测试 {film_count} 部影片")

        # 获取测试数据
        films = self.get_films_from_db(film_count)

        if not films:
            logger.warning("没有找到可测试的影片数据")
            return

        logger.info(f"获取到 {len(films)} 部影片，开始测试...")

        # 逐个测试（避免API压力）
        for i, film in enumerate(films, 1):
            logger.info(f"📤 测试进度: {i}/{len(films)}")

            success = await self.test_single_film(film)

            # 间隔等待
            if i < len(films):
                logger.info(f"等待 {delay} 秒后继续...")
                await asyncio.sleep(delay)

        # 打印统计结果
        self.print_stats()

    def print_stats(self):
        """打印测试统计结果"""
        print("\n" + "="*50)
        print("📊 API测试统计结果")
        print("="*50)
        print(f"总测试数量: {self.stats['total_tested']}")
        print(f"成功发送: {self.stats['successful']}")
        print(f"发送失败: {self.stats['failed']}")
        print(f"跳过数量: {self.stats['skipped']}")

        if self.stats['total_tested'] > 0:
            success_rate = self.stats['successful'] / self.stats['total_tested'] * 100
            print(f"成功率: {success_rate:.1f}%")

        print("\n📋 分类统计:")
        for category, stats in self.stats['category_stats'].items():
            rate = stats['success'] / stats['tested'] * 100 if stats['tested'] > 0 else 0
            print(f"  {category}: {stats['success']}/{stats['tested']} ({rate:.1f}%)")

        print("="*50)

async def main():
    """主函数"""
    print("🚀 VodCMS API 测试工具")
    print("根据 cid.txt 分类映射测试数据库到API的发送")
    print("-" * 50)

    # 检查配置
    if not Path("config/vodcms_api.yaml").exists():
        print("❌ 错误: 未找到 config/vodcms_api.yaml 配置文件")
        return

    if not Path("cid.txt").exists():
        print("❌ 错误: 未找到 cid.txt 分类映射文件")
        return

    if not Path("data/spider.db").exists():
        print("❌ 错误: 未找到 data/spider.db 数据库文件")
        return

    # 创建测试器
    tester = VodCMSAPITester()

    # 显示配置信息
    print(f"\n📋 当前配置:")
    print(f"  API地址: {tester.base_url}{tester.api_path}")
    print(f"  API密码: {tester.api_password}")
    print(f"  分类映射: {len(tester.category_mapper.category_mapping)} 个")

    # 用户选择测试模式
    print("\n选择测试模式:")
    print("1. 单部影片测试 (推荐)")
    print("2. 小批量测试 (5部影片)")
    print("3. 中等批量测试 (10部影片)")
    print("4. 查看分类映射")
    print("5. 查看数据库状态")

    choice = input("\n请选择 (1-5): ").strip()

    if choice == "1":
        films = tester.get_films_from_db(1)
        if films:
            await tester.test_single_film(films[0])
            tester.print_stats()
        else:
            print("❌ 没有找到可测试的影片")

    elif choice == "2":
        await tester.test_batch_sync(5, 2.0)

    elif choice == "3":
        await tester.test_batch_sync(10, 1.5)

    elif choice == "4":
        print("\n📋 当前分类映射:")
        for i, (source, target) in enumerate(list(tester.category_mapper.category_mapping.items())[:15]):
            print(f"  {source} -> {target['target_name']} (cid: {target['cid']})")
        if len(tester.category_mapper.category_mapping) > 15:
            print(f"  ... 还有 {len(tester.category_mapper.category_mapping) - 15} 个映射")

    elif choice == "5":
        # 查看数据库状态
        try:
            import sqlite3
            conn = sqlite3.connect('data/spider.db')
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM films WHERE synced_at IS NULL')
            unsynced = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM films')
            total = cursor.fetchone()[0]

            cursor.execute('SELECT category, COUNT(*) FROM films WHERE synced_at IS NULL GROUP BY category ORDER BY COUNT(*) DESC LIMIT 10')
            categories = cursor.fetchall()

            conn.close()

            print(f"\n📊 数据库状态:")
            print(f"  总影片数: {total:,}")
            print(f"  未同步: {unsynced:,}")
            print(f"  已同步: {total - unsynced:,}")

            print(f"\n📋 未同步影片分类分布 (前10):")
            for cat, count in categories:
                print(f"  {cat}: {count:,}")

        except Exception as e:
            print(f"❌ 查看数据库状态失败: {e}")

    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    # 确保日志目录存在
    Path("logs").mkdir(exist_ok=True)

    # 运行测试
    asyncio.run(main())