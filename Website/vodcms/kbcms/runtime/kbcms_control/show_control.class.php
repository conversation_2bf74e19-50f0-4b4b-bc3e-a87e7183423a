<?php

/**
 * (C)2012-2014 vodcms.com TongWang Inc.
 * Author: wuzhaohuan <<EMAIL>>
 */

defined('VODCMS_PATH') or exit;

class show_control extends control
{
	public $_cfg = array();	// 全站参数
	public $_var = array();	// 内容页参数


	public function index()
	{

		$_GET['cid'] = (int)R('cid');
		$_GET['id'] = (int)R('id');

		$this->_var = $this->category->get_cache($_GET['cid']);
		empty($this->_var) && core::error404();

		$this->_cfg = $this->runtime->xget();

		// 初始模型表名
		$this->cms_content->table = 'cms_' . $this->_var['table'];
		//初始化登陆模块

		// 读取内容
		$_show = $this->cms_content->read($_GET['id']);
		//var_dump($_show);
		// 读取评论审核数量
		$this->cms_content_comment_sort->table = 'cms_article_comment_sort';
		$where = array('id' => $_GET['id']);
		$recomment = $this->cms_content_comment_sort->find_fetch($where);
		$key = 'cms_article_comment_sort-id-' . $_GET['id'];
		if (!empty($recomment[$key]['checkcomments'])) {
			$_show['checkcomments'] = $recomment[$key]['checkcomments'];
		}

		//if(empty($_show['cid']) || $_show['cid'] != $_GET['cid']) core::error404();

		// SEO 相关
		$this->_cfg['titles'] = $_show['title'] . (empty($_show['seo_title']) ? '' : '/' . $_show['seo_title']);
		$this->_cfg['seo_keywords'] = empty($_show['seo_keywords']) ? $_show['title'] : $_show['seo_keywords'];
		$this->_cfg['seo_description'] = empty($_show['seo_description']) ? $_show['intro'] : $_show['seo_description'];

		//当前栏目
		if (count($this->_var['place']) > 1) {
			$this->_cfg['curr_cateurl'] = $this->_var['place'][1]['url'];
			$this->_cfg['curr_catename'] = $this->_var['place'][1]['name'];
			$this->_cfg['curr_catetopurl'] = $this->_var['place'][1]['topurl'];
		} else {
			$this->_cfg['curr_cateurl'] = $this->_var['place'][0]['url'];
			$this->_cfg['curr_catename'] = $this->_var['place'][0]['name'];
			$this->_cfg['curr_catetopurl'] = $this->_var['place'][0]['topurl'];
		}

		$this->assign('tw', $this->_cfg);
		$this->assign('tw_var', $this->_var);

		$GLOBALS['run'] = &$this;
		$GLOBALS['_show'] = &$_show;

		$ismobile = is_mobile();
		$_ENV['_theme'] = &$this->_cfg['theme'];
		$this->assign('is_mobile', $ismobile); //验证是否是手机端
		$this->display($this->_var['show_tpl']);
	}

}
