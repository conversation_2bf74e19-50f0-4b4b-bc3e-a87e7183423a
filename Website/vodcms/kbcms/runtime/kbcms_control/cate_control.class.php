<?php

/**
 * (C)2012-2014 vodcms.com TongWang Inc.
 * Author: wuzhaohuan <<EMAIL>>
 */

defined('VODCMS_PATH') or exit;

class cate_control extends control
{
	public $_cfg = array();	// 全站参数
	public $_var = array();	// 分类页参数


	public function index()
	{

		$_GET['cid'] = (int)R('cid');
		$_GET['order'] = R('order');
		$this->_var = $this->category->get_cache($_GET['cid']);
		empty($this->_var) && core::error404();

		$this->_cfg = $this->runtime->xget();

		// SEO 相关
		$this->_cfg['titles'] = empty($this->_var['seo_title']) ? '' : $this->_var['seo_title'];
		!empty($this->_var['seo_keywords']) && $this->_cfg['seo_keywords'] = $this->_var['seo_keywords'];
		!empty($this->_var['seo_description']) && $this->_cfg['seo_description'] =  $this->_var['seo_description'];
		$this->_cfg['upid'] = empty($this->_var['upid']) ? $_GET['cid'] : intval($this->_var['upid']); //新增type 频道id

		//当前栏目
		if (count($this->_var['place']) > 1) {
			$this->_cfg['curr_cateurl'] = $this->_var['place'][1]['url'];
			$this->_cfg['curr_catename'] = $this->_var['place'][1]['name'];
			$this->_cfg['curr_catetopurl'] = $this->_var['place'][1]['topurl'];
		} else {
			$this->_cfg['curr_cateurl'] = $this->_var['place'][0]['url'];
			$this->_cfg['curr_catename'] = $this->_var['place'][0]['name'];
			$this->_cfg['curr_catetopurl'] = $this->_var['place'][0]['topurl'];
		}
		$this->assign('tw', $this->_cfg);
		$this->assign('tw_var', $this->_var);


		$GLOBALS['run'] = &$this;



		$_ENV['_theme'] = &$this->_cfg['theme'];

		if (!empty($_GET['order']) == 'top') {
			$this->display('article_toplist.htm');
		} else {
			$this->display($this->_var['cate_tpl']);
		}
	}

}
