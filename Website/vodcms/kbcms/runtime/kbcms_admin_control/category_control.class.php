<?php

/**
 * (C)2012-2014 vodcms.com TongWang Inc.
 * Author: wuzhaohuan <<EMAIL>>
 */

defined('VODCMS_PATH') or exit;

include RUNTIME_CONTROL.'admin_control.class.php'; class category_control extends admin_control
{
	// 分类管理
	public function index()
	{
		$mod_name = $this->models->get_name();
		$this->assign('mod_name', $mod_name);

		$_ENV['_category_class'] = &$this->category;
		$_cfg = $this->runtime->xget();
		$this->assign('_cfg', $_cfg);

		$category_arr = $this->category->get_category();
		$this->assign('category_arr', $category_arr);

		$models = json_encode($this->models->get_models());
		$this->assign('models', $models);


		$this->display();
	}

	// 写入分类 (包括添加和编辑)
	public function set()
	{
		if (!empty($_POST)) {
			$post = array(
				'cid' => intval(R('cid', 'P')),
				'mid' =>  intval(R('mid', 'P')),
				'type' => intval(R('type', 'P')),
				'upid' => intval(R('upid', 'P')),
				'name' => trim(strip_tags(R('name', 'P'))),
				'alias' => trim(R('alias', 'P')),
				'intro' => trim(strip_tags(R('intro', 'P'))),
				'cate_tpl' => trim(strip_tags(R('cate_tpl', 'P'))),
				'show_tpl' => trim(strip_tags(R('show_tpl', 'P'))),
				'count' => 0,
				'orderby' => intval(R('orderby', 'P')),
				'seo_title' => trim(strip_tags(R('seo_title', 'P'))),
				'seo_keywords' => trim(strip_tags(R('seo_keywords', 'P'))),
				'seo_description' => trim(strip_tags(R('seo_description', 'P'))),
			);

			// 确保category对象正确初始化
			if (!isset($this->category) || $this->category === null) {
				// 尝试重新加载category模型
				$this->category = core::model('category');
				if (!$this->category) {
					E(1, 'category模型加载失败，请检查系统配置');
				}
			}

			$category = &$this->category;

			// 检查基本参数是否填写
			if ($err = $category->check_base($post)) {
				E(1, $err['msg'], $err['name']);
			}

			// cid 没有值时，为增加分类，否则为编辑分类
			if (empty($post['cid'])) {
				// 检查别名是否被使用
				if ($err = $category->check_alias($post['alias'])) {
					E(1, $err['msg'], $err['name']);
				}

				$maxid = $category->create($post);
				$mycid = $maxid;
				if (!$maxid) {
					E(1, '写入分类数据表出错');
				}

				// 单页时
				if ($post['mid'] == 1) {
					$pagedata = array('cid' => $maxid, 'content' => R('page_content', 'P'));
					if (!$this->cms_page->set($maxid, $pagedata)) {
						E(1, '写入单页数据表出错');
					}
				}
			} else {
				$mycid = $post['cid'];
				$data = $category->read($post['cid']);

				// 检查分类是否符合编辑条件
				if ($err = $category->check_is_edit($post, $data)) {
					E(1, $err['msg'], $err['name']);
				}

				// 别名被修改过才检查是否被使用
				if ($post['alias'] != $data['alias']) {
					$err = $category->check_alias($post['alias']);
					if ($err) {
						E(1, $err['msg'], $err['name']);
					}

					// 修改导航中的分类的别名
					$navigate = $this->kv->xget('navigate');
					foreach ($navigate as $k => $v) {
						if ($v['cid'] == $post['cid']) $navigate[$k]['alias'] = $post['alias'];
						if (isset($v['son'])) {
							foreach ($v['son'] as $k2 => $v2) {
								if ($v2['cid'] == $post['cid']) $navigate[$k]['son'][$k2]['alias'] = $post['alias'];
							}
						}
					}
					$this->kv->set('navigate', $navigate);
				}

				// 这里赋值，是为了开启缓存后，编辑时更新缓存
				$post['count'] = $data['count'];
				if (!$category->update($post)) {
					E(1, '写入分类数据表出错');
				}

				// 删除以前的单页数据
				if ($data['mid'] == 1 && $post['mid'] > 1) {
					$this->cms_page->delete($post['cid']);
				}

				// 单页时
				if ($post['mid'] == 1) {
					$pagedata = array('cid' => $post['cid'], 'content' => R('page_content', 'P'));
					if (!$this->cms_page->set($post['cid'], $pagedata)) {
						E(1, '写入单页数据表出错');
					}
				}
			}

			// 删除缓存
			$this->runtime->delete('cfg');
			$this->category->delete_cache();
			$this->runtime->truncate();

			if (empty($msg)) {
				E(0, 'cid_' . $mycid . '|保存成功');
			}
		}
	}

	// 删除分类
	public function del()
	{
		$cid = intval(R('cid', 'P'));

		$data = $this->category->read($cid);

		// 检查是否符合删除条件
		if ($err_msg = $this->category->check_is_del($data)) {
			E(1, $err_msg);
		}

		if (!$this->category->delete($cid)) {
			E(1, '操作分类表时出错');
		}

		if ($data['mid'] == 1 && !$this->cms_page->delete($cid)) {
			E(1, '操作单页表时出错');
		}

		// 删除导航中的分类
		$navigate = $this->kv->xget('navigate');
		foreach ($navigate as $k => $v) {
			if ($v['cid'] == $cid) unset($navigate[$k]);
			if (isset($v['son'])) {
				foreach ($v['son'] as $k2 => $v2) {
					if ($v2['cid'] == $cid) unset($navigate[$k]['son'][$k2]);
				}
			}
		}
		$this->kv->set('navigate', $navigate);

		// 删除缓存
		$this->runtime->delete('cfg');
		$this->category->delete_cache();
		$this->runtime->truncate();

		E(0, '删除完成');
	}

	// 修改分类排序
	public function edit_orderby()
	{
		if (!empty($_POST)) {
			$cid = intval(R('cid', 'P'));
			$orderby = intval(R('orderby', 'P'));

			$post = $this->category->read($cid);
			$post['orderby'] = $orderby;

			if (!$this->category->update($post)) {
				E(1, '修改分类排序出错');
			} else {
				E(0, '修改分类排序成功');
			}
		}
	}

		// 读取上级分类
	public function get_category_upid()
	{
		// 添加调试信息
		$debug = array();
		$debug[] = "=== get_category_upid 调试开始 ===";
		$debug[] = "请求参数: mid=" . R('mid') . ", upid=" . R('upid') . ", noid=" . R('noid');

		// 确保category对象正确初始化
		if (!isset($this->category) || $this->category === null) {
			$this->category = core::model('category');
			if (!$this->category) {
				$debug[] = "❌ category模型加载失败";
				echo json_encode(array('upid' => '<option value="0">无 (模型加载失败)</option>', 'debug' => $debug));
				exit;
			}
		}
		$debug[] = "✅ category模型加载成功";

								// 获取分类数据
		$category_db = $this->category->get_category_db();
		$debug[] = "DB数据条数: " . count($category_db);

		$category_arr = $this->category->get_category();
		$debug[] = "分类数组: " . (isset($category_arr[2]) ? count($category_arr[2]) . "个模型2分类" : "模型2无分类");

		$upid_html = $this->category->get_category_upid(intval(R('mid')), intval(R('upid')), intval(R('noid')));
		$debug[] = "生成的HTML: " . $upid_html;

		$data['upid'] = $upid_html;
		$data['debug'] = $debug;

		echo json_encode($data);
		exit;
	}

	// 读取分类 (JSON)
	public function get_category_json()
	{
		// 确保category对象正确初始化
		if (!isset($this->category) || $this->category === null) {
			$this->category = core::model('category');
			if (!$this->category) {
				echo json_encode(array('error' => 'category模型加载失败'));
				exit;
			}
		}

		$cid = intval(R('cid', 'P'));
		$data = $this->category->get($cid);

		// 读取单页内容
		if ($data['mid'] == 1) {
			$data2 = $this->cms_page->get($cid);
			if ($data2) $data['page_content'] = $data2['content'];
		}

		// 为频道时，检测是否有下级分类
		if ($data['type'] == 1 && $this->category->find_fetch_key(array('upid' => $data['cid']), array(), 0, 1)) {
			$data['son_cate'] = 1;
		}

		echo json_encode($data);
		exit;
	}

	// 读取分类 (JSON)
	public function get_category_content()
	{
		error_log("=== get_category_content 调试开始 ===");

		// 确保category对象正确初始化
		if (!isset($this->category) || $this->category === null) {
			$this->category = core::model('category');
			if (!$this->category) {
				error_log("❌ category模型加载失败");
				echo '<div class="p-8 text-center bg-red-100 border border-red-200 rounded-lg"><h3 class="text-red-800">错误：category模型加载失败</h3></div>';
				exit;
			}
		}
		error_log("✅ category模型加载成功");

		$_ENV['_category_class'] = &$this->category;

		$mod_name = $this->models->get_name();
		error_log("模型名称: " . print_r($mod_name, true));

		// 先获取数据库原始数据
		$db_categories = $this->category->get_category_db();
		error_log("数据库原始分类数据: " . print_r($db_categories, true));

		// 再获取处理后的数组数据
		$category_arr = $this->category->get_category();
		error_log("处理后的分类数组: " . print_r($category_arr, true));

		// 调试信息
		if (empty($category_arr)) {
			error_log("❌ get_category()返回空数组");
			if (!empty($db_categories)) {
				error_log("⚠️ 数据库有数据但get_category()返回空，处理过程有问题");
			} else {
				error_log("❌ 数据库中也没有分类数据");
			}
		} else {
			error_log("✅ get_category()返回了数据");
		}

		$this->assign('mod_name', $mod_name);
		$this->assign('category_arr', $category_arr);

		error_log("=== get_category_content 调试结束 ===");

		$this->display('inc-category_content.htm');
		exit;
	}

}
