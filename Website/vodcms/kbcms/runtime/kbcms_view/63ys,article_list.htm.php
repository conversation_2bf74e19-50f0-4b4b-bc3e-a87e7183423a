<?php defined('APP_NAME') || exit('Access Denied'); /**
 * 分类展示模块
 * @param int cid 分类ID 如果不填：自动识别
 * @param string type 显示类型   同级(sibling)、子级(child)、父级(parent)、顶级(top)
 * @param int mid 模型ID (默认自动识别)
 * @return array
 */
function kp_block_category($conf)
{
	global $run;
	$_cfg = $run->kv->xget(); //读取配置

	$cid = isset($conf['cid']) ? intval($conf['cid']) : _int($_GET, 'cid');
	$mid = isset($conf['mid']) ? intval($conf['mid']) : (isset($run->_var['mid']) ? $run->_var['mid'] : 2);
	$type = isset($conf['type']) && in_array($conf['type'], array('sibling', 'child', 'parent', 'top')) ? $conf['type'] : 'sibling';
	$tyname = isset($conf['tyname']) && $conf['tyname'] == 1 ? 1 : 0;
	$cate_arr = $run->category->get_category_db();

	switch ($type) {
		case 'sibling':
			$upid = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0; //计算upid 频道id
			break;
		case 'child':
			$upid = $cid;
			break;
		case 'parent':
			$parent_upid_temp = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0;
			$upid = (isset($cate_arr[$parent_upid_temp]) && isset($cate_arr[$parent_upid_temp]['upid'])) ? $cate_arr[$parent_upid_temp]['upid'] : $parent_upid_temp;
			break;
		case 'top':
			$upid = 0;
	}
	// $cids variable is declared here but seems unused later if $type is not 'sibling' or $conf['cid'] is not a comma-separated list.
	// This part of the logic might need review for its intended purpose if $cids is meant to be used more broadly.
	// For now, just ensuring it's declared if the conditions are met.
	if (isset($conf['cid']) && strpos($conf['cid'], ',') !== false && $type && $type == 'sibling') {
		$cids = explode(',', $conf['cid']); //分割数组,多分类写法
	}

	$filtered_cate_arr = array();
	foreach ($cate_arr as $k => $v_val) { // Changed to $v_val to avoid conflict if $v is used later by reference
		if (!is_array($v_val) || !isset($v_val['upid']) || !isset($v_val['mid']) || !isset($v_val['cid']) || !isset($v_val['alias'])) {
			continue; // Skip malformed category entries
		}

		if ($v_val['upid'] != $upid || $v_val['mid'] != $mid) {
			// unset($cate_arr[$k]); // Modifying array during iteration can be problematic, build a new one
			continue;
		}

		$v_val['url'] = $run->category->category_url($v_val['cid'], $v_val['alias']);

		//多分类同时输出，只支持同级 'sibling'
		if ($type == 'sibling') {
			if (!empty($conf['cid'])) {
				// Ensure $cids is defined from the earlier block, or re-evaluate $conf['cid'] here if it's specific to this loop context
				$cid_list_from_conf = isset($cids) ? $cids : (strpos($conf['cid'], ',') !== false ? explode(',', $conf['cid']) : array($conf['cid']));
				if (in_array($v_val['cid'], $cid_list_from_conf) == false) {
					// unset($cate_arr[$k]);
					continue;
				}
			}
		}
		$filtered_cate_arr[$k] = $v_val;
	}
	$cate_arr = $filtered_cate_arr; // Assign filtered array back

	if ($tyname && isset($_cfg['link_type_page_pre'])) { // Added isset for $_cfg key
		$cate_arr['tyname'] = $_cfg['link_type_page_pre']; //是否设置
	}

	//var_dump($cate_arr);
	return $cate_arr;
}
/**
 * 列表页模块 (不推荐频道分类使用此模块，影响性能)
 * @param int pagenum 每页显示条数
 * @param int titlenum 标题长度
 * @param int intronum 简介长度
 * @param string dateformat 时间格式
 * @param string orderby 排序方式
 * @param int orderway 降序(-1),升序(1)
 * @return array
 */
function kp_block_global_cate($conf) {
	global $run;

	$pagenum = empty($conf['pagenum']) ? 20 : max(1, (int)$conf['pagenum']);
	$titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0;
	$intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0;
	$dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat'];
	$orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline','views')) ? $conf['orderby'] : 'id';
	$orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1;
	$top = isset($_GET['order'])? trim($_GET['order']) : '';//排序
	// 排除单页模型
	$cid = &$run->_var['cid'];
	$mid = &$run->_var['mid'];
	if($mid == 1) return FALSE;

	if(!empty($run->_var['son_cids']) && is_array($run->_var['son_cids'])) {
		// 影响数据库性能
		$where = array('cid' => array("IN" => $run->_var['son_cids']));
		$total = 0;
		$cate_arr = array();
		foreach($run->_var['son_cids'] as $v) {
			$cate_arr[$v] = $run->category->get_cache($v);
			$total += $cate_arr[$v]['count'];
		}
	}else{
		$where = array('cid' => $cid);
		$total = &$run->_var['count'];
	}
	
	// 分页相关

	if($top){
		$table = $run->_var['table'];
		$run->cms_content_views->table = $table_key = 'cms_'.$table.'_views';
		$total = $run->cms_content_views->find_count($where);
		$maxpage = max(1, ceil($total/$pagenum));
		$page = min($maxpage, max(1, intval(R('page'))));
		$pages = pages($page, $maxpage, $run->category->category_url_top($cid, $run->_var['alias'],$top, TRUE),3);//分页链接格式化
		$pp  = $run->category->category_url_top($cid, $run->_var['alias'],$top, TRUE);
		$table = $run->_var['table'];
		$run->cms_content_views->table = $table_key = 'cms_'.$table.'_views';
		$key_arr = $run->cms_content_views->find_fetch($where,array($orderby => $orderway), ($page-1)*$pagenum, $pagenum, $total);
		
		$table_key .= '-id-';
		$keys = array();
		foreach($key_arr as $v) {
			$keys[] = $v['id'];
			
		}
		// 读取内容列表
		$run->cms_content->table = 'cms_'.$table;
		$list_arr = $run->cms_content->mget($keys);
		
	}else{
		$maxpage = max(1, ceil($total/$pagenum));
		$page = min($maxpage, max(1, intval(R('page'))));
		$pages = pages($page, $maxpage, $run->category->category_url($cid, $run->_var['alias'],TRUE),3);//分页链接格式化
		// 初始模型表名
		$run->cms_content->table = 'cms_'.$run->_var['table'];
		// 获取内容列表
		$where['isreview'] = 0;
		$list_arr = $run->cms_content->list_arr($where, $orderby, $orderway, ($page-1)*$pagenum, $pagenum, $total);	
	}

	foreach($list_arr as $key => &$v) {
		$cate_arr = $run->category->get_cache($v['cid']);
		$v['cate_name'] = $cate_arr['name'];
		if(count($cate_arr['place']) == 2){
			$v['cate_url'] = $cate_arr['place'][1]['url'];//分类URL
		}else{
			$v['cate_url'] = $cate_arr['place'][0]['url'];//分类URL
		}
		$run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum);
	}


	return array('total'=> $total, 'pages'=> $pages, 'list'=> $list_arr);
}
$gdata = kp_block_global_cate(array (
  'pagenum' => '30',
  'dateformat' => 'Y-m-d',
  'orderby' => 'dateline',
));
/**
 * 内容列表模块
 * @param int cid 分类ID 如果不填：自动识别 (不推荐用于读取频道分类，影响性能)
 * @param int mid 模型ID (当cid为0时，设置mid才能生效，否则程序自动识别)
 * @param string dateformat 时间格式
 * @param int titlenum 标题长度
 * @param int intronum 简介长度
 * @param string orderby 排序方式
 * @param int orderway 降序(-1),升序(1)
 * @param int start 开始位置
 * @param int i 序号
 * @param int limit 显示几条
 * @param int is_subtit 是否预告片 1是 0否 判断subtitle是否为空
 * @return array
 */
function kp_block_list($conf) {
	global $run;

	
	$cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0);
	$rcid = isset($_GET['cid'])? intval($_GET['cid']):'';//单页的cid
	$mid = _int($conf, 'mid', 2);
	$dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat'];
	$titlenum = _int($conf, 'titlenum');
	$intronum = _int($conf, 'intronum');
	$orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline')) ? $conf['orderby'] : 'id';
	$orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1;
	$start = _int($conf, 'start');
	$limit = _int($conf, 'limit', 10);
	$is_subtit = isset($conf['is_subtit']) && $conf['is_subtit'] == 1 ? 1: 0;
	$isreview = 0; //文章审核状态，默认是0已审，1未审
	
	// 读取分类内容
	
	if($cid == 0) {
		$cate_name = 'No Title';
		$cate_url = 'javascript:;';
		$table_arr = &$run->_cfg['table_arr'];
		$table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article';
		$where = array();
		$where['isreview']= $isreview;
	}else{
		$cate_arr = $run->category->get_cache($cid);
		if(empty($cate_arr)) return;
		$cate_name = $cate_arr['name'];
		$cate_url = $run->category->category_url($cid, $cate_arr['alias']);
		//$table = &$cate_arr['table'];
		$table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article';
		//判断是否多分类
		if(isset($conf['cid'])){
			$cids = explode(',',trim($conf['cid'])); //分割组成数组
		}
		if(!empty($cids) && is_array($cids) && count($cids)>1 ) { 
			$where = array('cid' => array("IN" => $cids)); // 影响数据库性能
		}else{
			$where = array('cid' => $cid);
		}
		$where['isreview']= $isreview;
	}

	if($is_subtit == 1){ //是否是预告片
		$where['subtitle']= ''; 
	}
	
	// 初始模型表名
	$run->cms_content->table = 'cms_'.$table;
	//$run->cms_content->table = 'cms_article';

	// 读取内容列表
	$list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit);
	foreach($list_arr as &$v) {
		$cate_arr = $run->category->get_cache($v['cid']);
		$v['cate_name'] = $cate_arr['name'];
		if(count($cate_arr['place']) == 2){
			$v['cate_url'] = $cate_arr['place'][1]['url'];//分类URL
		}else{
			$v['cate_url'] = $cate_arr['place'][0]['url'];//分类URL
		}
		$run->cms_content->format($v,$mid,$dateformat, $titlenum, $intronum);
	}


	//return array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr);
	return array('list'=> $list_arr);
}
?><!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title><?php $page = isset($_GET['page'])? $_GET['page']:'';$typetname = str_replace("片","电影",$tw['curr_catename']);
if($tw_var['upid'] == 1){$catname = $typetname."免费电影在线观看 - ".$tw['curr_catename']."大全 - 高清电影完整版在线播放";
}elseif($tw_var['upid'] == 2){$catname =  $typetname."电视剧在线观看-".$tw['curr_catename']."电视剧免费全集观看";
}elseif($tw_var['cid'] == 3){$catname =  $tw[titles].'动漫'."好看的动画片-".$tw['curr_catename']."动画片手机在线";
}else{$catname = $typetname."手机MP4无线观看-".$tw['curr_catename'].'电影排行榜-' ;} echo(isset($catname) ? $catname : ''); ?> <?php if (!empty($page)) { ?>第<?php echo(isset($page) ? $page : ''); ?>页<?php } ?> - 63影视</title>
    <meta name="keywords" content="<?php if ($tw_var['upid'] == 1) { echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>高清电影在线播放,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>视频在线观看,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>在线手机观看,免费在线播放<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); }elseif($tw_var['upid'] == 2) { echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>MP4手机观看,电视剧免费播放全集<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>手机无线播放,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>电视剧手机在线观看<?php }elseif($tw_var['cid'] == 3) { echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>经典动漫电影,少儿动画片,动漫动画片推荐,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>迅雷电影下载<?php }else{ echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>在线观看免费观看,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>电视剧免费全集观看,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>HD高清下载,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>电影排行榜<?php } ?>">
    <meta name="description" content="<?php if ($tw_var['upid'] == 1) { echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>排行榜十强<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,经典电影<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,高清大片在线<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,经典电影<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,迅雷下载<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); }elseif($tw_var['upid'] == 2) { echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>全集在线观看,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>支持全集VIP免费观看,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>免费追剧大全,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>最新视频播放,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>电视剧天天看高清影视,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>BD免费下载<?php }elseif($tw_var['cid'] == 3) { ?>
<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>BT种子下载,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>动漫动画片推荐,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>动画片手机在线,<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>最新动画片<?php }else{ echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>免费全集手机观看,电影排行榜<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,必看的经典<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); ?>,最新<?php echo(isset($tw['titles']) ? $tw['titles'] : ''); } ?>">
    <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta http-equiv="Cache-Control" content="no-transform">
<meta http-equiv="Cache-Control" content="no-siteapp">
<meta name="applicable-device" content="pc,mobile">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
<!-- 引用前端 Tailwind CSS -->
<link rel="stylesheet" href="/static/css/output.css">
<!-- 移动端优化脚本 -->
<script src="/static/js/jquery.min1.11.3.js"></script>
<script>
// 移动端适配
(function() {
    // 禁用双击缩放
    var lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        var now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // 移动端菜单切换
    window.toggleMobileMenu = function() {
        var menu = document.getElementById('mobile-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    };
})();

// 侧边栏控制脚本 - 增强版
(function() {
    console.log('侧边栏脚本开始加载...');

    // 侧边栏控制函数
    function toggleSidebar() {
        console.log('toggleSidebar 被调用');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        console.log('sidebar元素:', sidebar);
        console.log('overlay元素:', overlay);

        if (sidebar && overlay) {
            const isHidden = sidebar.classList.contains('-translate-x-full');
            console.log('当前侧边栏状态 - 隐藏:', isHidden);

            if (isHidden) {
                // 显示侧边栏
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                console.log('显示侧边栏');
            } else {
                // 隐藏侧边栏
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                console.log('隐藏侧边栏');
            }
        } else {
            console.error('找不到侧边栏或遮罩元素');
        }
    }

    function toggleSubmenu(menuId) {
        console.log('toggleSubmenu 被调用，menuId:', menuId);
        const menu = document.getElementById(menuId);
        const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

        if (menu) {
            menu.classList.toggle('hidden');
            console.log('切换子菜单:', menuId);
        }
        if (arrow) {
            arrow.classList.toggle('rotate-180');
        }
    }

    // 绑定事件的函数
    function bindEvents() {
        console.log('开始绑定事件...');

        // 移动端菜单按钮
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('移动端菜单按钮被点击');
                toggleSidebar();
            });
            console.log('移动端菜单按钮事件已绑定');
        } else {
            console.warn('找不到移动端菜单按钮');
        }

        // 关闭侧边栏按钮
        const closeSidebarBtn = document.getElementById('close-sidebar');
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('关闭按钮被点击');
                toggleSidebar();
            });
            console.log('关闭按钮事件已绑定');
        } else {
            console.warn('找不到关闭侧边栏按钮');
        }

        // 遮罩层点击
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('遮罩层被点击');
                toggleSidebar();
            });
            console.log('遮罩层事件已绑定');
        } else {
            console.warn('找不到侧边栏遮罩');
        }

        // 键盘ESC关闭侧边栏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                if (sidebar && overlay && !sidebar.classList.contains('-translate-x-full')) {
                    console.log('ESC键关闭侧边栏');
                    toggleSidebar();
                }
            }
        });
        console.log('键盘事件已绑定');
    }

    // 多种方式确保事件绑定
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindEvents);
    } else {
        bindEvents();
    }

    // 备用绑定方式
    setTimeout(bindEvents, 100);
    setTimeout(bindEvents, 500);

    // 全局函数，供onclick使用
    window.toggleSubmenu = toggleSubmenu;
    window.toggleSidebar = toggleSidebar;

    console.log('侧边栏脚本加载完成');
})();
</script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- PC端和移动端响应式导航栏 -->
<header class="bg-white shadow-md">
    <!-- 顶部区域 -->
    <div class="headtop bg-gray-50 py-2">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="logo">
                    <a href="https://www.63ys.com" class="flex items-center">
                        <img src="<?php echo(isset($tw['tpl']) ? $tw['tpl'] : ''); ?>images/logo.png" alt="63影视" title="63影视" class="h-8" />
                    </a>
                </div>

                <!-- 右侧搜索和链接 -->
                <div class="flex items-center space-x-4">
                    <!-- PC端顶部链接 -->
                    <div class="hidden lg:flex items-center space-x-4 text-gray-600" style="font-size: 12px;">
                        <a href="https://www.63ys.com/type.html" title="影片筛选" target="_blank" class="hover:text-blue-600">影片筛选</a>
                        <a href="https://www.63ys.com/tag-top/" target="_blank" class="hover:text-blue-600">关键词库</a>
                        <a href="https://www.63ys.com" title="加入收藏" class="hover:text-blue-600">收藏本站</a>
                    </div>

                    <!-- 搜索框 -->
                    <div class="header_search">
                        <form id="search_form" method="get" action="/index.php" class="flex">
                            <input type="hidden" name="u" value="search-index" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 hidden sm:block"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索影片..." style="font-size: 12px;" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 sm:hidden"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索..." style="font-size: 12px;" />
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </form>
                    </div>

                    <!-- 移动端菜单按钮 -->
                    <div class="lg:hidden">
                        <button id="mobile-menu-btn" class="p-2 text-gray-700 hover:text-blue-600 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PC端主导航栏 -->
    <div class="layout bg-blue-600 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu">
                <ul class="flex">
                    <li class="hover:bg-blue-700 <?php if (empty($tw_var['topcid'])) { ?>bg-white<?php } ?>">
                        <a href="https://www.63ys.com/" class="block px-6 py-3 <?php if (empty($tw_var['topcid'])) { ?>text-black<?php }else{ ?>text-white<?php } ?> hover:text-blue-100" style="font-size: 12px;">首页</a>
                    </li>
                    <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="hover:bg-blue-700 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'bg-white'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-6 py-3 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-black'; }else{ echo 'text-white'; } ?> hover:text-blue-100" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                    <?php unset($data); ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- PC端子导航栏 -->
    <div class="layout border-b border-gray-200 bg-gray-50 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu_sub py-2">
                <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                <ul class="flex flex-wrap">
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="mr-6 mb-1 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'font-semibold'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="text-gray-700 hover:text-blue-600" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                </ul>
                <?php unset($data); ?>

                <?php $data = kp_block_category(array (
  'tyname' => '1',
)); ?>
                <ul class="flex flex-wrap mt-2 pt-2 border-t border-gray-200">
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_色情__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">色情</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_情色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">情色</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_理论__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">理论</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_限制__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">限制</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_三级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">三级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_禁片__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">禁片</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_R级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">R级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_黄色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">黄色</a></li>
                </ul>
                <?php unset($data); ?>
            </div>
        </div>
    </div>
</header>

<!-- 移动端侧边栏遮罩 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

<!-- 移动端侧边栏 -->
<div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden">
    <div class="p-6">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">63</span>
                </div>
                <span class="text-xl font-bold">63影视</span>
            </div>
            <button id="close-sidebar" class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center hover:bg-teal-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- 移动端搜索框 -->
        <div class="mb-6">
            <form method="get" action="/index.php" class="relative">
                <input type="hidden" name="u" value="search-index" />
                <input type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" placeholder="搜索..." class="w-full bg-gray-800 text-white pl-4 pr-10 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500" style="font-size: 12px;">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-400 hover:text-teal-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </form>
        </div>

        <!-- 移动端导航菜单 -->
        <nav class="space-y-2">
            <!-- 首页 -->
            <a href="https://www.63ys.com/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors <?php if (empty($tw_var['topcid'])) { ?>bg-gray-800 text-white<?php } ?>" style="font-size: 12px;">
                首页
            </a>

            <!-- 电影 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('movie-menu')" style="font-size: 12px;">
                    <span>电影</span>
                    <svg class="w-4 h-4 transition-transform" id="movie-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="movie-menu" class="ml-4 space-y-1 hidden">
                    <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; } ?>" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    <?php }} ?>
                    <?php unset($data); ?>
                </div>
            </div>

            <!-- 电视剧 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('tv-menu')" style="font-size: 12px;">
                    <span>电视剧</span>
                    <svg class="w-4 h-4 transition-transform" id="tv-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="tv-menu" class="ml-4 space-y-1 hidden">
                    <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; } ?>" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    <?php }} ?>
                    <?php unset($data); ?>
                </div>
            </div>

            <!-- 动漫 - 直接链接 -->
            <a href="/dongman/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                动漫
            </a>

            <!-- 短剧 - 直接链接 -->
            <a href="/duanju/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                短剧
            </a>

            <!-- 快捷链接 -->
            <div class="border-t border-gray-700 pt-4 mt-4">
                <a href="https://www.63ys.com/type.html" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    影片筛选
                </a>
                <a href="https://www.63ys.com/tag-top/" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    关键词库
                </a>
                <a href="https://www.63ys.com" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    收藏本站
                </a>
            </div>
        </nav>
    </div>
</div>

<script>
// 移动端菜单控制
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const closeSidebar = document.getElementById('close-sidebar');

    // 打开侧边栏
    mobileMenuBtn.addEventListener('click', function() {
        sidebar.classList.remove('-translate-x-full');
        sidebarOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });

    // 关闭侧边栏
    function closeSidebarMenu() {
        sidebar.classList.add('-translate-x-full');
        sidebarOverlay.classList.add('hidden');
        document.body.style.overflow = '';
    }

    closeSidebar.addEventListener('click', closeSidebarMenu);
    sidebarOverlay.addEventListener('click', closeSidebarMenu);

    // ESC键关闭侧边栏
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSidebarMenu();
        }
    });
});

// 移动端二级菜单展开/收起功能
function toggleSubmenu(menuId) {
    const menu = document.getElementById(menuId);
    const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

    if (menu.classList.contains('hidden')) {
        menu.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        menu.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}
</script>



        <!-- 主内容区 -->
        <main class="flex-1 px-4 py-6 max-w-7xl mx-auto w-full">
            <!-- 广告位 -->
            <div class="mb-6">
                <div class="js_topad"></div>
            </div>

            <!-- 内容主体 -->
            <div class="space-y-6">
                <!-- 面包屑导航 -->
                <nav class="bg-white rounded-lg shadow-sm p-4">
                    <div class="flex items-center text-sm text-gray-600 space-x-2">
                        <span class="text-blue-600 font-medium">当前位置：</span>
                        <a href="https://www.63ys.com/" class="text-blue-600 hover:text-blue-800">首页</a>
                        <span>&gt;</span>
                        <a href="<?php echo(isset($tw['curr_cateurl']) ? $tw['curr_cateurl'] : ''); ?>" target="_blank" class="text-blue-600 hover:text-blue-800"><?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); ?></a>
                        <span>&gt;&gt;</span>
                        <span class="text-gray-800"><?php if (!empty($page)) { ?>第<?php echo(isset($page) ? $page : ''); ?>页<?php } ?></span>
                        <div class="ml-auto">
                            <a href="<?php echo(isset($tw['curr_catetopurl']) ? $tw['curr_catetopurl'] : ''); ?>" title="<?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); ?>人气榜单" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">按人气排行榜</a>
                        </div>
                    </div>
                </nav>

                <!-- 筛选区域 -->
                <div class="bg-white rounded-lg shadow-sm p-4">
                    <?php $catname_dy = array("喜剧","爱情","搞笑","战争","科幻","黑帮","枪战","动作","剧情","纪录","古装","犯罪","伦理","三级","成人","情色","色情","黄色","神话","西部","漫威","昭氏","音乐","运动","冒险","传记","恐怖","悬疑","惊悚","校园","青春","短片","同性","罪犯","罪案","文艺","限制","禁片","经典","史诗","暴力","女性","特工","R级");
$catname_tv = array("美剧","英剧","日剧","韩剧","港剧","台剧","网络剧","言情","悬疑","医务","战争","喜剧","情景","谍战","历史","刑侦","警匪","烧脑","科幻","都市","农村","奇幻","魔幻","武侠","神话","古装","剧情","家庭");
$catname_dm = array("魔法","亲子","热血","少儿","搞笑","爱情","美女","运动","机战","励志","冒险","格斗","奇幻","H漫","BL动漫");
$catname_zy = array("选秀","情感","访谈","搞笑","游戏","职场","娱乐","真人秀","生活","体育","盛会","财经","亲子","纪实","脱口秀","网络节目","美食");
$type_year = array("2022","2021","2020","2019","2018","2017","2016","2015","2014","2013","2012","2011","2010");
$type_zy = array("BD720P","BD1080P","HD720P","HD1080P","1280","DVD","英语","韩语","法语","日语","俄语","印地语","德语","西班牙","中字","粤语","国语"); ?>
                    <?php $data = kp_block_category(array (
  'tyname' => '1',
)); ?> <?php $type_dir = $data['tyname']; unset($data); ?>

                    <!-- 影片类型筛选 -->
                    <div class="mb-4">
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-blue-500 mr-2"></span>
                            影片类型
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            <?php 
if($tw['upid'] == 1){ $catname = $catname_dy; }
elseif($tw['upid'] == 2){ $catname = $catname_tv; }
elseif($tw_var['cid'] == 3){ $catname = $catname_dm; }
else{ $catname = $catname_dy;}
 ?>
                            <?php if(isset($catname) && is_array($catname)) { foreach($catname as &$v) { ?>
                            <a class="px-3 py-1 text-sm rounded-full border transition-colors <?php if ($tw['titles'] ==$v) { ?>bg-blue-500 text-white border-blue-500<?php }else{ ?>bg-gray-50 text-gray-700 border-gray-200 hover:bg-blue-50 hover:border-blue-300<?php } ?>" href="/<?php echo(isset($type_dir) ? $type_dir : ''); echo(isset($tw['upid']) ? $tw['upid'] : ''); ?>_<?php echo(isset($v) ? $v : ''); ?>_<?php echo(isset($tw['year']) ? $tw['year'] : ''); ?>_<?php echo(isset($tw['zy']) ? $tw['zy'] : ''); ?>.html" title="<?php echo(isset($v) ? $v : ''); ?>全集迅雷下载"><?php echo(isset($v) ? $v : ''); ?></a>
                            <?php }} ?>
                        </div>
                    </div>

                    <!-- 资源筛选 -->
                    <div class="mb-4">
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-green-500 mr-2"></span>
                            资源类型
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            <?php if(isset($type_zy) && is_array($type_zy)) { foreach($type_zy as &$v) { ?>
                            <?php if(strpos($v,'粤语') !== false){ $yue = str_replace('粤语','粤',$v); }else{ $yue = $v; } ?>
                            <a href="/<?php echo(isset($type_dir) ? $type_dir : ''); echo(isset($tw['upid']) ? $tw['upid'] : ''); ?>___<?php echo(isset($tw['year']) ? $tw['year'] : ''); ?>_<?php echo(isset($yue) ? $yue : ''); ?>.html" title="<?php echo(isset($v) ? $v : ''); ?>全集" class="px-3 py-1 text-sm rounded-full bg-gray-50 text-gray-700 border border-gray-200 hover:bg-green-50 hover:border-green-300 transition-colors">
                                <?php if(strpos($v,'粤') !== false){ $v='粤语';} echo(isset($v) ? $v : ''); ?>
                            </a>
                            <?php }} ?>
                        </div>
                    </div>

                    <!-- 年份筛选 -->
                    <div>
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-purple-500 mr-2"></span>
                            年份
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            <?php if(isset($type_year) && is_array($type_year)) { foreach($type_year as &$v) { ?>
                            <?php if(strpos($v,'年代') !== false){ $year = str_replace('年代','s',$v); }else{ $year = $v;} ?>
                            <a href="/<?php echo(isset($type_dir) ? $type_dir : ''); echo(isset($tw['upid']) ? $tw['upid'] : ''); ?>___<?php echo(isset($year) ? $year : ''); ?>__.html" class="px-3 py-1 text-sm rounded-full bg-gray-50 text-gray-700 border border-gray-200 hover:bg-purple-50 hover:border-purple-300 transition-colors"><?php echo(isset($v) ? $v : ''); ?></a>
                            <?php }} ?>
                        </div>
                    </div>
                </div>

                <!-- 影片列表 -->
                
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="space-y-0">
                        <?php if(isset($gdata['list']) && is_array($gdata['list'])) { foreach($gdata['list'] as &$v) { ?>
                        <div class="border-b border-gray-100 last:border-b-0 p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex space-x-4">
                                <!-- 海报 -->
                                <div class="flex-shrink-0 w-20 sm:w-24 md:w-28">
                                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank" class="block aspect-[3/4] overflow-hidden rounded-lg shadow-sm">
                                        <img data-original="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>" class="w-full h-full object-cover lazypic hover:scale-105 transition-transform duration-300" alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>海报">
                                    </a>
                                </div>

                                <!-- 信息 -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank" title="<?php echo(isset($v['subject']) ? $v['subject'] : ''); ?>" class="hover:text-blue-600 transition-colors"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
                                    </h3>

                                    <div class="space-y-1 text-sm text-gray-600">
                                        <p class="line-clamp-1">
                                            <span class="text-gray-500">导演：</span>
                                            <?php if (!empty($v['daoyan_arr'][0]['name'])) { ?>
                                                <?php if(isset($v['daoyan_arr']) && is_array($v['daoyan_arr'])) { foreach($v['daoyan_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="_blank" class="text-blue-600 hover:text-blue-800"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a> <?php }} ?>
                                            <?php }else{ ?>未知<?php } ?>
                                        </p>

                                        <p class="line-clamp-1">
                                            <span class="text-gray-500">演员：</span>
                                            <?php if (!empty($v['yanyuan_arr'][0]['name'])) { ?>
                                                <?php if(isset($v['yanyuan_arr']) && is_array($v['yanyuan_arr'])) { foreach($v['yanyuan_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" target="_blank" class="text-blue-600 hover:text-blue-800"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a> <?php }} ?>
                                            <?php }else{ ?>未知<?php } ?>
                                        </p>

                                        <p class="flex flex-wrap items-center gap-2">
                                            <span class="text-gray-500">分类：</span>
                                            <a href="<?php echo(isset($v['year_url']) ? $v['year_url'] : ''); ?>" target="_blank" class="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs"><?php if (!empty($v['year'])) { echo(isset($v['year']) ? $v['year'] : ''); }else{ ?>2019<?php } ?></a>
                                            <a href="<?php echo(isset($v['cate_url']) ? $v['cate_url'] : ''); ?>" title="<?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?>" target="_blank" class="px-2 py-0.5 bg-green-100 text-green-800 rounded text-xs"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></a>
                                            <?php if (isset($v['tag_arr'])) { ?>
                                                <?php if(isset($v['tag_arr']) && is_array($v['tag_arr'])) { foreach($v['tag_arr'] as &$v2) { ?><a href="<?php echo(isset($v2['url']) ? $v2['url'] : ''); ?>" class="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200"><?php echo(isset($v2['name']) ? $v2['name'] : ''); ?></a><?php }} ?>
                                            <?php } ?>
                                        </p>

                                        <?php $time = date("Y-m-d"); ?>
                                        <p class="text-xs">
                                            <span class="text-gray-500">时间：</span>
                                            <span class="<?php if ($time == $v['date']) { ?>text-red-500 font-medium<?php }else{ ?>text-gray-600<?php } ?>"><?php echo(isset($v['date']) ? $v['date'] : ''); ?></span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php }} ?>
                    </div>

                    <!-- 分页 -->
                    <div class="p-4 border-t border-gray-100 bg-gray-50">
                        <div class="text-center">
                            <?php echo(isset($gdata['pages']) ? $gdata['pages'] : ''); ?>
                        </div>
                    </div>
                </div>
                

                <!-- 推荐区域 (仅桌面端显示) -->
                <div class="hidden lg:block bg-white rounded-lg shadow-sm p-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="w-1 h-5 bg-yellow-500 mr-2"></span>
                        <?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); ?>推荐
                    </h3>
                    <?php $data = kp_block_list(array (
  'limit' => '32',
  'orderby' => 'id',
  'titlenum' => '40',
)); ?>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <div class="border border-gray-200 rounded p-2 hover:border-yellow-300 transition-colors">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>迅雷下载" target="_blank" class="text-sm text-gray-700 hover:text-yellow-600 line-clamp-1"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a>
                        </div>
                        <?php }} ?>
                    </div>
                    <?php unset($data); ?>
                </div>
            </div>
        </main>

        <!-- 底部 -->
<footer class="bg-gray-900 text-gray-300">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 主要底部内容 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <!-- 网站信息 -->
            <div>
                <div class="flex items-center space-x-2 mb-4">
                    <img src="/kbcms/view/63ys/images/logo.png" alt="63影视" class="h-8 w-auto">
                    <span class="text-xl font-bold text-white">63影视</span>
                </div>
                <p class="text-sm text-gray-400 leading-relaxed mb-4">
                    提供最新最热门的电影、电视剧、动漫、综艺节目免费在线观看，支持手机观看，高清流畅，更新及时。
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.347-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.878-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- 快速链接 -->
            <div>
                <h3 class="text-white font-semibold mb-4">快速导航</h3>
                <ul class="space-y-2 text-sm">
                    <li><a href="/" class="hover:text-white transition-colors">首页</a></li>
                    <li><a href="/type/1.html" class="hover:text-white transition-colors">最新电影</a></li>
                    <li><a href="/type/2.html" class="hover:text-white transition-colors">最新电视剧</a></li>
                    <li><a href="/type/3.html" class="hover:text-white transition-colors">动漫</a></li>
                    <li><a href="/type/4.html" class="hover:text-white transition-colors">综艺</a></li>
                    <li><a href="/search.html" class="hover:text-white transition-colors">搜索</a></li>
                </ul>
            </div>

            <!-- 热门分类 -->
            <div>
                <h3 class="text-white font-semibold mb-4">热门分类</h3>
                <ul class="space-y-2 text-sm">
                    <li><a href="#" class="hover:text-white transition-colors">动作片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">喜剧片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">爱情片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">科幻片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">韩剧</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">美剧</a></li>
                </ul>
            </div>

            <!-- 联系信息 -->
            <div>
                <h3 class="text-white font-semibold mb-4">关于我们</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <span>中国大陆</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <span><EMAIL></span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span class="leading-relaxed">为用户提供优质的在线观影体验</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 底部版权信息 -->
        <div class="border-t border-gray-800 pt-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-sm text-gray-400 text-center md:text-left">
                    <p>&copy; 2024 63影视. 保留所有权利.</p>
                    <p class="mt-1">本站为非营利性站点，所有内容均来源于互联网，如有侵权请联系删除。</p>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">隐私政策</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">使用条款</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">联系我们</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible" onclick="scrollToTop()">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>
</footer>

<script>
// 回到顶部功能
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 显示/隐藏回到顶部按钮
window.addEventListener('scroll', function() {
    const backToTopButton = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
    } else {
        backToTopButton.classList.add('opacity-0', 'invisible');
        backToTopButton.classList.remove('opacity-100', 'visible');
    }
});
</script>
    </div>
</body>
</html>