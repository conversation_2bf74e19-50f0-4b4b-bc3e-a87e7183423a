<?php defined('APP_NAME') || exit('Access Denied'); /**
 * 分类展示模块
 * @param int cid 分类ID 如果不填：自动识别
 * @param string type 显示类型   同级(sibling)、子级(child)、父级(parent)、顶级(top)
 * @param int mid 模型ID (默认自动识别)
 * @return array
 */
function kp_block_category($conf)
{
	global $run;
	$_cfg = $run->kv->xget(); //读取配置

	$cid = isset($conf['cid']) ? intval($conf['cid']) : _int($_GET, 'cid');
	$mid = isset($conf['mid']) ? intval($conf['mid']) : (isset($run->_var['mid']) ? $run->_var['mid'] : 2);
	$type = isset($conf['type']) && in_array($conf['type'], array('sibling', 'child', 'parent', 'top')) ? $conf['type'] : 'sibling';
	$tyname = isset($conf['tyname']) && $conf['tyname'] == 1 ? 1 : 0;
	$cate_arr = $run->category->get_category_db();

	switch ($type) {
		case 'sibling':
			$upid = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0; //计算upid 频道id
			break;
		case 'child':
			$upid = $cid;
			break;
		case 'parent':
			$parent_upid_temp = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0;
			$upid = (isset($cate_arr[$parent_upid_temp]) && isset($cate_arr[$parent_upid_temp]['upid'])) ? $cate_arr[$parent_upid_temp]['upid'] : $parent_upid_temp;
			break;
		case 'top':
			$upid = 0;
	}
	// $cids variable is declared here but seems unused later if $type is not 'sibling' or $conf['cid'] is not a comma-separated list.
	// This part of the logic might need review for its intended purpose if $cids is meant to be used more broadly.
	// For now, just ensuring it's declared if the conditions are met.
	if (isset($conf['cid']) && strpos($conf['cid'], ',') !== false && $type && $type == 'sibling') {
		$cids = explode(',', $conf['cid']); //分割数组,多分类写法
	}

	$filtered_cate_arr = array();
	foreach ($cate_arr as $k => $v_val) { // Changed to $v_val to avoid conflict if $v is used later by reference
		if (!is_array($v_val) || !isset($v_val['upid']) || !isset($v_val['mid']) || !isset($v_val['cid']) || !isset($v_val['alias'])) {
			continue; // Skip malformed category entries
		}

		if ($v_val['upid'] != $upid || $v_val['mid'] != $mid) {
			// unset($cate_arr[$k]); // Modifying array during iteration can be problematic, build a new one
			continue;
		}

		$v_val['url'] = $run->category->category_url($v_val['cid'], $v_val['alias']);

		//多分类同时输出，只支持同级 'sibling'
		if ($type == 'sibling') {
			if (!empty($conf['cid'])) {
				// Ensure $cids is defined from the earlier block, or re-evaluate $conf['cid'] here if it's specific to this loop context
				$cid_list_from_conf = isset($cids) ? $cids : (strpos($conf['cid'], ',') !== false ? explode(',', $conf['cid']) : array($conf['cid']));
				if (in_array($v_val['cid'], $cid_list_from_conf) == false) {
					// unset($cate_arr[$k]);
					continue;
				}
			}
		}
		$filtered_cate_arr[$k] = $v_val;
	}
	$cate_arr = $filtered_cate_arr; // Assign filtered array back

	if ($tyname && isset($_cfg['link_type_page_pre'])) { // Added isset for $_cfg key
		$cate_arr['tyname'] = $_cfg['link_type_page_pre']; //是否设置
	}

	//var_dump($cate_arr);
	return $cate_arr;
}
/**
 * 内容列表模块
 * @param int cid 分类ID 如果不填：自动识别 (不推荐用于读取频道分类，影响性能)
 * @param int mid 模型ID (当cid为0时，设置mid才能生效，否则程序自动识别)
 * @param string dateformat 时间格式
 * @param int titlenum 标题长度
 * @param int intronum 简介长度
 * @param string orderby 排序方式
 * @param int orderway 降序(-1),升序(1)
 * @param int start 开始位置
 * @param int i 序号
 * @param int limit 显示几条
 * @param int is_subtit 是否预告片 1是 0否 判断subtitle是否为空
 * @return array
 */
function kp_block_list($conf) {
	global $run;

	
	$cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0);
	$rcid = isset($_GET['cid'])? intval($_GET['cid']):'';//单页的cid
	$mid = _int($conf, 'mid', 2);
	$dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat'];
	$titlenum = _int($conf, 'titlenum');
	$intronum = _int($conf, 'intronum');
	$orderby = isset($conf['orderby']) && in_array($conf['orderby'], array('id', 'dateline')) ? $conf['orderby'] : 'id';
	$orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1;
	$start = _int($conf, 'start');
	$limit = _int($conf, 'limit', 10);
	$is_subtit = isset($conf['is_subtit']) && $conf['is_subtit'] == 1 ? 1: 0;
	$isreview = 0; //文章审核状态，默认是0已审，1未审
	
	// 读取分类内容
	
	if($cid == 0) {
		$cate_name = 'No Title';
		$cate_url = 'javascript:;';
		$table_arr = &$run->_cfg['table_arr'];
		$table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article';
		$where = array();
		$where['isreview']= $isreview;
	}else{
		$cate_arr = $run->category->get_cache($cid);
		if(empty($cate_arr)) return;
		$cate_name = $cate_arr['name'];
		$cate_url = $run->category->category_url($cid, $cate_arr['alias']);
		//$table = &$cate_arr['table'];
		$table = isset($table_arr[$mid]) ? $table_arr[$mid] : 'article';
		//判断是否多分类
		if(isset($conf['cid'])){
			$cids = explode(',',trim($conf['cid'])); //分割组成数组
		}
		if(!empty($cids) && is_array($cids) && count($cids)>1 ) { 
			$where = array('cid' => array("IN" => $cids)); // 影响数据库性能
		}else{
			$where = array('cid' => $cid);
		}
		$where['isreview']= $isreview;
	}

	if($is_subtit == 1){ //是否是预告片
		$where['subtitle']= ''; 
	}
	
	// 初始模型表名
	$run->cms_content->table = 'cms_'.$table;
	//$run->cms_content->table = 'cms_article';

	// 读取内容列表
	$list_arr = $run->cms_content->find_fetch($where, array($orderby => $orderway), $start, $limit);
	foreach($list_arr as &$v) {
		$cate_arr = $run->category->get_cache($v['cid']);
		$v['cate_name'] = $cate_arr['name'];
		if(count($cate_arr['place']) == 2){
			$v['cate_url'] = $cate_arr['place'][1]['url'];//分类URL
		}else{
			$v['cate_url'] = $cate_arr['place'][0]['url'];//分类URL
		}
		$run->cms_content->format($v,$mid,$dateformat, $titlenum, $intronum);
	}


	//return array('cate_name'=> $cate_name, 'cate_url'=> $cate_url, 'list'=> $list_arr);
	return array('list'=> $list_arr);
}
?><!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>63影视_免费电影在线观看_最新电视剧_手机看片神器</title>
    <meta name="keywords" content="63影视,免费电影,在线观看,最新电视剧,手机看片,高清电影,免费追剧">
    <meta name="description" content="63影视提供最新最热门的电影、电视剧、动漫、综艺节目免费在线观看，支持手机观看，高清流畅，更新及时，是您追剧看片的最佳选择。">
    <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta http-equiv="Cache-Control" content="no-transform">
<meta http-equiv="Cache-Control" content="no-siteapp">
<meta name="applicable-device" content="pc,mobile">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
<!-- 引用前端 Tailwind CSS -->
<link rel="stylesheet" href="/static/css/output.css">
<!-- 移动端优化脚本 -->
<script src="/static/js/jquery.min1.11.3.js"></script>
<script>
// 移动端适配
(function() {
    // 禁用双击缩放
    var lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        var now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // 移动端菜单切换
    window.toggleMobileMenu = function() {
        var menu = document.getElementById('mobile-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    };
})();

// 侧边栏控制脚本 - 增强版
(function() {
    console.log('侧边栏脚本开始加载...');

    // 侧边栏控制函数
    function toggleSidebar() {
        console.log('toggleSidebar 被调用');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        console.log('sidebar元素:', sidebar);
        console.log('overlay元素:', overlay);

        if (sidebar && overlay) {
            const isHidden = sidebar.classList.contains('-translate-x-full');
            console.log('当前侧边栏状态 - 隐藏:', isHidden);

            if (isHidden) {
                // 显示侧边栏
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                console.log('显示侧边栏');
            } else {
                // 隐藏侧边栏
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                console.log('隐藏侧边栏');
            }
        } else {
            console.error('找不到侧边栏或遮罩元素');
        }
    }

    function toggleSubmenu(menuId) {
        console.log('toggleSubmenu 被调用，menuId:', menuId);
        const menu = document.getElementById(menuId);
        const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

        if (menu) {
            menu.classList.toggle('hidden');
            console.log('切换子菜单:', menuId);
        }
        if (arrow) {
            arrow.classList.toggle('rotate-180');
        }
    }

    // 绑定事件的函数
    function bindEvents() {
        console.log('开始绑定事件...');

        // 移动端菜单按钮
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('移动端菜单按钮被点击');
                toggleSidebar();
            });
            console.log('移动端菜单按钮事件已绑定');
        } else {
            console.warn('找不到移动端菜单按钮');
        }

        // 关闭侧边栏按钮
        const closeSidebarBtn = document.getElementById('close-sidebar');
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('关闭按钮被点击');
                toggleSidebar();
            });
            console.log('关闭按钮事件已绑定');
        } else {
            console.warn('找不到关闭侧边栏按钮');
        }

        // 遮罩层点击
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('遮罩层被点击');
                toggleSidebar();
            });
            console.log('遮罩层事件已绑定');
        } else {
            console.warn('找不到侧边栏遮罩');
        }

        // 键盘ESC关闭侧边栏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                if (sidebar && overlay && !sidebar.classList.contains('-translate-x-full')) {
                    console.log('ESC键关闭侧边栏');
                    toggleSidebar();
                }
            }
        });
        console.log('键盘事件已绑定');
    }

    // 多种方式确保事件绑定
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindEvents);
    } else {
        bindEvents();
    }

    // 备用绑定方式
    setTimeout(bindEvents, 100);
    setTimeout(bindEvents, 500);

    // 全局函数，供onclick使用
    window.toggleSubmenu = toggleSubmenu;
    window.toggleSidebar = toggleSidebar;

    console.log('侧边栏脚本加载完成');
})();
</script>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- PC端和移动端响应式导航栏 -->
<header class="bg-white shadow-md">
    <!-- 顶部区域 -->
    <div class="headtop bg-gray-50 py-2">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="logo">
                    <a href="https://www.63ys.com" class="flex items-center">
                        <img src="<?php echo(isset($tw['tpl']) ? $tw['tpl'] : ''); ?>images/logo.png" alt="63影视" title="63影视" class="h-8" />
                    </a>
                </div>

                <!-- 右侧搜索和链接 -->
                <div class="flex items-center space-x-4">
                    <!-- PC端顶部链接 -->
                    <div class="hidden lg:flex items-center space-x-4 text-gray-600" style="font-size: 12px;">
                        <a href="https://www.63ys.com/type.html" title="影片筛选" target="_blank" class="hover:text-blue-600">影片筛选</a>
                        <a href="https://www.63ys.com/tag-top/" target="_blank" class="hover:text-blue-600">关键词库</a>
                        <a href="https://www.63ys.com" title="加入收藏" class="hover:text-blue-600">收藏本站</a>
                    </div>

                    <!-- 搜索框 -->
                    <div class="header_search">
                        <form id="search_form" method="get" action="/index.php" class="flex">
                            <input type="hidden" name="u" value="search-index" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 hidden sm:block"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索影片..." style="font-size: 12px;" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 sm:hidden"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索..." style="font-size: 12px;" />
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </form>
                    </div>

                    <!-- 移动端菜单按钮 -->
                    <div class="lg:hidden">
                        <button id="mobile-menu-btn" class="p-2 text-gray-700 hover:text-blue-600 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PC端主导航栏 -->
    <div class="layout bg-blue-600 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu">
                <ul class="flex">
                    <li class="hover:bg-blue-700 <?php if (empty($tw_var['topcid'])) { ?>bg-white<?php } ?>">
                        <a href="https://www.63ys.com/" class="block px-6 py-3 <?php if (empty($tw_var['topcid'])) { ?>text-black<?php }else{ ?>text-white<?php } ?> hover:text-blue-100" style="font-size: 12px;">首页</a>
                    </li>
                    <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="hover:bg-blue-700 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'bg-white'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-6 py-3 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-black'; }else{ echo 'text-white'; } ?> hover:text-blue-100" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                    <?php unset($data); ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- PC端子导航栏 -->
    <div class="layout border-b border-gray-200 bg-gray-50 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu_sub py-2">
                <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                <ul class="flex flex-wrap">
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="mr-6 mb-1 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'font-semibold'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="text-gray-700 hover:text-blue-600" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                </ul>
                <?php unset($data); ?>

                <?php $data = kp_block_category(array (
  'tyname' => '1',
)); ?>
                <ul class="flex flex-wrap mt-2 pt-2 border-t border-gray-200">
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_色情__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">色情</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_情色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">情色</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_理论__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">理论</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_限制__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">限制</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_三级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">三级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_禁片__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">禁片</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_R级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">R级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_黄色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">黄色</a></li>
                </ul>
                <?php unset($data); ?>
            </div>
        </div>
    </div>
</header>

<!-- 移动端侧边栏遮罩 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

<!-- 移动端侧边栏 -->
<div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden">
    <div class="p-6">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">63</span>
                </div>
                <span class="text-xl font-bold">63影视</span>
            </div>
            <button id="close-sidebar" class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center hover:bg-teal-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- 移动端搜索框 -->
        <div class="mb-6">
            <form method="get" action="/index.php" class="relative">
                <input type="hidden" name="u" value="search-index" />
                <input type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" placeholder="搜索..." class="w-full bg-gray-800 text-white pl-4 pr-10 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500" style="font-size: 12px;">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-400 hover:text-teal-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </form>
        </div>

        <!-- 移动端导航菜单 -->
        <nav class="space-y-2">
            <!-- 首页 -->
            <a href="https://www.63ys.com/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors <?php if (empty($tw_var['topcid'])) { ?>bg-gray-800 text-white<?php } ?>" style="font-size: 12px;">
                首页
            </a>

            <!-- 电影 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('movie-menu')" style="font-size: 12px;">
                    <span>电影</span>
                    <svg class="w-4 h-4 transition-transform" id="movie-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="movie-menu" class="ml-4 space-y-1 hidden">
                    <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; } ?>" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    <?php }} ?>
                    <?php unset($data); ?>
                </div>
            </div>

            <!-- 电视剧 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('tv-menu')" style="font-size: 12px;">
                    <span>电视剧</span>
                    <svg class="w-4 h-4 transition-transform" id="tv-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="tv-menu" class="ml-4 space-y-1 hidden">
                    <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; } ?>" style="font-size: 12px;"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    <?php }} ?>
                    <?php unset($data); ?>
                </div>
            </div>

            <!-- 动漫 - 直接链接 -->
            <a href="/dongman/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                动漫
            </a>

            <!-- 短剧 - 直接链接 -->
            <a href="/duanju/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                短剧
            </a>

            <!-- 快捷链接 -->
            <div class="border-t border-gray-700 pt-4 mt-4">
                <a href="https://www.63ys.com/type.html" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    影片筛选
                </a>
                <a href="https://www.63ys.com/tag-top/" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    关键词库
                </a>
                <a href="https://www.63ys.com" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    收藏本站
                </a>
            </div>
        </nav>
    </div>
</div>

<script>
// 移动端菜单控制
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const closeSidebar = document.getElementById('close-sidebar');

    // 打开侧边栏
    mobileMenuBtn.addEventListener('click', function() {
        sidebar.classList.remove('-translate-x-full');
        sidebarOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });

    // 关闭侧边栏
    function closeSidebarMenu() {
        sidebar.classList.add('-translate-x-full');
        sidebarOverlay.classList.add('hidden');
        document.body.style.overflow = '';
    }

    closeSidebar.addEventListener('click', closeSidebarMenu);
    sidebarOverlay.addEventListener('click', closeSidebarMenu);

    // ESC键关闭侧边栏
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSidebarMenu();
        }
    });
});

// 移动端二级菜单展开/收起功能
function toggleSubmenu(menuId) {
    const menu = document.getElementById(menuId);
    const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

    if (menu.classList.contains('hidden')) {
        menu.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        menu.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}
</script>


        <!-- 主内容区 -->
        <main class="flex-1">
            <!-- 分类导航 -->
            <section class="bg-white shadow-sm">
                <div class="max-w-7xl mx-auto px-4 py-6">
                    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <?php $data = kp_block_category(array (
  'limit' => '12',
  'upid' => '0',
)); ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="bg-blue-50 hover:bg-blue-100 border border-blue-200 hover:border-blue-300 rounded-lg p-4 text-center transition-all group">
                            <div class="text-blue-600 group-hover:text-blue-800 font-medium"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></div>
                        </a>
                        <?php }} ?>
                        <?php unset($data); ?>
                    </div>
                </div>
            </section>

            <!-- 最新更新 -->
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">最新更新</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <?php $data = kp_block_list(array (
  'limit' => '12',
  'orderby' => 'dateline',
  'dateformat' => 'm-d',
)); ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <div class="group">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="block">
                                <div class="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                                    <img src="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>"
                                         alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"
                                         class="w-full aspect-[3/4] object-cover group-hover:scale-105 transition-transform duration-300"
                                         loading="lazy">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    <div class="absolute bottom-2 left-2 right-2 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                                        <h3 class="text-white font-medium text-sm line-clamp-2"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></h3>
                                        <p class="text-gray-300 text-xs mt-1"><?php if (!empty($v['year'])) { echo(isset($v['year']) ? $v['year'] : ''); }else{ ?>2024<?php } ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <?php }} ?>
                        <?php unset($data); ?>
                    </div>
                </div>
            </section>

            <!-- 热门推荐 -->
            <section class="py-8 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">热门推荐</h2>
                    <div class="space-y-4">
                        <?php $data = kp_block_list(array (
  'limit' => '8',
  'orderby' => 'hits',
  'dateformat' => 'Y-m-d',
)); ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <div class="flex bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors group">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="flex-shrink-0">
                                <img src="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>"
                                     alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"
                                     class="w-20 sm:w-24 md:w-28 aspect-[3/4] object-cover rounded-md shadow-sm"
                                     loading="lazy">
                            </a>
                            <div class="ml-4 flex-1 min-w-0">
                                <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="block">
                                    <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></h3>
                                    <div class="flex flex-wrap items-center gap-2 mt-2 text-sm text-gray-600">
                                        <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded"><?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?></span>
                                        <span><?php if (!empty($v['year'])) { echo(isset($v['year']) ? $v['year'] : ''); }else{ ?>2024<?php } ?></span>
                                        <span>•</span>
                                        <span><?php if (!empty($v['area'])) { echo(isset($v['area']) ? $v['area'] : ''); }else{ ?>未知<?php } ?></span>
                                    </div>
                                    <p class="text-gray-700 text-sm mt-2 line-clamp-2"><?php if (!empty($v['blurb'])) { ?>{$v[blurb]|substr:0:100}...<?php }else{ ?>暂无简介<?php } ?></p>
                                </a>
                            </div>
                        </div>
                        <?php }} ?>
                        <?php unset($data); ?>
                    </div>
                </div>
            </section>

            <!-- 电影专区 -->
            <section class="py-8">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">电影专区</h2>
                        <a href="/type/1.html" class="text-blue-600 hover:text-blue-800 font-medium">更多 →</a>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <?php $data = kp_block_list(array (
  'limit' => '12',
  'cid' => '1',
  'orderby' => 'dateline',
  'dateformat' => 'm-d',
)); ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <div class="group">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="block">
                                <div class="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                                    <img src="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>"
                                         alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"
                                         class="w-full aspect-[3/4] object-cover group-hover:scale-105 transition-transform duration-300"
                                         loading="lazy">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    <div class="absolute bottom-2 left-2 right-2 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                                        <h3 class="text-white font-medium text-sm line-clamp-2"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></h3>
                                        <p class="text-gray-300 text-xs mt-1"><?php if (!empty($v['year'])) { echo(isset($v['year']) ? $v['year'] : ''); }else{ ?>2024<?php } ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <?php }} ?>
                        <?php unset($data); ?>
                    </div>
                </div>
            </section>

            <!-- 电视剧专区 -->
            <section class="py-8 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">电视剧专区</h2>
                        <a href="/type/2.html" class="text-blue-600 hover:text-blue-800 font-medium">更多 →</a>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <?php $data = kp_block_list(array (
  'limit' => '12',
  'cid' => '2',
  'orderby' => 'dateline',
  'dateformat' => 'm-d',
)); ?>
                        <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                        <div class="group">
                            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" class="block">
                                <div class="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
                                    <img src="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>"
                                         alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"
                                         class="w-full aspect-[3/4] object-cover group-hover:scale-105 transition-transform duration-300"
                                         loading="lazy">
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                                    <div class="absolute bottom-2 left-2 right-2 transform translate-y-2 group-hover:translate-y-0 transition-transform duration-300">
                                        <h3 class="text-white font-medium text-sm line-clamp-2"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></h3>
                                        <p class="text-gray-300 text-xs mt-1"><?php if (!empty($v['year'])) { echo(isset($v['year']) ? $v['year'] : ''); }else{ ?>2024<?php } ?></p>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <?php }} ?>
                        <?php unset($data); ?>
                    </div>
                </div>
            </section>

            <!-- 友情链接 -->
            <section class="bg-gray-100">
                <div class="max-w-7xl mx-auto px-4 py-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">友情链接</h3>
                    <div class="flex flex-wrap gap-4 text-sm">
                        <a href="https://www.63ys.com" class="text-gray-600 hover:text-blue-600 transition-colors">63影视</a>
                        <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">免费电影</a>
                        <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">在线观看</a>
                        <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">高清电影</a>
                        <a href="#" class="text-gray-600 hover:text-blue-600 transition-colors">电视剧大全</a>
                    </div>
                </div>
            </section>
        </main>

        {template "inc-footer"}
    </div>

    <!-- 懒加载脚本 -->
    <script>
    // 图片懒加载
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('opacity-0');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    </script>
</body>
</html>