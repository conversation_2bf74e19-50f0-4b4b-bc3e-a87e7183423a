<?php defined('APP_NAME') || exit('Access Denied'); /**
 * 分类展示模块
 * @param int cid 分类ID 如果不填：自动识别
 * @param string type 显示类型   同级(sibling)、子级(child)、父级(parent)、顶级(top)
 * @param int mid 模型ID (默认自动识别)
 * @return array
 */
function kp_block_category($conf)
{
	global $run;
	$_cfg = $run->kv->xget(); //读取配置

	$cid = isset($conf['cid']) ? intval($conf['cid']) : _int($_GET, 'cid');
	$mid = isset($conf['mid']) ? intval($conf['mid']) : (isset($run->_var['mid']) ? $run->_var['mid'] : 2);
	$type = isset($conf['type']) && in_array($conf['type'], array('sibling', 'child', 'parent', 'top')) ? $conf['type'] : 'sibling';
	$tyname = isset($conf['tyname']) && $conf['tyname'] == 1 ? 1 : 0;
	$cate_arr = $run->category->get_category_db();

	switch ($type) {
		case 'sibling':
			$upid = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0; //计算upid 频道id
			break;
		case 'child':
			$upid = $cid;
			break;
		case 'parent':
			$parent_upid_temp = (isset($cate_arr[$cid]) && isset($cate_arr[$cid]['upid'])) ? $cate_arr[$cid]['upid'] : 0;
			$upid = (isset($cate_arr[$parent_upid_temp]) && isset($cate_arr[$parent_upid_temp]['upid'])) ? $cate_arr[$parent_upid_temp]['upid'] : $parent_upid_temp;
			break;
		case 'top':
			$upid = 0;
	}
	// $cids variable is declared here but seems unused later if $type is not 'sibling' or $conf['cid'] is not a comma-separated list.
	// This part of the logic might need review for its intended purpose if $cids is meant to be used more broadly.
	// For now, just ensuring it's declared if the conditions are met.
	if (isset($conf['cid']) && strpos($conf['cid'], ',') !== false && $type && $type == 'sibling') {
		$cids = explode(',', $conf['cid']); //分割数组,多分类写法
	}

	$filtered_cate_arr = array();
	foreach ($cate_arr as $k => $v_val) { // Changed to $v_val to avoid conflict if $v is used later by reference
		if (!is_array($v_val) || !isset($v_val['upid']) || !isset($v_val['mid']) || !isset($v_val['cid']) || !isset($v_val['alias'])) {
			continue; // Skip malformed category entries
		}

		if ($v_val['upid'] != $upid || $v_val['mid'] != $mid) {
			// unset($cate_arr[$k]); // Modifying array during iteration can be problematic, build a new one
			continue;
		}

		$v_val['url'] = $run->category->category_url($v_val['cid'], $v_val['alias']);

		//多分类同时输出，只支持同级 'sibling'
		if ($type == 'sibling') {
			if (!empty($conf['cid'])) {
				// Ensure $cids is defined from the earlier block, or re-evaluate $conf['cid'] here if it's specific to this loop context
				$cid_list_from_conf = isset($cids) ? $cids : (strpos($conf['cid'], ',') !== false ? explode(',', $conf['cid']) : array($conf['cid']));
				if (in_array($v_val['cid'], $cid_list_from_conf) == false) {
					// unset($cate_arr[$k]);
					continue;
				}
			}
		}
		$filtered_cate_arr[$k] = $v_val;
	}
	$cate_arr = $filtered_cate_arr; // Assign filtered array back

	if ($tyname && isset($_cfg['link_type_page_pre'])) { // Added isset for $_cfg key
		$cate_arr['tyname'] = $_cfg['link_type_page_pre']; //是否设置
	}

	//var_dump($cate_arr);
	return $cate_arr;
}
/**
 * 详情内容页
 * @param string dateformat 时间格式
 * @param int show_prev_next 显示上下翻页
 * @return array
 */
function kp_block_global_show($conf) {
	global $run, $_show;
	
	$cid = isset($conf['cid']) ? intval($conf['cid']) : (isset($_GET['cid']) ? intval($_GET['cid']) : 0);
	$dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat'];
	$show_prev_next = isset($conf['show_prev_next']) && (int)$conf['show_prev_next'] ? true : false;
	$titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0;
	$intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0;
	// 排除单页模型
	$mid = &$run->_var['mid'];
	
	if($mid == 1) return FALSE;
	
	// 初始模型表名
	$run->cms_content_data->table = 'cms_'.$run->_var['table'].'_data';
	// 合并大数据字段
	$id = &$_show['id'];


	//处理媒体资源标签
	if(isset($_show['media'])){
		$_show['media'] = rtrim(rtrim($_show['media'],'##<-P->'));//删除末端字符;再删除末端出现的空格等符号;

		if(strpos($_show['media'],'<-P->') !== false){
			$_show['media'] = explode("<-P->",strip_tags($_show['media'],'##<-P->'));//分割成数组==多个下载地址;
			$_show['depth'] = 2;
			foreach($_show['media'] as $iii => $group ) //下载地址组，二维数组
			{
				$server_play = Exdownurl($group,$_show['depth'],$iii);
				$_show['media'][$iii]= $server_play;
			}
			
		}else{
			$_show['depth'] = 1;
			$_show['media'] = Exdownurl($_show['media'],$_show['depth']);
			
		}
	}
	//处理在线播放资源标签
	if(isset($_show['olmedia'])){
		$play_pre = empty($run->_cfg['link_show_play_pre']) ? 'playid' : $run->_cfg['link_show_play_pre'];//播放页前缀
		$_show['olmedia'] = rtrim(rtrim($_show['olmedia'],'#tw#'));//删除末端分割符
		if(strpos($_show['olmedia'],'###') !== false){
			$_show['olmedia'] = explode('#tw#',strip_tags($_show['olmedia'],'##'));
			$olplay = array();
			$zu_kk = 1;
			
			foreach( $_show['olmedia'] as $k => $player_group )
			{
				$ji_kk = 1;
				$play_ji = explode('###',$player_group);//分割播放器 标注播放器 $play_ji[0] 播放器名称
				$olplay['bfq'] = $play_ji[0];//播放器
				$olplay['jihe'] = explode('##',$play_ji[1]);//分割段落 播放地址和备注
				$newplay = array();
				foreach( $olplay['jihe'] as $kk => $jiji )
				{	
					$play_arr = explode('$',$jiji);//分割名称备注 标注
					$play_arr[0] = preg_replace('#\s#','',$play_arr[0]);//去除掉空格
					if(!empty($play_arr[0])){
						$play_name = $play_arr[0];
						$play_url = $play_arr[1];
						$newplay['ji_'.$ji_kk] = array('url'=>$play_url,'name'=>$play_name,'ji_id'=>$ji_kk);
						$ji_kk += 1;
					}
					
					
				}
				$_show['olmedia']['play_zu'][] = array("play_con" =>$player_group,'play_ji'=>$newplay,'play_server'=>$olplay['bfq'],'play_zuid'=>$zu_kk);
				$_show['olmedia']['zu_total'] = $zu_kk;
				$zu_kk += 1;
			}
		}
	}
	
	//每次访问就增加一次浏览量
	$run->cms_content_views->views_insert($id);
	
	//调用浏览量数据
	$viewarr = $run->cms_content_views->mget(array('id'=>$id));
	$key = "cms_article_views-id-";
	$_show['views'] = $viewarr[$key.$id]["views"];
	
	// 格式化
	$run->cms_content->format($_show, $mid, $dateformat,$titlenum,$intronum);
	
	$_show['comment_url'] = $run->cms_content->comment_url($run->_var['cid'], $id);
	$_show['views_url'] = $run->_cfg['webdir'].'index.php?u=views--cid-'.$run->_var['cid'].'-id-'.$id;
	$data = $run->cms_content_data->read($id);
	if($data) $_show += $data;

	// 提示：文章模型没有图集
	if(isset($_show['images'])) {
		$_show['images'] = (array)_json_decode($_show['images']);
		foreach($_show['images'] as &$v) {
			$v['big'] = $run->_cfg['webdir'].$v['big'];
			$v['thumb'] = $run->_cfg['webdir'].$v['thumb'];
		}
	}

	// 显示上下翻页 (大数据站点建议关闭)
	if($show_prev_next) {
		// 上一页
		$_show['prev'] = $run->cms_content->find_fetch(array('cid' => $run->_var['cid'], 'id'=>array('<'=> $id)), array('id'=>-1), 0 , 1);
		$_show['prev'] = array_pop($_show['prev']);
		$run->cms_content->format($_show['prev'], $mid, $dateformat);

		// 下一页
		$_show['next'] = $run->cms_content->find_fetch(array('cid' => $run->_var['cid'], 'id'=>array('>'=> $id)), array('id'=>1), 0 , 1);
		$_show['next'] = array_pop($_show['next']);
		$run->cms_content->format($_show['next'], $mid, $dateformat);
	}

	
	
	return $_show;
}


//分解下载地址
//downid 下页页面分组ID值
function  Exdownurl($media,$depth = 1,$downid = 0){
	global $run;
	$server_arr = explode("##",strip_tags($media,'##'));//分割成数组;
	$down_pre = empty($run->_cfg['link_show_down_pre']) ? 'downid' : $run->_cfg['link_show_down_pre'];//下载页前缀
	
	foreach($server_arr as $key => $value )
	{
		$media_arr = explode('$',$value);//分割成数组
		$medianame = $media_arr[0];//名称
		$mediaurl = isset($media_arr[1])?$media_arr[1]:'';//地址
		$media_vpath = $mediaurl;
		if(empty($medianame) && !empty($mediaurl)){
			$medianame = "第".$key ++."集";
		}
			
		//判断链接的属性
		if(strpos($mediaurl,'ed2k://') !== false){
			$mediahead = "ed2k";
		}elseif(strpos($mediaurl,'magnet') !== false){
			$mediahead = "magnet";
		}elseif(strpos($mediaurl,'torrent') !== false){
			$mediahead = "torrent";
		}elseif(strpos($mediaurl,'pan.baidu.com') !== false){
			$mediahead = "yunpan";
		}else{
			$mediahead = "thunder";
		}
		if(strpos($mediaurl,"thunder://") === false){
			if(strpos($mediaurl,'ed2k://') !== false){
			   $mediaurl = ThunderEncode(Regname($mediaurl));//重命名并转码
			}elseif(strpos($mediaurl,'magnet') !== false){
			   $mediaurl = Regname($mediaurl);//重命名
			}elseif(strpos($mediaurl,'pan.baidu.com') !== false){
			   $mediaurl = $media_vpath;//不用转换
			}else{
			   $mediaurl = ThunderEncode($mediaurl);//重命名并转码
			}
		}else{
			$newurl = Xurl($mediaurl);//解码地址
			if(strpos($newurl,'ed2k://')){ //查找是否存在ed2k等
			   $mediaurl = ThunderEncode(Regname($newurl));//重命名并转码
			}
		}
		if($depth === 1){
			$server_arr[$key] = array("media_con" =>$value,"media_name" =>$medianame, "media_url" =>$mediaurl,"media_head"=>$mediahead,$down_pre=>$key,'media_playurl'=>$media_vpath);
		}
		if($depth === 2){
			$server_arr[$key] = array("media_con" =>$value,"media_name" =>$medianame, "media_url" =>$mediaurl,"media_head"=>$mediahead,$down_pre=>$depth.'_'.$key,'media_playurl'=>$media_vpath);
		}
		
		
	}
	return $server_arr;
	
}
//判断数组维度
function array_depth($array) {
    if(!is_array($array)) return 0;
    $max_depth = 1;
    foreach ($array as $value) {
        if (is_array($value)) {
            $depth = array_depth($value) + 1;

            if ($depth > $max_depth) {
                $max_depth = $depth;
            }
        }
    }
    return $max_depth;
}

	//迅雷URL解码
function Xurl($url){
	$urlodd=explode('//',$url,2);//分割成数组
	$urlhead=strtolower($urlodd[0]);//头部字符串
	$behind=$urlodd[1];//
    $url=substr(base64_decode($behind),2,-2);
    return $url;
}
 	//带[]文件名 重命名
function Regname($url){
	global $run;
	if(strpos($url,'xunleicang.cc') === false){ //不存在，才开始重命名
		if(strpos($url,'[') !== false && strpos($url,']') !== false){
	  		$pattern = '/\[[\s\S]*?\]/';
	 		$url = preg_replace($pattern,'['.$run->_cfg['webdomain'].']',$url);
	 		//var_dump($run->_cfg['webdomain']);
		}
		if(strpos($url,'%5b') !== false && strpos($url,'%5d') !== false){
	  		$pattern = '/\%5b[\s\S]*?\%5d/';
	  		$url = preg_replace($pattern,'%5b'.$run->_cfg['webdomain'].'%5d',$url);
		}
	}
	
	return $url;
}

	//添加迅雷转换函数
function ThunderEncode($url) 
{
		$thunderPrefix="AA";
		$thunderPosix="ZZ";
		$thunderTitle="thunder://";
		$thunderUrl=$thunderTitle.base64_encode($thunderPrefix.$url.$thunderPosix);
		return $thunderUrl;
}
$gdata = kp_block_global_show(array (
  'show_prev_next' => '1',
  'intronum' => '230',
  'dateformat' => 'Y-m-d',
));
/**
 * 相关内容模块 (只能用于内容页)
 * @param int titlenum 标题长度
 * @param int intronum 简介长度
 * @param string dateformat 时间格式
 * @param int type 相关内容类型 (1为显示第一个tag相关内容，2为随机显示一个tag相关内容)
 * @param int orderway 降序(-1),升序(1)
 * @param int start 开始位置
 * @param int limit 显示几条
 * @return array
 */
function kp_block_taglike($conf) {
	global $run, $_show;


	if(empty($_show['tags'])) return array('list'=> array());

	$titlenum = isset($conf['titlenum']) ? (int)$conf['titlenum'] : 0;
	$intronum = isset($conf['intronum']) ? (int)$conf['intronum'] : 0;
	$dateformat = empty($conf['dateformat']) ? 'Y-m-d H:i:s' : $conf['dateformat'];
	$type = max(1, _int($conf, 'type'));
	$orderway = isset($conf['orderway']) && $conf['orderway'] == 1 ? 1 : -1;
	$start = _int($conf, 'start');
	$limit = _int($conf, 'limit', 10);

	$mid = &$run->_var['mid'];
	$table = &$run->_var['table'];
	if($type == 2) {
		$tagid = array_rand($_show['tags']);
	}else{
		$tagid = key($_show['tags']);
	}

	// 读取内容ID
	$run->cms_content_tag_data->table = 'cms_'.$table.'_tag_data';
	$tag_arr = $run->cms_content_tag_data->find_fetch(array('tagid'=>$tagid), array('id'=>$orderway), $start, $limit);
	$keys = array();
	foreach($tag_arr as $v) {
		$keys[] = $v['id'];
	}

	// 读取内容列表
	$run->cms_content->table = 'cms_'.$table;
	$list_arr = $run->cms_content->mget($keys);
	foreach($list_arr as &$v) {
		$run->cms_content->format($v, $mid, $dateformat, $titlenum, $intronum);
	}


	return array('list'=> $list_arr);
}
?><!doctype html>
<html lang="zh-cn">

<head>
    <meta charset="UTF-8" />
    <title><?php if($tw_var['upid'] == 1){$catname="_高清电影免费在线观看_"; $ss="电影完整版"; }elseif($tw_var['upid'] == 2){ $catname = "_全集电视剧免vip高清不卡在线观看_"; $ss="电视剧全集";}elseif($tw_var['cid'] == 3){ $catname = "动画片_全集高清在线观看_";$ss="最新熟肉H番"; }else{ $catname = "高清电影_最新大片免费版在线观看_";} echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>_<?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); echo(isset($catname) ? $catname : ''); ?>63影视</title>
    <meta name="keywords" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>高清<?php echo(isset($ss) ? $ss : ''); ?>,<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($gdata['subtitle']) ? $gdata['subtitle'] : ''); echo(isset($ss) ? $ss : ''); ?>在线播放,<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($ss) ? $ss : ''); ?>在线播放,<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($ss) ? $ss : ''); ?>免费观看">
    <meta name="description" content="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($ss) ? $ss : ''); ?>在线观看,<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>免费观看,<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>手机在线播放,影片讲述<?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?>">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta http-equiv="Cache-Control" content="no-transform">
<meta http-equiv="Cache-Control" content="no-siteapp">
<meta name="applicable-device" content="pc,mobile">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
<!-- 引用前端 Tailwind CSS -->
<link rel="stylesheet" href="/static/css/output.css">
<!-- 移动端优化脚本 -->
<script src="/static/js/jquery.min1.11.3.js"></script>
<script>
// 移动端适配
(function() {
    // 禁用双击缩放
    var lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        var now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // 移动端菜单切换
    window.toggleMobileMenu = function() {
        var menu = document.getElementById('mobile-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    };
})();

// 侧边栏控制脚本 - 增强版
(function() {
    console.log('侧边栏脚本开始加载...');

    // 侧边栏控制函数
    function toggleSidebar() {
        console.log('toggleSidebar 被调用');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        console.log('sidebar元素:', sidebar);
        console.log('overlay元素:', overlay);

        if (sidebar && overlay) {
            const isHidden = sidebar.classList.contains('-translate-x-full');
            console.log('当前侧边栏状态 - 隐藏:', isHidden);

            if (isHidden) {
                // 显示侧边栏
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                console.log('显示侧边栏');
            } else {
                // 隐藏侧边栏
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                console.log('隐藏侧边栏');
            }
        } else {
            console.error('找不到侧边栏或遮罩元素');
        }
    }

    function toggleSubmenu(menuId) {
        console.log('toggleSubmenu 被调用，menuId:', menuId);
        const menu = document.getElementById(menuId);
        const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

        if (menu) {
            menu.classList.toggle('hidden');
            console.log('切换子菜单:', menuId);
        }
        if (arrow) {
            arrow.classList.toggle('rotate-180');
        }
    }

    // 绑定事件的函数
    function bindEvents() {
        console.log('开始绑定事件...');

        // 移动端菜单按钮
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('移动端菜单按钮被点击');
                toggleSidebar();
            });
            console.log('移动端菜单按钮事件已绑定');
        } else {
            console.warn('找不到移动端菜单按钮');
        }

        // 关闭侧边栏按钮
        const closeSidebarBtn = document.getElementById('close-sidebar');
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('关闭按钮被点击');
                toggleSidebar();
            });
            console.log('关闭按钮事件已绑定');
        } else {
            console.warn('找不到关闭侧边栏按钮');
        }

        // 遮罩层点击
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('遮罩层被点击');
                toggleSidebar();
            });
            console.log('遮罩层事件已绑定');
        } else {
            console.warn('找不到侧边栏遮罩');
        }

        // 键盘ESC关闭侧边栏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                if (sidebar && overlay && !sidebar.classList.contains('-translate-x-full')) {
                    console.log('ESC键关闭侧边栏');
                    toggleSidebar();
                }
            }
        });
        console.log('键盘事件已绑定');
    }

    // 多种方式确保事件绑定
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindEvents);
    } else {
        bindEvents();
    }

    // 备用绑定方式
    setTimeout(bindEvents, 100);
    setTimeout(bindEvents, 500);

    // 全局函数，供onclick使用
    window.toggleSubmenu = toggleSubmenu;
    window.toggleSidebar = toggleSidebar;

    console.log('侧边栏脚本加载完成');
})();
</script>
</head>

<body>
    <div class="layout"><!-- PC端和移动端响应式导航栏 -->
<header class="bg-white shadow-md">
    <!-- 顶部区域 -->
    <div class="headtop bg-gray-50 py-2">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="logo">
                    <a href="https://www.63ys.com" class="flex items-center">
                        <img src="<?php echo(isset($tw['tpl']) ? $tw['tpl'] : ''); ?>images/logo.png" alt="63影视" title="63影视" class="h-8" />
                    </a>
                </div>

                <!-- 右侧搜索和链接 -->
                <div class="flex items-center space-x-4">
                    <!-- PC端顶部链接 -->
                    <div class="hidden lg:flex items-center space-x-4 text-sm text-gray-600">
                        <a href="https://www.63ys.com/type.html" title="影片筛选" target="_blank" class="hover:text-blue-600">影片筛选</a>
                        <a href="https://www.63ys.com/tag-top/" target="_blank" class="hover:text-blue-600">关键词库</a>
                        <a href="https://www.63ys.com" title="加入收藏" class="hover:text-blue-600">收藏本站</a>
                    </div>

                    <!-- 搜索框 -->
                    <div class="header_search">
                        <form id="search_form" method="get" action="/index.php" class="flex">
                            <input type="hidden" name="u" value="search-index" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 hidden sm:block"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索影片..." />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 sm:hidden"
                                   type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" autocomplete="off" placeholder="搜索..." />
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </form>
                    </div>

                    <!-- 移动端菜单按钮 -->
                    <div class="lg:hidden">
                        <button id="mobile-menu-btn" class="p-2 text-gray-700 hover:text-blue-600 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PC端主导航栏 -->
    <div class="layout bg-blue-600 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu">
                <ul class="flex">
                    <li class="hover:bg-blue-700 <?php if (empty($tw_var['topcid'])) { ?>bg-blue-800<?php } ?>">
                        <a href="https://www.63ys.com/" class="block px-6 py-3 text-white hover:text-blue-100">首页</a>
                    </li>
                    <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="hover:bg-blue-700 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'bg-blue-800'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-6 py-3 text-white hover:text-blue-100"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                    <?php unset($data); ?>
                </ul>
            </div>
        </div>
    </div>

    <!-- PC端子导航栏 -->
    <div class="layout border-b border-gray-200 bg-gray-50 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu_sub py-2">
                <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                <ul class="flex flex-wrap">
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <li class="mr-6 mb-1 <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'font-semibold'; } ?>">
                        <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="text-gray-700 hover:text-blue-600 text-sm"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    </li>
                    <?php }} ?>
                </ul>
                <?php unset($data); ?>

                <?php $data = kp_block_category(array (
  'tyname' => '1',
)); ?>
                <ul class="flex flex-wrap mt-2 pt-2 border-t border-gray-200">
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_色情__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">色情</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_情色__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">情色</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_理论__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">理论</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_限制__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">限制</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_三级__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">三级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_禁片__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">禁片</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_R级__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">R级</a></li>
                    <li class="mr-4 mb-1"><a href="/<?php echo(isset($data['tyname']) ? $data['tyname'] : ''); ?>1_黄色__.html" target="_blank" class="text-gray-500 hover:text-red-600 text-xs">黄色</a></li>
                </ul>
                <?php unset($data); ?>
            </div>
        </div>
    </div>
</header>

<!-- 移动端侧边栏遮罩 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

<!-- 移动端侧边栏 -->
<div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden">
    <div class="p-6">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">63</span>
                </div>
                <span class="text-xl font-bold">63影视</span>
            </div>
            <button id="close-sidebar" class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center hover:bg-teal-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- 移动端搜索框 -->
        <div class="mb-6">
            <form method="get" action="/index.php" class="relative">
                <input type="hidden" name="u" value="search-index" />
                <input type="text" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>" placeholder="搜索..." class="w-full bg-gray-800 text-white pl-4 pr-10 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-400 hover:text-teal-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </form>
        </div>

        <!-- 移动端导航菜单 -->
        <nav class="space-y-2">
            <!-- 首页 -->
            <a href="https://www.63ys.com/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors <?php if (empty($tw_var['topcid'])) { ?>bg-gray-800 text-white<?php } ?>">
                首页
            </a>

            <!-- 主分类 -->
            <?php $data = kp_block_category(array (
  'cid' => '1',
  'mid' => '2',
  'type' => 'child',
)); ?>
            <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
            <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'bg-gray-800 text-white'; } ?>">
                <?php echo(isset($v['name']) ? $v['name'] : ''); ?>
            </a>
            <?php }} ?>
            <?php unset($data); ?>

            <!-- 子分类展开菜单 -->
            <div class="mb-4">
                <div class="flex items-center justify-between px-4 py-3 text-white cursor-pointer hover:bg-gray-800 rounded-lg" onclick="toggleSubmenu('subcategory-menu')">
                    <span>更多分类</span>
                    <svg class="w-4 h-4 transition-transform" id="subcategory-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="subcategory-menu" class="ml-4 space-y-1 hidden">
                    <?php $data = kp_block_category(array (
  'cid' => '2',
  'mid' => '2',
  'type' => 'child',
)); ?>
                    <?php if(isset($data) && is_array($data)) { foreach($data as &$v) { ?>
                    <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['name']) ? $v['name'] : ''); ?>" target="<?php echo(isset($v['target']) ? $v['target'] : ''); ?>" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors <?php $cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; } ?>"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a>
                    <?php }} ?>
                    <?php unset($data); ?>
                </div>
            </div>

            <!-- 快捷链接 -->
            <div class="border-t border-gray-700 pt-4 mt-4">
                <a href="https://www.63ys.com/type.html" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                    影片筛选
                </a>
                <a href="https://www.63ys.com/tag-top/" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                    关键词库
                </a>
                <a href="https://www.63ys.com" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors">
                    收藏本站
                </a>
            </div>
        </nav>
    </div>
</div>


        <div class="container">
            <div class="line-middle">
                <div class="xl12 xs12 xm12 float-left">
                    <div class="conpath margin-top">
                        <h3 class="bg-back"><span class="bg-main float-left">当前位置：</span><a
                                href="https://www.63ys.com/">首页</a> > <a href="<?php echo(isset($tw['curr_cateurl']) ? $tw['curr_cateurl'] : ''); ?>"
                                target=_blank><?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); ?></a> >> <?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h3>
                    </div>
                    <!--//conpath-->
                    <div class="txtcon">
                        <div class="bodycon margin-top">
                            <div class="line-middle">
                                <div class="xl4 xs4 xm3">
                                    <div class="bodycon_thumb"><img data-original="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($gdata['pic']) ? $gdata['pic'] : ''); ?>"
                                            class="img-responsive lazypic" alt="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>海报"></div>
                                </div>
                                <div class="xl8 xs8 xm9">
                                    <div class="bodycon_detail">
                                        <h1 class="title"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></h1>
                                        <p><span class="text-back">资源：</span><?php echo(isset($gdata['subtitle']) ? $gdata['subtitle'] : ''); ?></p>
                                        <p><span class="text-back">导演：</span><?php if (!empty($gdata['daoyan_arr'][0]['name'])) { ?>
                                            <?php if(isset($gdata['daoyan_arr']) && is_array($gdata['daoyan_arr'])) { foreach($gdata['daoyan_arr'] as &$vv) { ?><a href="<?php echo(isset($vv['url']) ? $vv['url'] : ''); ?>"
                                                target=_blank><?php echo(isset($vv['name']) ? $vv['name'] : ''); ?></a><?php }} ?> <?php }else{ ?>未知<?php } ?></p>
                                        <p><span class="text-back">主演：</span><?php if (!empty($gdata['yanyuan_arr'][0]['name'])) { if(isset($gdata['yanyuan_arr']) && is_array($gdata['yanyuan_arr'])) { foreach($gdata['yanyuan_arr'] as &$vv) { ?>
                                            <a href="<?php echo(isset($vv['url']) ? $vv['url'] : ''); ?>" target=_blank><?php echo(isset($vv['name']) ? $vv['name'] : ''); ?></a>&nbsp;<?php }} }else{ ?>未知<?php } ?>
                                        </p>
                                        <p><span class="text-back">类型：</span><a href="<?php echo(isset($tw['curr_cateurl']) ? $tw['curr_cateurl'] : ''); ?>"
                                                target=_blank><?php echo(isset($tw['curr_catename']) ? $tw['curr_catename'] : ''); ?></a>
                                            <?php if (isset($gdata['tag_arr'])) { if(isset($gdata['tag_arr']) && is_array($gdata['tag_arr'])) { foreach($gdata['tag_arr'] as &$v) { ?> <a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"
                                                target=_blank><?php echo(isset($v['name']) ? $v['name'] : ''); ?></a><?php }} } ?><span
                                                class="split-line hidden-s"></span></p>
                                        <p><span class="text-back">年份：</span><?php if (!empty($gdata['year'])) { ?><a
                                                href="<?php echo(isset($gdata['year_url']) ? $gdata['year_url'] : ''); ?>"
                                                target=_blank><?php echo(isset($gdata['year']) ? $gdata['year'] : ''); }else{ ?>2022<?php } ?></a></p>
                                        <div class="desc detail hidden-l hidden-s"><span
                                                class="left text-back">简介：</span><span class="pIntro"><?php echo(isset($gdata['intro']) ? $gdata['intro'] : ''); ?> <a
                                                    href="#bodyshow">查看详情>></a></span></div>
                                    </div>
                                </div>
                            </div>
                            <!--//bodycon-->
                            <div class="margin-big-top body_media">
                                <?php if (!empty($gdata['olmedia'])) { ?>
                                <?php if (!empty($gdata['ismedia'])) { ?>
                                <p class="badge-first text-center padding-top padding-bottom">
                                    <strong>版权限制，删除资源</strong>
                                </p>
                                <?php }else{ ?>
                                <?php  $xl =1; $dd=1; ?>
                                <?php if(isset($gdata['olmedia']['play_zu']) && is_array($gdata['olmedia']['play_zu'])) { foreach($gdata['olmedia']['play_zu'] as &$zuzu) { ?>
                                <h3><span class="float-right text-right padding-right">无需插件即可点播</span><i
                                        class="icon icon-play">&nbsp;</i>高清在线观看 高速云播【<?php echo(isset($dd) ? $dd : ''); ?>】</h3>
                                <div class="panel-body">
                                    <ul class="list-unstyle"></ul>
                                    <?php if (empty($zuzu['play_ji'])) { ?>
                                    <p class="badge-first text-center padding-top padding-bottom">
                                        抱歉！因版权或无资源等情况无法显示，请耐心等待小编进行整理更新 </p>
                                    <?php }else{ ?>
                                    <?php if(isset($zuzu['play_ji']) && is_array($zuzu['play_ji'])) { foreach($zuzu['play_ji'] as &$vv) { ?>
                                    <li><a href="<?php echo(isset($vv['play_url']) ? $vv['play_url'] : ''); ?>" target='_blank'
                                            title="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($vv['name']) ? $vv['name'] : ''); ?>"><?php echo(isset($vv['name']) ? $vv['name'] : ''); ?></a></li>
                                    <?php }} ?>
                                    <?php } ?>
                                </div><?php  $dd++;  ?>
                            </div><?php } ?>
                            <!--//body_media--><?php }} } ?>
                            <?php if (!empty($gdata['media'][0]['media_name'])) { ?>
                            <div class="downurl margin-top" name="downs" id="downs">
                                <h3 class="icon-download"> 《<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>》迅雷下载资源 BT种子</h3>
                                <div class="hidden-s hidden-l">
                                    <h5 class="margin-top border-bottom"><span>图标提示：</span><span class="padding-left"><i
                                                class="iIcon ed2k_ico"></i>电驴ed2k资源</span><span class="padding-left"><i
                                                class="iIcon magnet_ico"></i>磁力链资源</span><span class="padding-left"><i
                                                class="iIcon torrent_ico"></i>BT种子资源</span><span class="padding-left"><i
                                                class="iIcon thunder_ico"></i>迅雷下载资源</span></h5>
                                    <p>温馨提示：由于版权原因，迅雷下载软件可能存在资源等无法下载，请更换其它下载软件！</p>
                                </div>
                                <div class="downmedia">
                                    <ul class="margin-top">
                                        <script src="/static/js/down.js"></script><?php if (!empty($gdata['ismedia'])) { ?>
                                        <p class="bg-green-light radius-small text-center padding-small text-main">
                                            <strong>版权限制，暂无资源</strong>
                                        </p><?php }else{ ?>
                                        <?php if(isset($gdata['media']) && is_array($gdata['media'])) { foreach($gdata['media'] as &$v) { ?>
                                        <?php $i=0; ?>
                                        <?php if (!empty($v['media_name'])) { ?>
                                        <li>
                                            <div class="xl7 xs7 xm9"><i class="iIcon float-left hidden-s hidden-l <?php 
switch ($v['media_head']){
case 'ed2k': echo 'ed2k_ico';
break;
case 'magnet':echo 'magnet_ico';
break;
case 'torrent':echo 'torrent_ico';
break;
case 'thunder':echo 'thunder_ico';
break;
default: echo 'thunder_ico';
}  ?>"></i>
                                                <input type="checkbox" name="down_url_list_0"
                                                    class="down_url float-left" value="<?php echo(isset($v['media_url']) ? $v['media_url'] : ''); ?>"
                                                    file_name="<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($v['media_name']) ? $v['media_name'] : ''); ?>[63影视 www.63ys.com].mp4" /><strong
                                                    class="down_part_name float-left"><a href="<?php echo(isset($v['media_url']) ? $v['media_url'] : ''); ?>"
                                                        title="<?php echo(isset($v['media_name']) ? $v['media_name'] : ''); ?>" target="_blank"><em
                                                            class="hidden-s hidden-l"><?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?></em>
                                                        <?php echo(isset($v['media_name']) ? $v['media_name'] : ''); ?></a></strong>
                                            </div>
                                            <div class="xl5 xs5 xm3"><span><label class="thunder_down_re"><a
                                                            href="<?php echo(isset($v['media_url']) ? $v['media_url'] : ''); ?>" rel="nofollow"
                                                            target="_blank">迅雷下载</a></label><label class="kk"><a
                                                            href="javascript:void(0)" rel="nofollow"
                                                            onclick="kkPlay('<?php echo(isset($v['media_url']) ? $v['media_url'] : ''); ?>','<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); echo(isset($v['media_name']) ? $v['media_name'] : ''); ?>_[63影视 www.63ys.com].mp4');"
                                                            target="_self">看看播放</a></label></span></div>
                                        </li><?php } }} ?>
                                        <?php if(isset($gdata['clouddisk']) && is_array($gdata['clouddisk'])) { foreach($gdata['clouddisk'] as &$v) { ?>
                                        <?php if (!empty($v['cloud_name'])) { ?>
                                        <li>
                                            <div class="xl9 xs9 xm8"><i
                                                    class="iIcon pan_ico float-left hidden-s hidden-l "></i><span
                                                    class="float-left"><a href="<?php echo(isset($v['yun_url']) ? $v['yun_url'] : ''); ?>" rel="nofollow"
                                                        target="_blank"><cite
                                                            class="hidden-s hidden-l">《<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>》</cite>百度网盘
                                                        &<?php echo(isset($v['cloud_name']) ? $v['cloud_name'] : ''); ?></a></span></div>
                                            <div class="xl3 xs3 xm3"><span><label class="float-right"><a
                                                            href="<?php echo(isset($v['yun_url']) ? $v['yun_url'] : ''); ?>" rel="nofollow"
                                                            target="_blank">转存网盘</a></label></span></div>
                                        </li><?php } ?>
                                        <?php }} ?>
                                        <?php } ?>
                                    </ul>
                                </div>
                                <!--//downmedia-->
                                <div class="downtools box hidden-s hidden-l"><input id="allcheck1" onclick="CheckAll"
                                        type="checkbox" name="checkall"><em>全选</em><a href="javascript:void(0);"
                                        target="_self" class="thunder_down_all">迅雷批量下载</a><a href="javascript:void(0);"
                                        target="_self" class="xf_down_all">旋风批量下载</a></div>
                            </div>
                            <!--//downurl-->
                            <?php } ?>
                        </div>
                        <!--//bodycon-->
                    </div>
                    <!--//txtcon-->
                    <div class="margin-top body_detail ">
                        <h3>《<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>》详细介绍</h3>
                        <div class="panel-body">
                            <div class="margin-top" id="bodyshow">
                                <p><?php echo(isset($gdata['content']) ? $gdata['content'] : ''); ?></p>
                            </div>
                            <div id="gradient"></div>
                        </div>
                    </div>
                    <!--//body_detail--><a href="javascript:;" id="read-more"></a>
                    <div class="related_con margin-top">
                        <h3>《<?php echo(isset($gdata['title']) ? $gdata['title'] : ''); ?>》相似影片</h3>
                        <div class="line margin-top"><?php $data = kp_block_taglike(array (
  'type' => '2',
  'limit' => '12',
)); ?>
                            <?php if(isset($data['list']) && is_array($data['list'])) { foreach($data['list'] as &$v) { ?>
                            <div class="xl4 xs3 xm2 margin-bottom">
                                <div class="media"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>" target="_blank"><img
                                            data-original="<?php echo(isset($tw['up_img_url']) ? $tw['up_img_url'] : ''); echo(isset($v['pic']) ? $v['pic'] : ''); ?>" class="lazypic"
                                            alt="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>海报"></a>
                                    <p class="media-body text-small"><a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" title="<?php echo(isset($v['title']) ? $v['title'] : ''); ?>"
                                            target="_blank"><?php echo(isset($v['subject']) ? $v['subject'] : ''); ?></a></p>
                                </div>
                            </div>
                            <!--//xl2--><?php }} ?>
                            <?php unset($data); ?>
                        </div>
                    </div>
                    <!--//related_con-->
                    
                </div>
            </div>
            <!--//line_middle-->
        </div>
        <!--//container-->
    </div>
    <!--//layout-->
    <div class="layout"><!-- 底部 -->
<footer class="bg-gray-900 text-gray-300">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <!-- 主要底部内容 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <!-- 网站信息 -->
            <div>
                <div class="flex items-center space-x-2 mb-4">
                    <img src="/kbcms/view/63ys/images/logo.png" alt="63影视" class="h-8 w-auto">
                    <span class="text-xl font-bold text-white">63影视</span>
                </div>
                <p class="text-sm text-gray-400 leading-relaxed mb-4">
                    提供最新最热门的电影、电视剧、动漫、综艺节目免费在线观看，支持手机观看，高清流畅，更新及时。
                </p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
                        </svg>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.347-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.878-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001 12.017.001z"/>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- 快速链接 -->
            <div>
                <h3 class="text-white font-semibold mb-4">快速导航</h3>
                <ul class="space-y-2 text-sm">
                    <li><a href="/" class="hover:text-white transition-colors">首页</a></li>
                    <li><a href="/type/1.html" class="hover:text-white transition-colors">最新电影</a></li>
                    <li><a href="/type/2.html" class="hover:text-white transition-colors">最新电视剧</a></li>
                    <li><a href="/type/3.html" class="hover:text-white transition-colors">动漫</a></li>
                    <li><a href="/type/4.html" class="hover:text-white transition-colors">综艺</a></li>
                    <li><a href="/search.html" class="hover:text-white transition-colors">搜索</a></li>
                </ul>
            </div>

            <!-- 热门分类 -->
            <div>
                <h3 class="text-white font-semibold mb-4">热门分类</h3>
                <ul class="space-y-2 text-sm">
                    <li><a href="#" class="hover:text-white transition-colors">动作片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">喜剧片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">爱情片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">科幻片</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">韩剧</a></li>
                    <li><a href="#" class="hover:text-white transition-colors">美剧</a></li>
                </ul>
            </div>

            <!-- 联系信息 -->
            <div>
                <h3 class="text-white font-semibold mb-4">关于我们</h3>
                <ul class="space-y-2 text-sm">
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                        <span>中国大陆</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
                        </svg>
                        <span><EMAIL></span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-4 h-4 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span class="leading-relaxed">为用户提供优质的在线观影体验</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 底部版权信息 -->
        <div class="border-t border-gray-800 pt-6">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="text-sm text-gray-400 text-center md:text-left">
                    <p>&copy; 2024 63影视. 保留所有权利.</p>
                    <p class="mt-1">本站为非营利性站点，所有内容均来源于互联网，如有侵权请联系删除。</p>
                </div>
                <div class="flex space-x-6 text-sm">
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">隐私政策</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">使用条款</a>
                    <a href="#" class="text-gray-400 hover:text-white transition-colors">联系我们</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-blue-600 text-white p-3 rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 invisible" onclick="scrollToTop()">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>
</footer>

<script>
// 回到顶部功能
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// 显示/隐藏回到顶部按钮
window.addEventListener('scroll', function() {
    const backToTopButton = document.getElementById('back-to-top');
    if (window.pageYOffset > 300) {
        backToTopButton.classList.remove('opacity-0', 'invisible');
        backToTopButton.classList.add('opacity-100', 'visible');
    } else {
        backToTopButton.classList.add('opacity-0', 'invisible');
        backToTopButton.classList.remove('opacity-100', 'visible');
    }
});
</script>
    </div>
    <script>(function () {
            var bp = document.createElement('script');
            var curProtocol = window.location.protocol.split(':')[0];
            if (curProtocol === 'https') {
                bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
            }
            else {
                bp.src = 'http://push.zhanzhang.baidu.com/push.js';
            }
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(bp, s);
        })();
    </script>
</body>

</html>