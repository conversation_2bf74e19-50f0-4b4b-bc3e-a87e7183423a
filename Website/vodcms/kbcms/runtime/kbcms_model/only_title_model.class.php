<?php

/**
 * (C)2019 更新检查，用于API更新做数据对比调用
 * Author: gelinbin.com 格林斌
 */

defined('VODCMS_PATH') or exit;

#[AllowDynamicProperties]
class only_title extends model
{
	function __construct()
	{
		$this->table = 'cms_article';	// 表名
		$this->pri = array('id');	// 主键
	}

	// 检查标题是否已被使用
	public function check_title($title)
	{
		$where = array('title' => $title); //查询相同的数据表
		$relist = $this->find_fetch($where); //得到数组文件

		if (!empty($relist)) {
			return $relist;
		} else {
			return false;
		}
	}
}
