<?php defined('APP_NAME') || exit('Access Denied'); 
?><!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/css/output.css" rel="stylesheet">
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>js/jquery.js" type="text/javascript"></script>
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/js/global.js" type="text/javascript"></script>
	<script type="text/javascript">
		var pKey = "<?php echo(isset($_pkey) ? $_pkey : ''); ?>", urlKey = "<?php echo(isset($_ukey) ? $_ukey : ''); ?>", place = "<?php echo(isset($_place) ? $_place : ''); ?>";
	</script>
	<title><?php echo(isset($_title) ? $_title : ''); ?></title>
	<style>
		/* 统一的现代化页面样式 - 使用设计规范配色 */
		body {
			background: #0f172a;
			/* slate-950 - 统一主背景 */
			min-height: 100vh;
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			color: #e2e8f0;
			/* slate-200 - 次级文本 */
		}

		.modern-container {
			max-width: 1400px;
			margin: 0 auto;
			padding: 20px;
		}

		.modern-card {
			background: #1e293b;
			/* slate-900 - 次级背景 */
			border-radius: 20px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			border: 1px solid #334155;
			/* slate-700 - 主边框 */
			overflow: hidden;
		}

		.modern-header {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 使用统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 - 主文本 */
			padding: 30px;
			text-align: center;
			position: relative;
		}

		.modern-header::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
			opacity: 0.3;
		}

		.modern-header h1 {
			font-size: 2.5rem;
			font-weight: 700;
			margin: 0;
			position: relative;
			z-index: 1;
			color: #f8fafc;
			/* slate-50 */
		}

		.modern-form {
			padding: 40px;
		}

		.form-grid {
			display: grid;
			grid-template-columns: 2fr 1fr;
			gap: 40px;
			align-items: start;
		}

		.form-section {
			background: #334155;
			/* slate-800 - 三级背景 */
			border-radius: 16px;
			padding: 30px;
			border: 1px solid #475569;
			/* slate-600 - 次级边框 */
		}

		.form-group {
			margin-bottom: 25px;
		}

		.form-label {
			display: block;
			font-weight: 600;
			color: #cbd5e1;
			/* slate-300 - 三级文本 */
			margin-bottom: 8px;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}

		.form-label.required::before {
			content: '*';
			color: #ef4444;
			/* red-500 - 错误色 */
			margin-right: 4px;
		}

		.form-input {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #475569;
			/* slate-600 */
			border-radius: 12px;
			font-size: 14px;
			transition: all 0.3s ease;
			background: #334155;
			/* slate-700 */
			color: #f8fafc;
			/* slate-50 */
		}

		.form-input:focus {
			outline: none;
			border-color: #3b82f6;
			/* blue-500 - 激活边框 */
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
			background: #1e293b;
			/* slate-900 */
		}

		.form-input::placeholder {
			color: #94a3b8;
			/* slate-400 - 占位符文本 */
		}

		.form-textarea {
			min-height: 120px;
			resize: vertical;
			font-family: inherit;
		}

		.form-select {
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 12px center;
			background-repeat: no-repeat;
			background-size: 16px;
			padding-right: 40px;
		}

		.checkbox-group {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin-top: 10px;
		}

		.checkbox-item {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 12px;
			background: #475569;
			/* slate-600 */
			border-radius: 8px;
			border: 1px solid #64748b;
			/* slate-500 */
			transition: all 0.3s ease;
			color: #e2e8f0;
			/* slate-200 */
		}

		.checkbox-item:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #3b82f6;
			/* blue-500 */
		}

		.checkbox-item input[type="checkbox"] {
			width: 16px;
			height: 16px;
			accent-color: #3b82f6;
			/* blue-500 */
		}

		.btn-primary {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 */
			border: none;
			padding: 14px 32px;
			border-radius: 12px;
			font-weight: 600;
			font-size: 16px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
		}

		.btn-primary:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
			background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
			/* blue-600 to blue-800 */
		}

		.btn-secondary {
			background: #475569;
			/* slate-600 */
			color: #e2e8f0;
			/* slate-200 */
			border: 2px solid #64748b;
			/* slate-500 */
			padding: 10px 20px;
			border-radius: 8px;
			font-weight: 500;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.btn-secondary:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #94a3b8;
			/* slate-400 */
		}

		.sidebar-section {
			background: #334155;
			/* slate-800 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
			border: 1px solid #475569;
			/* slate-600 */
		}

		.sidebar-title {
			font-weight: 700;
			color: #f8fafc;
			/* slate-50 */
			margin-bottom: 20px;
			font-size: 16px;
			padding-bottom: 10px;
			border-bottom: 2px solid #64748b;
			/* slate-500 */
		}

		.image-upload-area {
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 30px;
			text-align: center;
			background: #334155;
			/* slate-700 */
			transition: all 0.3s ease;
			cursor: pointer;
		}

		.image-upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.image-preview {
			max-width: 100%;
			height: auto;
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.resource-section {
			background: #334155;
			/* slate-700 */
			border: 1px solid #10b981;
			/* emerald-500 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
		}

		.resource-title {
			color: #10b981;
			/* emerald-500 */
			font-weight: 700;
			margin-bottom: 15px;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.resource-title::before {
			content: '🎬';
			font-size: 18px;
		}

		.add-resource-btn {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			color: white;
			border: none;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 12px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.add-resource-btn:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
		}

		.upload-area {
			background: #334155;
			/* slate-700 */
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 20px;
			text-align: center;
			transition: all 0.3s ease;
		}

		.upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.upload-status {
			margin-top: 10px;
			padding: 10px;
			border-radius: 8px;
			background: #334155;
			/* slate-700 */
			font-size: 13px;
		}

		/* 响应式设计 */
		@media (max-width: 1024px) {
			.form-grid {
				grid-template-columns: 1fr;
				gap: 30px;
			}

			.modern-form {
				padding: 30px 20px;
			}
		}

		@media (max-width: 768px) {
			.modern-container {
				padding: 10px;
			}

			.modern-header {
				padding: 20px;
			}

			.modern-header h1 {
				font-size: 2rem;
			}

			.checkbox-group {
				flex-direction: column;
			}
		}

		/* 动画效果 */
		@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translateY(30px);
			}

			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		.modern-card {
			animation: fadeInUp 0.6s ease-out;
		}

		.form-section {
			animation: fadeInUp 0.6s ease-out 0.1s both;
		}

		.sidebar-section {
			animation: fadeInUp 0.6s ease-out 0.2s both;
		}

		/* 隐藏传统样式元素 */
		.tb,
		.th,
		.col,
		.cc,
		.contadd {
			all: unset;
		}

		/* 重置表格样式 */
		table.tb {
			width: 100%;
			border-collapse: separate;
			border-spacing: 0;
		}

		.tb tr {
			display: contents;
		}

		.tb td,
		.tb th {
			display: block;
			width: 100%;
			margin-bottom: 20px;
		}
	</style>

	<!-- 统一对话框样式 -->
	<style>
		.ajaxoverlay {
			position: fixed !important;
			top: 0 !important;
			left: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			z-index: 10000 !important;
			width: 100vw !important;
			height: 100vh !important;
			background: rgba(0, 0, 0, 0.4) !important;
			backdrop-filter: blur(2px) !important;
		}

		.ajaxtips {
			position: fixed !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			z-index: 10001 !important;
			max-width: 90vw !important;
			max-height: 90vh !important;
		}

		.ajaxbox {
			background: #1e293b !important;
			/* slate-800 */
			border: 1px solid #3b82f6 !important;
			/* blue-500 */
			border-radius: 12px !important;
			padding: 24px !important;
			line-height: 1.6 !important;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
			min-width: 320px !important;
			max-width: 500px !important;
			color: #f8fafc !important;
			/* slate-50 */
			font-size: 16px !important;
			display: flex !important;
			flex-direction: column !important;
			align-items: center !important;
			gap: 12px !important;
		}

		.ajaxbox i {
			width: 32px !important;
			height: 32px !important;
			margin: 0 !important;
			flex-shrink: 0 !important;
			background-size: contain !important;
		}

		.ajaxbox b {
			flex: 1 !important;
			font-weight: 500 !important;
			font-size: 16px !important;
			line-height: 1.5 !important;
			margin: 0 !important;
		}

		.ajaxbox u {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
			padding: 8px 16px !important;
			border-radius: 6px !important;
			cursor: pointer !important;
			text-decoration: none !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin-left: 16px !important;
			flex-shrink: 0 !important;
		}

		.ajaxbox u:hover {
			background: #2563eb !important;
			/* blue-600 */
			transform: translateY(-1px) !important;
		}

		/* 成功状态 */
		.btrue {
			border-color: #10b981 !important;
			/* emerald-500 */
			background: #064e3b !important;
			/* emerald-900 */
		}

		.btrue i {
			background: #10b981 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.btrue i::after {
			content: "✓" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.btrue u {
			background: #10b981 !important;
		}

		.btrue u:hover {
			background: #059669 !important;
		}

		/* 错误状态 */
		.bfalse {
			border-color: #ef4444 !important;
			/* red-500 */
			background: #7f1d1d !important;
			/* red-900 */
		}

		.bfalse i {
			background: #ef4444 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bfalse i::after {
			content: "✕" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.bfalse u {
			background: #ef4444 !important;
		}

		.bfalse u:hover {
			background: #dc2626 !important;
		}

		/* 警告状态 */
		.bnote {
			border-color: #f59e0b !important;
			/* amber-500 */
			background: #78350f !important;
			/* amber-900 */
		}

		.bnote i {
			background: #f59e0b !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bnote i::after {
			content: "!" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 20px !important;
			font-weight: bold !important;
		}

		.bnote u {
			background: #f59e0b !important;
		}

		.bnote u:hover {
			background: #d97706 !important;
		}

		/* 加载动画优化 */
		.ajaximg {
			width: 48px !important;
			height: 48px !important;
			background: none !important;
			border: 4px solid #334155 !important;
			border-top: 4px solid #3b82f6 !important;
			border-radius: 50% !important;
			animation: spin 1s linear infinite !important;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		/* 确认框样式优化 */
		.ajaxbox .cf {
			margin-top: 20px !important;
			padding: 0 !important;
			display: flex !important;
			justify-content: center !important;
			gap: 12px !important;
		}

		.ajaxbox .cf .but3 {
			padding: 8px 16px !important;
			border-radius: 6px !important;
			border: none !important;
			cursor: pointer !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin: 0 !important;
			float: none !important;
		}

		.ajaxbox .cf .but3:first-child {
			background: #6b7280 !important;
			/* gray-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:first-child:hover {
			background: #4b5563 !important;
			/* gray-600 */
		}

		.ajaxbox .cf .but3:last-child {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:last-child:hover {
			background: #2563eb !important;
			/* blue-600 */
		}
	</style>
</head>

<body>

<!-- 主题管理页面 -->
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 p-6">
	<div class="max-w-7xl mx-auto space-y-6">

		<!-- 页面头部 -->
		<div class="bg-slate-900 border border-slate-700 rounded-xl shadow-lg overflow-hidden">
			<div class="bg-gradient-to-r from-slate-800 to-slate-700 border-b border-slate-600 px-6 py-4">
				<div class="flex items-center justify-between">
					<div>
						<h1 class="text-2xl font-bold text-slate-50 flex items-center">
							<svg class="w-7 h-7 mr-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-5L9 9h6a2 2 0 012 2v2"/>
							</svg>
							主题管理
						</h1>
						<p class="text-slate-400 mt-1">管理和切换网站前台主题</p>
					</div>

					<!-- 标签页切换 -->
					<div class="flex bg-slate-800 rounded-lg p-1 border border-slate-600">
						<button class="theme-tab active px-4 py-2 rounded-md text-sm font-medium transition-all duration-200" data-tab="local">
							<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
							</svg>
							本地主题
						</button>
						<button class="theme-tab px-4 py-2 rounded-md text-sm font-medium transition-all duration-200" data-tab="online">
							<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
							</svg>
							在线获取
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 本地主题内容 -->
		<div id="local-themes" class="tab-content">
			<?php if (!$themes) { ?>
			<div class="bg-slate-900 border border-slate-700 rounded-xl p-8 text-center">
				<div class="w-16 h-16 bg-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
					<svg class="w-8 h-8 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
					</svg>
				</div>
				<h3 class="text-lg font-semibold text-slate-200 mb-2">暂无主题</h3>
				<p class="text-slate-400">请从在线获取页面下载主题或手动上传主题文件</p>
			</div>
			<?php }else{ ?>
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
				<?php if(isset($themes) && is_array($themes)) { foreach($themes as $k=>&$v) { ?>
				<?php  $enable = ($k == $theme);  ?>
				<div class="theme-card bg-slate-900 border border-slate-700 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:border-purple-500/50 group <?php if ($enable) { ?>current-theme<?php } ?>">

					<!-- 主题预览图 -->
					<div class="relative h-48 overflow-hidden">
						<div class="theme-preview absolute inset-0 bg-cover bg-center bg-slate-800 transition-transform duration-500 group-hover:scale-105"
							 style="background-image: url('../<?php echo(isset($core) ? $core : ''); ?>/view/<?php echo(isset($k) ? $k : ''); ?>/show.jpg');">
							<div class="absolute inset-0 bg-gradient-to-t from-slate-900/90 via-transparent to-transparent"></div>
						</div>

						<!-- 当前使用标志 -->
						<?php if ($enable) { ?>
						<div class="absolute top-3 right-3">
							<span class="bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center">
								<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								当前使用
							</span>
						</div>
						<?php } ?>

						<!-- 详细信息悬停显示 -->
						<div class="theme-details absolute inset-0 bg-slate-900/95 p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-center">
							<div class="text-sm text-slate-300 space-y-2">
								<div class="flex items-center">
									<svg class="w-4 h-4 mr-2 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
									</svg>
									版本：<?php echo(isset($v['version']) ? $v['version'] : ''); ?>
								</div>
								<div class="flex items-center">
									<svg class="w-4 h-4 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
									</svg>
									更新：<?php echo(isset($v['update']) ? $v['update'] : ''); ?>
								</div>
								<div class="text-slate-400 text-xs leading-relaxed mt-3">
									<?php echo(isset($v['brief']) ? $v['brief'] : ''); ?>
								</div>
							</div>
						</div>
					</div>

					<!-- 主题信息 -->
					<div class="p-4">
						<h3 class="text-lg font-semibold text-slate-100 mb-1 group-hover:text-purple-300 transition-colors duration-200">
							<?php echo(isset($v['name']) ? $v['name'] : ''); ?>
						</h3>
						<p class="text-sm text-slate-400 mb-1">目录：<?php echo(isset($k) ? $k : ''); ?></p>
						<div class="flex items-center text-xs text-slate-500 mb-4">
							<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
							</svg>
							作者：<a href="<?php echo(isset($v['authorurl']) ? $v['authorurl'] : ''); ?>" target="_blank" class="text-blue-400 hover:text-blue-300 transition-colors"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></a>
						</div>

						<!-- 操作按钮 -->
						<div class="flex gap-2 theme-actions" data-theme="<?php echo(isset($k) ? $k : ''); ?>">
							<?php if ($enable) { ?>
							<span class="flex-1 bg-green-500/20 text-green-400 px-4 py-2 rounded-lg text-sm font-medium text-center border border-green-500/30">
								<svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
								</svg>
								当前使用
							</span>
							<?php }else{ ?>
							<button class="enable-btn flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
								<svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
								</svg>
								启用
							</button>
							<button class="delete-btn bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg">
								<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
								</svg>
							</button>
							<?php } ?>
						</div>
					</div>
				</div>
				<?php }} ?>
			</div>
			<?php } ?>
		</div>

		<!-- 在线获取内容 -->
		<div id="online-themes" class="tab-content hidden">
			<div class="bg-slate-900 border border-slate-700 rounded-xl p-8 text-center">
				<div class="loading-spinner w-16 h-16 border-4 border-slate-600 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
				<h3 class="text-lg font-semibold text-slate-200 mb-2">正在加载在线主题</h3>
				<p class="text-slate-400" id="loading-text">玩命加载中<span id="loading-dots">...</span></p>
			</div>
		</div>
	</div>
</div>

<!-- 统一弹出框样式 -->
<style>
/* 标签页样式 */
.theme-tab {
	color: #94a3b8;
	background: transparent;
	border: none;
	cursor: pointer;
}

.theme-tab.active {
	background: #3b82f6 !important;
	color: white !important;
}

.theme-tab:hover:not(.active) {
	background: #475569 !important;
	color: #e2e8f0 !important;
}

/* 主题卡片动画 */
.theme-card {
	transform: translateY(0);
	transition: all 0.3s ease;
}

.theme-card:hover {
	transform: translateY(-4px);
}

.current-theme {
	border-color: #10b981 !important;
	box-shadow: 0 0 20px rgba(16, 185, 129, 0.3) !important;
}

/* 加载动画 */
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-spinner {
	animation: spin 1s linear infinite;
}

/* 统一弹出框样式 - 从其他页面复制 */
.ajaxoverlay {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	z-index: 10000 !important;
	width: 100vw !important;
	height: 100vh !important;
	background: rgba(0, 0, 0, 0.4) !important;
	backdrop-filter: blur(2px) !important;
}

.ajaxtips {
	position: fixed !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	z-index: 10001 !important;
	max-width: 90vw !important;
	max-height: 90vh !important;
}

.ajaxbox {
	background: #1e293b !important;
	border: 1px solid #3b82f6 !important;
	border-radius: 12px !important;
	padding: 24px !important;
	line-height: 1.6 !important;
	box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
	min-width: 320px !important;
	max-width: 500px !important;
	color: #f8fafc !important;
	font-size: 16px !important;
	display: flex !important;
	align-items: center !important;
	gap: 12px !important;
}

.ajaxbox i {
	width: 32px !important;
	height: 32px !important;
	margin: 0 !important;
	flex-shrink: 0 !important;
	background-size: contain !important;
}

.ajaxbox b {
	flex: 1 !important;
	font-weight: 500 !important;
	font-size: 16px !important;
	line-height: 1.5 !important;
	margin: 0 !important;
}

.ajaxbox u {
	background: #3b82f6 !important;
	color: white !important;
	padding: 8px 16px !important;
	border-radius: 6px !important;
	cursor: pointer !important;
	text-decoration: none !important;
	font-weight: 500 !important;
	transition: all 0.2s !important;
	margin-left: 16px !important;
	flex-shrink: 0 !important;
}

.ajaxbox u:hover {
	background: #2563eb !important;
	transform: translateY(-1px) !important;
}

/* 成功状态 */
.btrue {
	border-color: #10b981 !important;
	background: #064e3b !important;
}

.btrue i {
	background: #10b981 !important;
	border-radius: 50% !important;
	position: relative !important;
}

.btrue i::after {
	content: "✓" !important;
	position: absolute !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	color: white !important;
	font-size: 18px !important;
	font-weight: bold !important;
}

.btrue u {
	background: #10b981 !important;
}

.btrue u:hover {
	background: #059669 !important;
}

/* 错误状态 */
.bfalse {
	border-color: #ef4444 !important;
	background: #7f1d1d !important;
}

.bfalse i {
	background: #ef4444 !important;
	border-radius: 50% !important;
	position: relative !important;
}

.bfalse i::after {
	content: "✕" !important;
	position: absolute !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	color: white !important;
	font-size: 18px !important;
	font-weight: bold !important;
}

.bfalse u {
	background: #ef4444 !important;
}

.bfalse u:hover {
	background: #dc2626 !important;
}

/* 警告状态 */
.bnote {
	border-color: #f59e0b !important;
	background: #78350f !important;
}

.bnote i {
	background: #f59e0b !important;
	border-radius: 50% !important;
	position: relative !important;
}

.bnote i::after {
	content: "!" !important;
	position: absolute !important;
	top: 50% !important;
	left: 50% !important;
	transform: translate(-50%, -50%) !important;
	color: white !important;
	font-size: 20px !important;
	font-weight: bold !important;
}

.bnote u {
	background: #f59e0b !important;
}

.bnote u:hover {
	background: #d97706 !important;
}
</style>

<script type="text/javascript">
// 标签页切换功能
document.addEventListener('DOMContentLoaded', function() {
	// 标签页切换
	const tabs = document.querySelectorAll('.theme-tab');
	const contents = document.querySelectorAll('.tab-content');

	tabs.forEach(tab => {
		tab.addEventListener('click', function() {
			const targetTab = this.dataset.tab;

			// 更新标签页状态
			tabs.forEach(t => t.classList.remove('active'));
			this.classList.add('active');

			// 显示对应内容
			contents.forEach(content => {
				if (content.id === targetTab + '-themes') {
					content.classList.remove('hidden');
				} else {
					content.classList.add('hidden');
				}
			});

			// 如果是在线获取标签页，加载在线内容
			if (targetTab === 'online') {
				loadOnlineThemes();
			}
		});
	});

	// 绑定操作按钮事件
	bindThemeActions();
});

// 绑定主题操作事件
function bindThemeActions() {
	// 启用按钮
	document.querySelectorAll('.enable-btn').forEach(btn => {
		btn.addEventListener('click', function() {
			const theme = this.closest('.theme-actions').dataset.theme;
			enableTheme(theme);
		});
	});

	// 删除按钮
	document.querySelectorAll('.delete-btn').forEach(btn => {
		btn.addEventListener('click', function() {
			const theme = this.closest('.theme-actions').dataset.theme;
			const themeName = this.closest('.theme-card').querySelector('h3').textContent;
			deleteTheme(theme, themeName);
		});
	});
}

// 启用主题
function enableTheme(theme) {
	twAjax.postd("index.php?u=theme-enable-ajax-1", { "theme": theme }, function(data) {
		twAjax.alert(data);
		if (window.twData.err == 0) {
			setTimeout(function() {
				window.location.reload();
			}, 1000);
		}
	});
}

// 删除主题
function deleteTheme(theme, themeName) {
	twAjax.confirm(`删除不可恢复，确定删除"<span style='color:#ef4444'>${themeName}</span>"？`, function() {
		twAjax.postd("index.php?u=theme-delete-ajax-1", { "theme": theme }, function(data) {
			window.twErr = true;
			twAjax.alert(data);
			$(".ajaxtips u").click(function() {
				window.location.reload();
			});
			if (window.twData.err == 0) {
				setTimeout(function() {
					window.location.reload();
				}, 1000);
			}
		});
	});
}

// 加载在线主题
let onlineLoaded = false;
function loadOnlineThemes() {
	if (onlineLoaded) return;

	// 加载动画效果
	let dots = '';
	const interval = setInterval(function() {
		dots += '.';
		if (dots.length > 6) dots = '.';
		const loadingEl = document.getElementById('loading-dots');
		if (loadingEl) loadingEl.textContent = dots;
	}, 200);

	// 加载在线主题脚本
	try {
		const script = document.createElement('script');
		script.src = 'http://www.vodcms.com/app/?go=theme';
		script.onload = function() {
			clearInterval(interval);
			onlineLoaded = true;
		};
		script.onerror = function() {
			clearInterval(interval);
			document.getElementById('online-themes').innerHTML = `
				<div class="bg-slate-900 border border-slate-700 rounded-xl p-8 text-center">
					<div class="w-16 h-16 bg-red-500/10 rounded-full flex items-center justify-center mx-auto mb-4">
						<svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"/>
						</svg>
					</div>
					<h3 class="text-lg font-semibold text-slate-200 mb-2">加载失败</h3>
					<p class="text-slate-400">无法连接到在线主题服务器，请检查网络连接</p>
				</div>
			`;
		};
		document.head.appendChild(script);
	} catch (e) {
		clearInterval(interval);
		console.error('加载在线主题失败:', e);
	}
}

// 在线安装主题
function install_theme(dir) {
	twAjax.confirm(`正在下载 <span style='color:#3b82f6'>${dir}</span> 主题<span id='loading'>......</span>`, function() {});

	// 加载效果
	let dot = '';
	const int = setInterval(function() {
		dot += '.';
		if (dot.length > 6) dot = '.';
		const loadingEl = document.getElementById("loading");
		if (loadingEl) loadingEl.textContent = dot;
	}, 50);

	const script = document.createElement('script');
	script.src = `index.php?u=theme-install_theme-dir-${dir}`;
	script.onload = function() {
		clearInterval(int);
		if (typeof err !== 'undefined' && !err && !document.getElementById("install_enable")) {
			const noBtn = document.getElementById("noA");
			if (noBtn) {
				const enableBtn = document.createElement('a');
				enableBtn.id = 'install_enable';
				enableBtn.className = 'but3';
				enableBtn.href = 'javascript:;';
				enableBtn.textContent = '启用';
				enableBtn.onclick = function() { enableTheme(dir); };
				noBtn.insertAdjacentElement('afterend', enableBtn);
			}
		}

		// 重新加载本地主题页面
		const localTab = document.querySelector('.theme-tab[data-tab="local"]');
		if (localTab) {
			localTab.addEventListener('click', function() {
				window.location.reload();
			}, { once: true });
		}
	};
	document.head.appendChild(script);
}

// 兼容老的全局函数调用
window.install_theme = install_theme;
window.enable = enableTheme;
</script>

</body>
</html>