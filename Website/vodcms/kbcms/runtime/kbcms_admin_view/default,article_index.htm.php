<?php defined('APP_NAME') || exit('Access Denied'); 
?><!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/css/output.css" rel="stylesheet">
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>js/jquery.js" type="text/javascript"></script>
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/js/global.js" type="text/javascript"></script>
	<script type="text/javascript">
		var pKey = "<?php echo(isset($_pkey) ? $_pkey : ''); ?>", urlKey = "<?php echo(isset($_ukey) ? $_ukey : ''); ?>", place = "<?php echo(isset($_place) ? $_place : ''); ?>";
	</script>
	<title><?php echo(isset($_title) ? $_title : ''); ?></title>
	<style>
		/* 统一的现代化页面样式 - 使用设计规范配色 */
		body {
			background: #0f172a;
			/* slate-950 - 统一主背景 */
			min-height: 100vh;
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			color: #e2e8f0;
			/* slate-200 - 次级文本 */
		}

		.modern-container {
			max-width: 1400px;
			margin: 0 auto;
			padding: 20px;
		}

		.modern-card {
			background: #1e293b;
			/* slate-900 - 次级背景 */
			border-radius: 20px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			border: 1px solid #334155;
			/* slate-700 - 主边框 */
			overflow: hidden;
		}

		.modern-header {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 使用统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 - 主文本 */
			padding: 30px;
			text-align: center;
			position: relative;
		}

		.modern-header::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
			opacity: 0.3;
		}

		.modern-header h1 {
			font-size: 2.5rem;
			font-weight: 700;
			margin: 0;
			position: relative;
			z-index: 1;
			color: #f8fafc;
			/* slate-50 */
		}

		.modern-form {
			padding: 40px;
		}

		.form-grid {
			display: grid;
			grid-template-columns: 2fr 1fr;
			gap: 40px;
			align-items: start;
		}

		.form-section {
			background: #334155;
			/* slate-800 - 三级背景 */
			border-radius: 16px;
			padding: 30px;
			border: 1px solid #475569;
			/* slate-600 - 次级边框 */
		}

		.form-group {
			margin-bottom: 25px;
		}

		.form-label {
			display: block;
			font-weight: 600;
			color: #cbd5e1;
			/* slate-300 - 三级文本 */
			margin-bottom: 8px;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}

		.form-label.required::before {
			content: '*';
			color: #ef4444;
			/* red-500 - 错误色 */
			margin-right: 4px;
		}

		.form-input {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #475569;
			/* slate-600 */
			border-radius: 12px;
			font-size: 14px;
			transition: all 0.3s ease;
			background: #334155;
			/* slate-700 */
			color: #f8fafc;
			/* slate-50 */
		}

		.form-input:focus {
			outline: none;
			border-color: #3b82f6;
			/* blue-500 - 激活边框 */
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
			background: #1e293b;
			/* slate-900 */
		}

		.form-input::placeholder {
			color: #94a3b8;
			/* slate-400 - 占位符文本 */
		}

		.form-textarea {
			min-height: 120px;
			resize: vertical;
			font-family: inherit;
		}

		.form-select {
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 12px center;
			background-repeat: no-repeat;
			background-size: 16px;
			padding-right: 40px;
		}

		.checkbox-group {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin-top: 10px;
		}

		.checkbox-item {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 12px;
			background: #475569;
			/* slate-600 */
			border-radius: 8px;
			border: 1px solid #64748b;
			/* slate-500 */
			transition: all 0.3s ease;
			color: #e2e8f0;
			/* slate-200 */
		}

		.checkbox-item:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #3b82f6;
			/* blue-500 */
		}

		.checkbox-item input[type="checkbox"] {
			width: 16px;
			height: 16px;
			accent-color: #3b82f6;
			/* blue-500 */
		}

		.btn-primary {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 */
			border: none;
			padding: 14px 32px;
			border-radius: 12px;
			font-weight: 600;
			font-size: 16px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
		}

		.btn-primary:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
			background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
			/* blue-600 to blue-800 */
		}

		.btn-secondary {
			background: #475569;
			/* slate-600 */
			color: #e2e8f0;
			/* slate-200 */
			border: 2px solid #64748b;
			/* slate-500 */
			padding: 10px 20px;
			border-radius: 8px;
			font-weight: 500;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.btn-secondary:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #94a3b8;
			/* slate-400 */
		}

		.sidebar-section {
			background: #334155;
			/* slate-800 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
			border: 1px solid #475569;
			/* slate-600 */
		}

		.sidebar-title {
			font-weight: 700;
			color: #f8fafc;
			/* slate-50 */
			margin-bottom: 20px;
			font-size: 16px;
			padding-bottom: 10px;
			border-bottom: 2px solid #64748b;
			/* slate-500 */
		}

		.image-upload-area {
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 30px;
			text-align: center;
			background: #334155;
			/* slate-700 */
			transition: all 0.3s ease;
			cursor: pointer;
		}

		.image-upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.image-preview {
			max-width: 100%;
			height: auto;
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.resource-section {
			background: #334155;
			/* slate-700 */
			border: 1px solid #10b981;
			/* emerald-500 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
		}

		.resource-title {
			color: #10b981;
			/* emerald-500 */
			font-weight: 700;
			margin-bottom: 15px;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.resource-title::before {
			content: '🎬';
			font-size: 18px;
		}

		.add-resource-btn {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			color: white;
			border: none;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 12px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.add-resource-btn:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
		}

		.upload-area {
			background: #334155;
			/* slate-700 */
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 20px;
			text-align: center;
			transition: all 0.3s ease;
		}

		.upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.upload-status {
			margin-top: 10px;
			padding: 10px;
			border-radius: 8px;
			background: #334155;
			/* slate-700 */
			font-size: 13px;
		}

		/* 响应式设计 */
		@media (max-width: 1024px) {
			.form-grid {
				grid-template-columns: 1fr;
				gap: 30px;
			}

			.modern-form {
				padding: 30px 20px;
			}
		}

		@media (max-width: 768px) {
			.modern-container {
				padding: 10px;
			}

			.modern-header {
				padding: 20px;
			}

			.modern-header h1 {
				font-size: 2rem;
			}

			.checkbox-group {
				flex-direction: column;
			}
		}

		/* 动画效果 */
		@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translateY(30px);
			}

			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		.modern-card {
			animation: fadeInUp 0.6s ease-out;
		}

		.form-section {
			animation: fadeInUp 0.6s ease-out 0.1s both;
		}

		.sidebar-section {
			animation: fadeInUp 0.6s ease-out 0.2s both;
		}

		/* 隐藏传统样式元素 */
		.tb,
		.th,
		.col,
		.cc,
		.contadd {
			all: unset;
		}

		/* 重置表格样式 */
		table.tb {
			width: 100%;
			border-collapse: separate;
			border-spacing: 0;
		}

		.tb tr {
			display: contents;
		}

		.tb td,
		.tb th {
			display: block;
			width: 100%;
			margin-bottom: 20px;
		}
	</style>

	<!-- 统一对话框样式 -->
	<style>
		.ajaxoverlay {
			position: fixed !important;
			top: 0 !important;
			left: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			z-index: 10000 !important;
			width: 100vw !important;
			height: 100vh !important;
			background: rgba(0, 0, 0, 0.4) !important;
			backdrop-filter: blur(2px) !important;
		}

		.ajaxtips {
			position: fixed !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			z-index: 10001 !important;
			max-width: 90vw !important;
			max-height: 90vh !important;
		}

		.ajaxbox {
			background: #1e293b !important;
			/* slate-800 */
			border: 1px solid #3b82f6 !important;
			/* blue-500 */
			border-radius: 12px !important;
			padding: 24px !important;
			line-height: 1.6 !important;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
			min-width: 320px !important;
			max-width: 500px !important;
			color: #f8fafc !important;
			/* slate-50 */
			font-size: 16px !important;
			display: flex !important;
			flex-direction: column !important;
			align-items: center !important;
			gap: 12px !important;
		}

		.ajaxbox i {
			width: 32px !important;
			height: 32px !important;
			margin: 0 !important;
			flex-shrink: 0 !important;
			background-size: contain !important;
		}

		.ajaxbox b {
			flex: 1 !important;
			font-weight: 500 !important;
			font-size: 16px !important;
			line-height: 1.5 !important;
			margin: 0 !important;
		}

		.ajaxbox u {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
			padding: 8px 16px !important;
			border-radius: 6px !important;
			cursor: pointer !important;
			text-decoration: none !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin-left: 16px !important;
			flex-shrink: 0 !important;
		}

		.ajaxbox u:hover {
			background: #2563eb !important;
			/* blue-600 */
			transform: translateY(-1px) !important;
		}

		/* 成功状态 */
		.btrue {
			border-color: #10b981 !important;
			/* emerald-500 */
			background: #064e3b !important;
			/* emerald-900 */
		}

		.btrue i {
			background: #10b981 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.btrue i::after {
			content: "✓" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.btrue u {
			background: #10b981 !important;
		}

		.btrue u:hover {
			background: #059669 !important;
		}

		/* 错误状态 */
		.bfalse {
			border-color: #ef4444 !important;
			/* red-500 */
			background: #7f1d1d !important;
			/* red-900 */
		}

		.bfalse i {
			background: #ef4444 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bfalse i::after {
			content: "✕" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.bfalse u {
			background: #ef4444 !important;
		}

		.bfalse u:hover {
			background: #dc2626 !important;
		}

		/* 警告状态 */
		.bnote {
			border-color: #f59e0b !important;
			/* amber-500 */
			background: #78350f !important;
			/* amber-900 */
		}

		.bnote i {
			background: #f59e0b !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bnote i::after {
			content: "!" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 20px !important;
			font-weight: bold !important;
		}

		.bnote u {
			background: #f59e0b !important;
		}

		.bnote u:hover {
			background: #d97706 !important;
		}

		/* 加载动画优化 */
		.ajaximg {
			width: 48px !important;
			height: 48px !important;
			background: none !important;
			border: 4px solid #334155 !important;
			border-top: 4px solid #3b82f6 !important;
			border-radius: 50% !important;
			animation: spin 1s linear infinite !important;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		/* 确认框样式优化 */
		.ajaxbox .cf {
			margin-top: 20px !important;
			padding: 0 !important;
			display: flex !important;
			justify-content: center !important;
			gap: 12px !important;
		}

		.ajaxbox .cf .but3 {
			padding: 8px 16px !important;
			border-radius: 6px !important;
			border: none !important;
			cursor: pointer !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin: 0 !important;
			float: none !important;
		}

		.ajaxbox .cf .but3:first-child {
			background: #6b7280 !important;
			/* gray-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:first-child:hover {
			background: #4b5563 !important;
			/* gray-600 */
		}

		.ajaxbox .cf .but3:last-child {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:last-child:hover {
			background: #2563eb !important;
			/* blue-600 */
		}
	</style>
</head>

<body>

<div class="min-h-screen bg-slate-950 p-4">
	<div class="max-w-7xl mx-auto">
		<!-- 页面标题和操作栏 -->
		<div class="bg-slate-900 border border-slate-700 rounded-xl shadow-lg p-6 mb-6">
			<div class="flex items-center justify-between mb-6">
				<div>
					<h1 class="text-2xl font-bold text-slate-50">文章管理</h1>
					<p class="text-slate-300 mt-1">管理您的文章内容，发布精彩文章</p>
				</div>
				<div class="flex gap-3">
					<button onclick="window.location.href='/admin/index.php?u=article-index'"
						class="bg-slate-600 hover:bg-slate-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
						<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
						</svg>
						返回首页
					</button>
					<button id="add"
						class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
						<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
						</svg>
						发布文章
					</button>
				</div>
			</div>

			<!-- 搜索和筛选区域 -->
			<div class="bg-slate-800 border border-slate-600 rounded-lg p-4">
				<form class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 items-end" method="post" action="index.php?u=article-index">
					<!-- 分类筛选 -->
					<div class="lg:col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-2">选择分类</label>
						<?php echo(isset($cidhtml) ? $cidhtml : ''); ?>
					</div>

					<!-- 关键词搜索 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">关键词</label>
						<input type="search" name="keyword" value="<?php echo(isset($keyword) ? $keyword : ''); ?>"
							class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400"
							placeholder="搜索标题">
					</div>

					<!-- 年份搜索 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">年份</label>
						<input type="search" name="year" value="<?php echo(isset($year) ? $year : ''); ?>"
							class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400"
							placeholder="年份">
					</div>

					<!-- 作者搜索 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">作者</label>
						<input type="search" name="author" value="<?php echo(isset($author) ? $author : ''); ?>"
							class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400"
							placeholder="作者名">
					</div>

					<!-- 内容搜索 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">内容</label>
						<input type="search" name="content" value="<?php echo(isset($content) ? $content : ''); ?>"
							class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400"
							placeholder="搜索内容">
					</div>

					<!-- 搜索按钮 -->
					<div class="lg:col-span-6 flex justify-end">
						<button type="submit"
							class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
							<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
									d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
							</svg>
							搜索文章
						</button>
					</div>
				</form>
			</div>

			<!-- 批量操作按钮区域 -->
			<div id="batch_operations" class="mt-4 hidden">
				<div class="flex space-x-3">
					<button id="batch_del"
						class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
						<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
						</svg>
						批量删除
					</button>
					<button id="bdpost_url"
						class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
						<svg class="w-4 h-4 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
						</svg>
						链接提交
					</button>
				</div>
				<div id="operation_status" class="mt-2 text-sm text-slate-300"></div>
			</div>
	</div>

		<!-- 文章列表内容 -->
		<div class="bg-slate-900 border border-slate-700 rounded-xl shadow-lg overflow-hidden">
			<?php if (empty($cms_article_arr)) { ?>
			<div class="p-8 text-center">
				<div class="inline-flex items-center justify-center w-16 h-16 bg-slate-700 rounded-full mb-4">
					<svg class="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
					</svg>
				</div>
				<h3 class="text-lg font-medium text-slate-200 mb-2">暂无文章</h3>
				<p class="text-slate-400 mb-4">还没有发布任何文章，点击上方按钮开始创作吧！</p>
				<button id="add_empty"
					class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
					发布第一篇文章
				</button>
			</div>
			<?php }else{ ?>
			<!-- 文章表格 -->
			<div class="overflow-x-auto">
				<table class="w-full">
					<thead class="bg-slate-800 border-b border-slate-700">
						<tr>
							<th class="w-12 px-4 py-4 text-left">
								<input type="checkbox" id="check_all"
									class="rounded bg-slate-700 border-slate-600 text-blue-600 focus:ring-blue-500 focus:ring-2">
							</th>
							<th class="w-16 px-4 py-4 text-left text-sm font-semibold text-slate-300">ID</th>
							<th class="px-4 py-4 text-left text-sm font-semibold text-slate-300">标题</th>
							<th class="w-24 px-4 py-4 text-left text-sm font-semibold text-slate-300">分类</th>
							<th class="w-20 px-4 py-4 text-left text-sm font-semibold text-slate-300">审核</th>
							<th class="w-32 px-4 py-4 text-left text-sm font-semibold text-slate-300">资源</th>
							<th class="w-24 px-4 py-4 text-left text-sm font-semibold text-slate-300">作者</th>
							<th class="w-20 px-4 py-4 text-left text-sm font-semibold text-slate-300">年份</th>
							<th class="w-40 px-4 py-4 text-left text-sm font-semibold text-slate-300">发布时间</th>
							<th class="w-40 px-4 py-4 text-left text-sm font-semibold text-slate-300">操作</th>
				</tr>
					</thead>
					<tbody class="divide-y divide-slate-700">
				<?php if(isset($cms_article_arr) && is_array($cms_article_arr)) { foreach($cms_article_arr as &$v) { ?>
						<tr class="hover:bg-slate-800 transition-colors duration-150">
							<td class="px-4 py-4">
								<input type="checkbox" name="chk_row" _id="<?php echo(isset($v['id']) ? $v['id'] : ''); ?>" _cid="<?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>" _url="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>"
									class="rounded bg-slate-700 border-slate-600 text-blue-600 focus:ring-blue-500 focus:ring-2">
							</td>
							<td class="px-4 py-4 text-sm font-medium text-slate-300"><?php echo(isset($v['id']) ? $v['id'] : ''); ?></td>
							<td class="px-4 py-4">
								<div class="text-sm font-medium text-slate-200"><?php echo(isset($v['title']) ? $v['title'] : ''); ?></div>
								<?php if (!empty($v['flagstr'])) { ?>
								<div class="mt-1">
									<span class="inline-flex items-center text-xs"><?php echo(isset($v['flagstr']) ? $v['flagstr'] : ''); ?></span>
								</div>
								<?php } ?>
							</td>
							<td class="px-4 py-4">
								<span class="inline-flex px-2 py-1 text-xs font-medium bg-slate-700 text-slate-300 rounded-full">
									<?php echo(isset($v['cate_name']) ? $v['cate_name'] : ''); ?>
								</span>
							</td>
							<td class="px-4 py-4">
								<?php if ($v['isreview'] == 1) { ?>
								<span class="inline-flex px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
									未审
								</span>
								<?php }else{ ?>
								<span class="inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
									已审
								</span>
								<?php } ?>
							</td>
							<td class="px-4 py-4">
								<div class="text-sm text-slate-300 truncate max-w-32" title="<?php echo(isset($v['subtitle']) ? $v['subtitle'] : ''); ?>">
									<?php echo(isset($v['subtitle']) ? $v['subtitle'] : ''); ?>
								</div>
							</td>
							<td class="px-4 py-4 text-sm text-slate-300"><?php echo(isset($v['author']) ? $v['author'] : ''); ?></td>
							<td class="px-4 py-4 text-sm text-slate-300"><?php echo(isset($v['year']) ? $v['year'] : ''); ?></td>
							<td class="px-4 py-4 text-sm text-slate-400">
								<?php  echo date('Y-m-d H:i:s', $v['dateline']); ?>
							</td>
							<td class="px-4 py-4">
								<div class="flex space-x-2">
									<button onclick="edit('<?php echo(isset($v['id']) ? $v['id'] : ''); ?>', '<?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>')"
										class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors duration-200">
										编辑
									</button>
									<a href="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>" target="_blank"
										class="bg-emerald-600 hover:bg-emerald-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors duration-200 inline-block">
										查看
									</a>
									<button onclick="del('<?php echo(isset($v['id']) ? $v['id'] : ''); ?>', '<?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>')"
										class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium transition-colors duration-200">
										删除
									</button>
								</div>
					</td>
				</tr>
				<?php }} ?>
					</tbody>
			</table>
			</div>

						<!-- 分页信息 -->
			<div class="bg-slate-800 border-t border-slate-700 px-6 py-4">
				<div class="flex items-center justify-between">
					<!-- 左侧：文章统计信息和每页数量选择 -->
					<div class="flex items-center gap-6">
						<div class="text-sm text-slate-400">
							共 <span class="font-medium text-slate-200"><?php echo(isset($total) ? $total : ''); ?></span> 篇文章
						</div>

						<div class="flex items-center gap-2">
							<label class="text-sm text-slate-400">每页显示:</label>
							<select id="page_size" onchange="changePageSize()"
								class="bg-slate-700 border border-slate-600 text-slate-200 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
								<option value="20" <?php if ($pagesize == 20) { ?>selected<?php } ?>>20条</option>
								<option value="50" <?php if ($pagesize == 50) { ?>selected<?php } ?>>50条</option>
								<option value="100" <?php if ($pagesize == 100) { ?>selected<?php } ?>>100条</option>
							</select>
						</div>
					</div>

					<!-- 右侧：分页导航 -->
					<div class="flex items-center gap-4">
						<!-- 传统分页链接（隐藏但保持功能） -->
						<div class="hidden">
							<?php echo(isset($pages) ? $pages : ''); ?>
						</div>

						<!-- 现代化分页组件 -->
						<div id="modern_pagination" class="flex items-center gap-1">
							<!-- 上一页 -->
							<button id="prev_page" onclick="goToPage(<?php echo(isset($current_page) ? $current_page : ''); ?> - 1)"
								class="flex items-center px-3 py-2 text-sm font-medium text-slate-300 bg-slate-700 border border-slate-600 rounded-md hover:bg-slate-600 hover:text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
								<?php if ($current_page <= 1) { ?>disabled<?php } ?>>
								<svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
								</svg>
								上一页
							</button>

							<!-- 页码按钮 -->
							<div id="page_numbers" class="flex items-center gap-1">
								<!-- 页码将通过JavaScript动态生成 -->
							</div>

							<!-- 下一页 -->
							<button id="next_page" onclick="goToPage(<?php echo(isset($current_page) ? $current_page : ''); ?> + 1)"
								class="flex items-center px-3 py-2 text-sm font-medium text-slate-300 bg-slate-700 border border-slate-600 rounded-md hover:bg-slate-600 hover:text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
								<?php if ($current_page >= $total_pages) { ?>disabled<?php } ?>>
								下一页
								<svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
									<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
								</svg>
							</button>
						</div>

						<!-- 页码跳转 -->
						<div class="flex items-center gap-2">
							<span class="text-sm text-slate-400">跳至</span>
							<input type="number" id="jump_page" min="1" max="<?php echo(isset($total_pages) ? $total_pages : ''); ?>" placeholder="<?php echo(isset($current_page) ? $current_page : ''); ?>"
								class="w-16 px-2 py-1 text-sm bg-slate-700 border border-slate-600 text-slate-200 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
								onkeypress="handlePageJump(event)">
							<span class="text-sm text-slate-400">页</span>
							<button onclick="jumpToPage()"
								class="px-3 py-1 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors duration-200">
								跳转
							</button>
						</div>
					</div>
				</div>
			</div>
			<?php } ?>
		</div>
	</div>
</div>

<style>
/* 分类下拉框样式优化 */
#cid {
	background: #334155 !important;
	border: 1px solid #475569 !important;
	color: #f1f5f9 !important;
	padding: 8px 12px !important;
	border-radius: 8px !important;
	width: 100% !important;
}

#cid:focus {
	outline: none !important;
	border-color: #3b82f6 !important;
	box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5) !important;
}

/* 分页样式优化 */
.pages a {
	background: #334155 !important;
	color: #cbd5e1 !important;
	border: 1px solid #475569 !important;
	padding: 8px 12px !important;
	margin: 0 2px !important;
	border-radius: 6px !important;
	text-decoration: none !important;
	transition: all 0.2s ease !important;
	font-size: 14px !important;
}

.pages a:hover {
	background: #475569 !important;
	color: #f1f5f9 !important;
	border-color: #64748b !important;
	transform: translateY(-1px) !important;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.pages a.current {
	background: #3b82f6 !important;
	color: white !important;
	border-color: #3b82f6 !important;
	box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.pages span {
	color: #94a3b8 !important;
	padding: 8px 12px !important;
	font-size: 14px !important;
}

/* 表格按钮组样式优化 */
.flex.space-x-2 button, .flex.space-x-2 a {
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important;
	font-size: 12px !important;
	font-weight: 500 !important;
	min-width: 50px !important;
	height: 28px !important;
	border: none !important;
	cursor: pointer !important;
	border-radius: 6px !important;
	transition: all 0.15s ease !important;
	text-decoration: none !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.flex.space-x-2 button:hover, .flex.space-x-2 a:hover {
	transform: translateY(-1px) !important;
	box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.flex.space-x-2 button:active, .flex.space-x-2 a:active {
	transform: translateY(0) !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 状态徽章样式优化 */
.inline-flex.px-2.py-1 {
	font-weight: 600 !important;
	letter-spacing: 0.025em !important;
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 复选框样式优化 */
input[type="checkbox"].rounded {
	width: 16px !important;
	height: 16px !important;
	margin: 0 !important;
	cursor: pointer !important;
}

/* 搜索表单样式优化 */
.bg-slate-800.border.border-slate-600.rounded-lg input,
.bg-slate-800.border.border-slate-600.rounded-lg select {
	transition: all 0.2s ease !important;
}

.bg-slate-800.border.border-slate-600.rounded-lg input:focus,
.bg-slate-800.border.border-slate-600.rounded-lg select:focus {
	transform: translateY(-1px) !important;
	box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}
</style>

<script type="text/javascript">
$("#add, #add_empty").click(function(){
	parent.oneTab("article-add");
});

$("#cid").change(function(){
	location.href = "index.php?u=article-index-cid-"+ $(this).val();
});

//全选
$("#check_all").change(function(){
	var bool = $(this)[0].checked;
	$("input[name='chk_row']").prop('checked', bool);
	toggleBatchOperations();
});

//单选
$("input[name='chk_row']").change(function(){
	toggleBatchOperations();

	// 更新全选状态
	var total = $("input[name='chk_row']").length;
	var checked = $("input[name='chk_row']:checked").length;
	$("#check_all").prop('checked', total === checked);
});

//切换批量操作按钮显示
function toggleBatchOperations() {
	var checkedCount = $("input[name='chk_row']:checked").length;
	if (checkedCount > 0) {
		$("#batch_operations").removeClass("hidden");
	} else {
		$("#batch_operations").addClass("hidden");
		$("#operation_status").text("");
	}
}

//编辑
function edit(id, cid) {
	parent.oneTab("article-edit-cid-"+cid+"-id-"+id);
}

//删除
function del(id, cid) {
	twAjax.confirm("删除不可恢复，确定删除？", function(){
		twAjax.postd("index.php?u=article-del-ajax-1", {"id":parseInt(id), "cid":parseInt(cid)}, function(data){
			twAjax.alert(data);
			if(window.twData.err==0) setTimeout(function(){ window.location.reload(); }, 1000);
		});
	});
}

//百度链接推送
$("#bdpost_url").click(function(){
	var id_arr = {};
	var index = 0;
	$("input[name='chk_row']:checked").each(function(){
		var obj = $(this);
		id_arr[index] = [obj.attr("_id"), obj.attr("_cid"), obj.attr("_url")];
		index++;
	});

	$("#operation_status").text("正在提交链接...");

	$.ajax({
		type: "POST",
		url: "index.php?u=article-bdpost_url-ajax-1",
		data: {'id_arr': id_arr},
		success: function(data){
			try {
			var reg = /.*?(?=\r\n)/;
			var str = data.match(reg);
				if(str && str[0].indexOf("success_realtime") !== -1){
					var obj = $.parseJSON(str[0]);
					$("#operation_status").text('成功：' + obj.success_realtime + '条，剩余推送:' + obj.remain_realtime + '条').removeClass("text-red-400").addClass("text-green-400");
				} else {
					var obj = $.parseJSON(str[0]);
					$("#operation_status").text('错误码:' + obj.error + '，' + obj.message).removeClass("text-green-400").addClass("text-red-400");
			}
			} catch(e) {
				$("#operation_status").text('链接提交完成').removeClass("text-red-400").addClass("text-green-400");
			}
		},
		error: function(){
			$("#operation_status").text('链接提交失败').removeClass("text-green-400").addClass("text-red-400");
}
	});
});

//批量删除
$("#batch_del").click(function(){
	twAjax.confirm("删除不可恢复，确定删除？", function(){
		var id_arr = {};
		var index = 0;
		$("input[name='chk_row']:checked").each(function(){
			var obj = $(this);
			id_arr[index] = [obj.attr("_id"), obj.attr("_cid")];
			index++;
		});

		twAjax.postd("index.php?u=article-batch_del-ajax-1", {"id_arr":id_arr}, function(data){
			twAjax.alert(data);
			if(window.twData.err==0) setTimeout(function(){ window.location.reload(); }, 1000);
		});
	});
});

// 分页相关函数
$(document).ready(function() {
	initPagination();
});

// 初始化分页
function initPagination() {
	// 从页面隐藏的分页链接中提取信息
	var pages = $('.hidden .pages a');
	var currentPageFromUrl = getCurrentPageFromUrl();
	var totalPages = getTotalPagesFromLinks();

	generatePageNumbers(currentPageFromUrl, totalPages);
}

// 从URL获取当前页码
function getCurrentPageFromUrl() {
	var urlParams = new URLSearchParams(window.location.search);
	var page = urlParams.get('page') || 1;
	return parseInt(page);
}

// 从分页链接中获取总页数
function getTotalPagesFromLinks() {
	var pages = $('.hidden .pages a');
	var maxPage = 1;

	pages.each(function() {
		var href = $(this).attr('href');
		if (href) {
			var match = href.match(/page[_-](\d+)/);
			if (match) {
				maxPage = Math.max(maxPage, parseInt(match[1]));
			}
		}
	});

	return maxPage;
}

// 生成页码按钮
function generatePageNumbers(currentPage, totalPages) {
	var pageNumbersContainer = $('#page_numbers');
	pageNumbersContainer.empty();

	// 计算显示的页码范围
	var startPage = Math.max(1, currentPage - 2);
	var endPage = Math.min(totalPages, currentPage + 2);

	// 如果开始页码大于1，显示第一页和省略号
	if (startPage > 1) {
		pageNumbersContainer.append(createPageButton(1, currentPage));
		if (startPage > 2) {
			pageNumbersContainer.append('<span class="px-2 py-2 text-slate-400">...</span>');
		}
	}

	// 显示中间的页码
	for (var i = startPage; i <= endPage; i++) {
		pageNumbersContainer.append(createPageButton(i, currentPage));
	}

	// 如果结束页码小于总页数，显示省略号和最后一页
	if (endPage < totalPages) {
		if (endPage < totalPages - 1) {
			pageNumbersContainer.append('<span class="px-2 py-2 text-slate-400">...</span>');
		}
		pageNumbersContainer.append(createPageButton(totalPages, currentPage));
	}
}

// 创建页码按钮
function createPageButton(pageNum, currentPage) {
	var isActive = pageNum === currentPage;
	var buttonClass = isActive
		? 'px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md'
		: 'px-3 py-2 text-sm font-medium text-slate-300 bg-slate-700 border border-slate-600 rounded-md hover:bg-slate-600 hover:text-white transition-colors duration-200';

	return $('<button>')
		.attr('onclick', 'goToPage(' + pageNum + ')')
		.attr('class', buttonClass)
		.text(pageNum);
}

// 跳转到指定页面
function goToPage(page) {
	if (page < 1) return;

	var url = new URL(window.location);
	url.searchParams.set('page', page);
	window.location.href = url.toString();
}

// 改变每页显示数量
function changePageSize() {
	var pageSize = $('#page_size').val();
	var url = new URL(window.location);
	url.searchParams.set('pagesize', pageSize);
	url.searchParams.set('page', 1); // 重置到第一页
	window.location.href = url.toString();
}

// 处理页码跳转输入框的回车事件
function handlePageJump(event) {
	if (event.key === 'Enter') {
		jumpToPage();
	}
}

// 跳转到指定页面
function jumpToPage() {
	var pageNum = parseInt($('#jump_page').val());
	var totalPages = getTotalPagesFromLinks();

	if (pageNum && pageNum >= 1 && pageNum <= totalPages) {
		goToPage(pageNum);
	} else {
		// 显示错误提示
		$('#jump_page').addClass('border-red-500').focus();
		setTimeout(function() {
			$('#jump_page').removeClass('border-red-500');
		}, 2000);
	}
}
</script>
</body>
</html>
