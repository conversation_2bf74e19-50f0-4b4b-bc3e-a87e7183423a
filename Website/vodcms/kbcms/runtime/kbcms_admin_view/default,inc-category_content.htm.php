<?php defined('APP_NAME') || exit('Access Denied'); 
if (empty($category_arr)) { ?>
<div class="p-8 text-center">
	<div class="bg-yellow-100 border border-yellow-300 rounded-lg p-6">
		<svg class="w-12 h-12 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
				d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
			</path>
		</svg>
		<h3 class="text-lg font-medium text-yellow-800 mb-2">暂无分类</h3>
		<p class="text-yellow-700">请先创建分类来管理您的内容</p>
	</div>
</div>
<?php }else{ ?>
	<?php if(isset($category_arr) && is_array($category_arr)) { foreach($category_arr as $mid=>&$arr) { ?>
<div class="category mb-6">
	<!-- 分类模型标题 -->
	<div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 rounded-t-lg">
		<div class="grid grid-cols-12 gap-4 items-center font-medium">
			<div class="col-span-5 flex items-center">
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
						d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
					</path>
				</svg>
				<?php  echo $mod_name[$mid]; ?>分类
			</div>
			<div class="col-span-2 text-center">分类别名</div>
			<div class="col-span-2 text-center">排序</div>
			<div class="col-span-2 text-center">内容数量</div>
			<div class="col-span-1 text-center">操作</div>
		</div>
	</div>

	<!-- 分类列表 -->
	<div class="bg-slate-800 border-l border-r border-b border-slate-600 rounded-b-lg">
		<?php if(isset($arr) && is_array($arr)) { foreach($arr as &$v) { ?>
		<?php 
			$v['prestr'] = str_repeat("　　", $v['pre']-1);
			$v['url'] = $_ENV['_category_class']->category_url($v['cid'], $v['alias']);
		 ?>
		<div class="cat_col grid grid-cols-12 gap-4 items-center p-4 border-b border-slate-600 hover:bg-slate-700 transition-colors cursor-pointer"
			cid="<?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>" mid="<?php echo(isset($v['mid']) ? $v['mid'] : ''); ?>" url="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
			<div class="cat_c_1 col-span-5 flex items-center">
				<div class="c_name flex items-center" title="编号(cid): <?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>&#10;分类名称: <?php echo(isset($v['name']) ? $v['name'] : ''); ?>">
					<span class="text-slate-400 mr-2"><?php echo(isset($v['prestr']) ? $v['prestr'] : ''); ?></span>
					<?php if ($v['mid'] == 1) { ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-600 text-purple-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
							</path>
						</svg>
						单页
					</span>
					<?php }elseif($v['type'] == 1) { ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-green-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
							</path>
						</svg>
						频道
					</span>
					<?php }else{ ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-blue-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
						</svg>
						列表
					</span>
					<?php } ?>
					<span class="font-medium text-slate-100"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></span>
				</div>
			</div>
			<div class="cat_c_2 col-span-2 text-center">
				<span class="inline-flex items-center px-2 py-1 rounded bg-slate-700 text-slate-200 text-sm font-mono"
					title="<?php echo(isset($v['alias']) ? $v['alias'] : ''); ?>"><?php echo(isset($v['alias']) ? $v['alias'] : ''); ?></span>
			</div>
			<div class="cat_c_3 col-span-2 text-center">
				<input name="orderby" value="<?php echo(isset($v['orderby']) ? $v['orderby'] : ''); ?>" type="number"
					class="w-16 px-2 py-1 text-center border border-slate-600 bg-slate-700 text-slate-200 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
			</div>
			<div class="cat_c_4 col-span-2 text-center">
				<?php if ($v['mid'] != 1 && $v['type'] != 1) { ?>
				<span
					class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-600 text-slate-200">
					<?php echo(isset($v['count']) ? $v['count'] : ''); ?>
				</span>
				<?php }else{ ?>
				<span class="text-slate-400">-</span>
				<?php } ?>
			</div>
			<div class="col-span-1 text-center">
				<div class="flex justify-center">
					<svg class="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z">
						</path>
					</svg>
				</div>
			</div>
		</div>
		<?php }} ?>
	</div>
	</div>
	<?php }} ?>
<?php } ?>