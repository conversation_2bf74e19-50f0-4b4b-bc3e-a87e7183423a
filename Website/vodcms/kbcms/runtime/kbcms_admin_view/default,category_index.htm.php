<?php defined('APP_NAME') || exit('Access Denied'); 
?><!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/xhtml">

<head>
	<meta http-equiv="content-type" content="text/html;charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link href="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/css/output.css" rel="stylesheet">
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>js/jquery.js" type="text/javascript"></script>
	<script src="<?php echo(isset($C['admin_static']) ? $C['admin_static'] : ''); ?>admin/js/global.js" type="text/javascript"></script>
	<script type="text/javascript">
		var pKey = "<?php echo(isset($_pkey) ? $_pkey : ''); ?>", urlKey = "<?php echo(isset($_ukey) ? $_ukey : ''); ?>", place = "<?php echo(isset($_place) ? $_place : ''); ?>";
	</script>
	<title><?php echo(isset($_title) ? $_title : ''); ?></title>
	<style>
		/* 统一的现代化页面样式 - 使用设计规范配色 */
		body {
			background: #0f172a;
			/* slate-950 - 统一主背景 */
			min-height: 100vh;
			font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
			color: #e2e8f0;
			/* slate-200 - 次级文本 */
		}

		.modern-container {
			max-width: 1400px;
			margin: 0 auto;
			padding: 20px;
		}

		.modern-card {
			background: #1e293b;
			/* slate-900 - 次级背景 */
			border-radius: 20px;
			box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
			border: 1px solid #334155;
			/* slate-700 - 主边框 */
			overflow: hidden;
		}

		.modern-header {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 使用统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 - 主文本 */
			padding: 30px;
			text-align: center;
			position: relative;
		}

		.modern-header::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
			opacity: 0.3;
		}

		.modern-header h1 {
			font-size: 2.5rem;
			font-weight: 700;
			margin: 0;
			position: relative;
			z-index: 1;
			color: #f8fafc;
			/* slate-50 */
		}

		.modern-form {
			padding: 40px;
		}

		.form-grid {
			display: grid;
			grid-template-columns: 2fr 1fr;
			gap: 40px;
			align-items: start;
		}

		.form-section {
			background: #334155;
			/* slate-800 - 三级背景 */
			border-radius: 16px;
			padding: 30px;
			border: 1px solid #475569;
			/* slate-600 - 次级边框 */
		}

		.form-group {
			margin-bottom: 25px;
		}

		.form-label {
			display: block;
			font-weight: 600;
			color: #cbd5e1;
			/* slate-300 - 三级文本 */
			margin-bottom: 8px;
			font-size: 14px;
			text-transform: uppercase;
			letter-spacing: 0.5px;
		}

		.form-label.required::before {
			content: '*';
			color: #ef4444;
			/* red-500 - 错误色 */
			margin-right: 4px;
		}

		.form-input {
			width: 100%;
			padding: 12px 16px;
			border: 2px solid #475569;
			/* slate-600 */
			border-radius: 12px;
			font-size: 14px;
			transition: all 0.3s ease;
			background: #334155;
			/* slate-700 */
			color: #f8fafc;
			/* slate-50 */
		}

		.form-input:focus {
			outline: none;
			border-color: #3b82f6;
			/* blue-500 - 激活边框 */
			box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
			background: #1e293b;
			/* slate-900 */
		}

		.form-input::placeholder {
			color: #94a3b8;
			/* slate-400 - 占位符文本 */
		}

		.form-textarea {
			min-height: 120px;
			resize: vertical;
			font-family: inherit;
		}

		.form-select {
			appearance: none;
			background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%2394a3b8' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
			background-position: right 12px center;
			background-repeat: no-repeat;
			background-size: 16px;
			padding-right: 40px;
		}

		.checkbox-group {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin-top: 10px;
		}

		.checkbox-item {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 8px 12px;
			background: #475569;
			/* slate-600 */
			border-radius: 8px;
			border: 1px solid #64748b;
			/* slate-500 */
			transition: all 0.3s ease;
			color: #e2e8f0;
			/* slate-200 */
		}

		.checkbox-item:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #3b82f6;
			/* blue-500 */
		}

		.checkbox-item input[type="checkbox"] {
			width: 16px;
			height: 16px;
			accent-color: #3b82f6;
			/* blue-500 */
		}

		.btn-primary {
			background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
			/* 统一的蓝色主色调 */
			color: #f8fafc;
			/* slate-50 */
			border: none;
			padding: 14px 32px;
			border-radius: 12px;
			font-weight: 600;
			font-size: 16px;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
		}

		.btn-primary:hover {
			transform: translateY(-2px);
			box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
			background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
			/* blue-600 to blue-800 */
		}

		.btn-secondary {
			background: #475569;
			/* slate-600 */
			color: #e2e8f0;
			/* slate-200 */
			border: 2px solid #64748b;
			/* slate-500 */
			padding: 10px 20px;
			border-radius: 8px;
			font-weight: 500;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.btn-secondary:hover {
			background: #64748b;
			/* slate-500 */
			border-color: #94a3b8;
			/* slate-400 */
		}

		.sidebar-section {
			background: #334155;
			/* slate-800 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
			border: 1px solid #475569;
			/* slate-600 */
		}

		.sidebar-title {
			font-weight: 700;
			color: #f8fafc;
			/* slate-50 */
			margin-bottom: 20px;
			font-size: 16px;
			padding-bottom: 10px;
			border-bottom: 2px solid #64748b;
			/* slate-500 */
		}

		.image-upload-area {
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 30px;
			text-align: center;
			background: #334155;
			/* slate-700 */
			transition: all 0.3s ease;
			cursor: pointer;
		}

		.image-upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.image-preview {
			max-width: 100%;
			height: auto;
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.resource-section {
			background: #334155;
			/* slate-700 */
			border: 1px solid #10b981;
			/* emerald-500 */
			border-radius: 16px;
			padding: 25px;
			margin-bottom: 25px;
		}

		.resource-title {
			color: #10b981;
			/* emerald-500 */
			font-weight: 700;
			margin-bottom: 15px;
			display: flex;
			align-items: center;
			gap: 8px;
		}

		.resource-title::before {
			content: '🎬';
			font-size: 18px;
		}

		.add-resource-btn {
			background: linear-gradient(135deg, #10b981 0%, #059669 100%);
			color: white;
			border: none;
			padding: 8px 16px;
			border-radius: 8px;
			font-size: 12px;
			font-weight: 600;
			cursor: pointer;
			transition: all 0.3s ease;
		}

		.add-resource-btn:hover {
			transform: translateY(-1px);
			box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
		}

		.upload-area {
			background: #334155;
			/* slate-700 */
			border: 2px dashed #475569;
			/* slate-600 */
			border-radius: 12px;
			padding: 20px;
			text-align: center;
			transition: all 0.3s ease;
		}

		.upload-area:hover {
			border-color: #3b82f6;
			/* blue-500 */
			background: #475569;
			/* slate-600 */
		}

		.upload-status {
			margin-top: 10px;
			padding: 10px;
			border-radius: 8px;
			background: #334155;
			/* slate-700 */
			font-size: 13px;
		}

		/* 响应式设计 */
		@media (max-width: 1024px) {
			.form-grid {
				grid-template-columns: 1fr;
				gap: 30px;
			}

			.modern-form {
				padding: 30px 20px;
			}
		}

		@media (max-width: 768px) {
			.modern-container {
				padding: 10px;
			}

			.modern-header {
				padding: 20px;
			}

			.modern-header h1 {
				font-size: 2rem;
			}

			.checkbox-group {
				flex-direction: column;
			}
		}

		/* 动画效果 */
		@keyframes fadeInUp {
			from {
				opacity: 0;
				transform: translateY(30px);
			}

			to {
				opacity: 1;
				transform: translateY(0);
			}
		}

		.modern-card {
			animation: fadeInUp 0.6s ease-out;
		}

		.form-section {
			animation: fadeInUp 0.6s ease-out 0.1s both;
		}

		.sidebar-section {
			animation: fadeInUp 0.6s ease-out 0.2s both;
		}

		/* 隐藏传统样式元素 */
		.tb,
		.th,
		.col,
		.cc,
		.contadd {
			all: unset;
		}

		/* 重置表格样式 */
		table.tb {
			width: 100%;
			border-collapse: separate;
			border-spacing: 0;
		}

		.tb tr {
			display: contents;
		}

		.tb td,
		.tb th {
			display: block;
			width: 100%;
			margin-bottom: 20px;
		}
	</style>

	<!-- 统一对话框样式 -->
	<style>
		.ajaxoverlay {
			position: fixed !important;
			top: 0 !important;
			left: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			z-index: 10000 !important;
			width: 100vw !important;
			height: 100vh !important;
			background: rgba(0, 0, 0, 0.4) !important;
			backdrop-filter: blur(2px) !important;
		}

		.ajaxtips {
			position: fixed !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			z-index: 10001 !important;
			max-width: 90vw !important;
			max-height: 90vh !important;
		}

		.ajaxbox {
			background: #1e293b !important;
			/* slate-800 */
			border: 1px solid #3b82f6 !important;
			/* blue-500 */
			border-radius: 12px !important;
			padding: 24px !important;
			line-height: 1.6 !important;
			box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
			min-width: 320px !important;
			max-width: 500px !important;
			color: #f8fafc !important;
			/* slate-50 */
			font-size: 16px !important;
			display: flex !important;
			flex-direction: column !important;
			align-items: center !important;
			gap: 12px !important;
		}

		.ajaxbox i {
			width: 32px !important;
			height: 32px !important;
			margin: 0 !important;
			flex-shrink: 0 !important;
			background-size: contain !important;
		}

		.ajaxbox b {
			flex: 1 !important;
			font-weight: 500 !important;
			font-size: 16px !important;
			line-height: 1.5 !important;
			margin: 0 !important;
		}

		.ajaxbox u {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
			padding: 8px 16px !important;
			border-radius: 6px !important;
			cursor: pointer !important;
			text-decoration: none !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin-left: 16px !important;
			flex-shrink: 0 !important;
		}

		.ajaxbox u:hover {
			background: #2563eb !important;
			/* blue-600 */
			transform: translateY(-1px) !important;
		}

		/* 成功状态 */
		.btrue {
			border-color: #10b981 !important;
			/* emerald-500 */
			background: #064e3b !important;
			/* emerald-900 */
		}

		.btrue i {
			background: #10b981 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.btrue i::after {
			content: "✓" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.btrue u {
			background: #10b981 !important;
		}

		.btrue u:hover {
			background: #059669 !important;
		}

		/* 错误状态 */
		.bfalse {
			border-color: #ef4444 !important;
			/* red-500 */
			background: #7f1d1d !important;
			/* red-900 */
		}

		.bfalse i {
			background: #ef4444 !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bfalse i::after {
			content: "✕" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 18px !important;
			font-weight: bold !important;
		}

		.bfalse u {
			background: #ef4444 !important;
		}

		.bfalse u:hover {
			background: #dc2626 !important;
		}

		/* 警告状态 */
		.bnote {
			border-color: #f59e0b !important;
			/* amber-500 */
			background: #78350f !important;
			/* amber-900 */
		}

		.bnote i {
			background: #f59e0b !important;
			border-radius: 50% !important;
			position: relative !important;
		}

		.bnote i::after {
			content: "!" !important;
			position: absolute !important;
			top: 50% !important;
			left: 50% !important;
			transform: translate(-50%, -50%) !important;
			color: white !important;
			font-size: 20px !important;
			font-weight: bold !important;
		}

		.bnote u {
			background: #f59e0b !important;
		}

		.bnote u:hover {
			background: #d97706 !important;
		}

		/* 加载动画优化 */
		.ajaximg {
			width: 48px !important;
			height: 48px !important;
			background: none !important;
			border: 4px solid #334155 !important;
			border-top: 4px solid #3b82f6 !important;
			border-radius: 50% !important;
			animation: spin 1s linear infinite !important;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}

			100% {
				transform: rotate(360deg);
			}
		}

		/* 确认框样式优化 */
		.ajaxbox .cf {
			margin-top: 20px !important;
			padding: 0 !important;
			display: flex !important;
			justify-content: center !important;
			gap: 12px !important;
		}

		.ajaxbox .cf .but3 {
			padding: 8px 16px !important;
			border-radius: 6px !important;
			border: none !important;
			cursor: pointer !important;
			font-weight: 500 !important;
			transition: all 0.2s !important;
			margin: 0 !important;
			float: none !important;
		}

		.ajaxbox .cf .but3:first-child {
			background: #6b7280 !important;
			/* gray-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:first-child:hover {
			background: #4b5563 !important;
			/* gray-600 */
		}

		.ajaxbox .cf .but3:last-child {
			background: #3b82f6 !important;
			/* blue-500 */
			color: white !important;
		}

		.ajaxbox .cf .but3:last-child:hover {
			background: #2563eb !important;
			/* blue-600 */
		}
	</style>
</head>

<body>

<div class="min-h-screen bg-slate-950 p-4">
	<div class="max-w-7xl mx-auto">
		<!-- 页面标题 -->
		<div class="bg-slate-900 border border-slate-700 rounded-xl shadow-lg p-6 mb-6">
			<div class="flex items-center justify-between">
				<div>
					<h1 class="text-2xl font-bold text-slate-50">分类管理</h1>
					<p class="text-slate-300 mt-1">管理您的内容分类，创建层级结构</p>
				</div>
				<button id="add"
					class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 shadow-md hover:shadow-lg">
					<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
					</svg>
					增加分类
				</button>
			</div>
	</div>

		<!-- 分类内容 -->
		<div class="bg-slate-900 border border-slate-700 rounded-xl shadow-lg overflow-hidden">
			<div id="category_content">
			<?php if (empty($category_arr)) { ?>
<div class="p-8 text-center">
	<div class="bg-yellow-100 border border-yellow-300 rounded-lg p-6">
		<svg class="w-12 h-12 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
				d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
			</path>
		</svg>
		<h3 class="text-lg font-medium text-yellow-800 mb-2">暂无分类</h3>
		<p class="text-yellow-700">请先创建分类来管理您的内容</p>
	</div>
</div>
<?php }else{ ?>
	<?php if(isset($category_arr) && is_array($category_arr)) { foreach($category_arr as $mid=>&$arr) { ?>
<div class="category mb-6">
	<!-- 分类模型标题 -->
	<div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-4 rounded-t-lg">
		<div class="grid grid-cols-12 gap-4 items-center font-medium">
			<div class="col-span-5 flex items-center">
				<svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
						d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
					</path>
				</svg>
				<?php  echo $mod_name[$mid]; ?>分类
			</div>
			<div class="col-span-2 text-center">分类别名</div>
			<div class="col-span-2 text-center">排序</div>
			<div class="col-span-2 text-center">内容数量</div>
			<div class="col-span-1 text-center">操作</div>
		</div>
	</div>

	<!-- 分类列表 -->
	<div class="bg-slate-800 border-l border-r border-b border-slate-600 rounded-b-lg">
		<?php if(isset($arr) && is_array($arr)) { foreach($arr as &$v) { ?>
		<?php 
			$v['prestr'] = str_repeat("　　", $v['pre']-1);
			$v['url'] = $_ENV['_category_class']->category_url($v['cid'], $v['alias']);
		 ?>
		<div class="cat_col grid grid-cols-12 gap-4 items-center p-4 border-b border-slate-600 hover:bg-slate-700 transition-colors cursor-pointer"
			cid="<?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>" mid="<?php echo(isset($v['mid']) ? $v['mid'] : ''); ?>" url="<?php echo(isset($v['url']) ? $v['url'] : ''); ?>">
			<div class="cat_c_1 col-span-5 flex items-center">
				<div class="c_name flex items-center" title="编号(cid): <?php echo(isset($v['cid']) ? $v['cid'] : ''); ?>&#10;分类名称: <?php echo(isset($v['name']) ? $v['name'] : ''); ?>">
					<span class="text-slate-400 mr-2"><?php echo(isset($v['prestr']) ? $v['prestr'] : ''); ?></span>
					<?php if ($v['mid'] == 1) { ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-600 text-purple-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
							</path>
						</svg>
						单页
					</span>
					<?php }elseif($v['type'] == 1) { ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-green-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
							</path>
						</svg>
						频道
					</span>
					<?php }else{ ?>
					<span
						class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-blue-100 mr-2">
						<svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
						</svg>
						列表
					</span>
					<?php } ?>
					<span class="font-medium text-slate-100"><?php echo(isset($v['name']) ? $v['name'] : ''); ?></span>
				</div>
			</div>
			<div class="cat_c_2 col-span-2 text-center">
				<span class="inline-flex items-center px-2 py-1 rounded bg-slate-700 text-slate-200 text-sm font-mono"
					title="<?php echo(isset($v['alias']) ? $v['alias'] : ''); ?>"><?php echo(isset($v['alias']) ? $v['alias'] : ''); ?></span>
			</div>
			<div class="cat_c_3 col-span-2 text-center">
				<input name="orderby" value="<?php echo(isset($v['orderby']) ? $v['orderby'] : ''); ?>" type="number"
					class="w-16 px-2 py-1 text-center border border-slate-600 bg-slate-700 text-slate-200 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm">
			</div>
			<div class="cat_c_4 col-span-2 text-center">
				<?php if ($v['mid'] != 1 && $v['type'] != 1) { ?>
				<span
					class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-slate-600 text-slate-200">
					<?php echo(isset($v['count']) ? $v['count'] : ''); ?>
				</span>
				<?php }else{ ?>
				<span class="text-slate-400">-</span>
				<?php } ?>
			</div>
			<div class="col-span-1 text-center">
				<div class="flex justify-center">
					<svg class="w-4 h-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
							d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z">
						</path>
					</svg>
				</div>
			</div>
		</div>
		<?php }} ?>
	</div>
	</div>
	<?php }} ?>
<?php } ?>
			</div>
		</div>
	</div>
</div>

<!-- 分类设置对话框模板 -->
<script id="set_dialog" type="text/html" _mid="2" _type="0" _upid="0">
	<div class="rounded-lg">
		<form method="post" action="index.php?u=category-set-ajax-1" class="space-y-6">
		<input name="cid" type="hidden" />

			<!-- 基本设置 -->
			<div class="border-b border-slate-600 pb-6">
				<h3 class="text-lg font-semibold text-slate-50 mb-4">基本设置</h3>

				<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
					<!-- 分类模型 -->
					<div class="col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-3">分类模型</label>
						<div class="flex flex-wrap gap-3">
							<?php if(isset($mod_name) && is_array($mod_name)) { foreach($mod_name as $k=>&$v) { ?>
							<label class="flex items-center space-x-2 bg-slate-700 hover:bg-slate-600 px-4 py-2 rounded-lg cursor-pointer transition-colors">
								<input class="text-blue-500 focus:ring-blue-500" name="mid" type="radio" value="<?php echo(isset($k) ? $k : ''); ?>">
								<span class="text-sm font-medium text-slate-200"><?php echo(isset($v) ? $v : ''); ?></span>
							</label>
							<?php }} ?>
						</div>
					</div>

					<!-- 分类类型 -->
					<div id="i_type" class="col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-3">分类类型</label>
						<div class="space-y-2">
							<label class="flex items-start space-x-3 bg-slate-700 hover:bg-slate-600 p-3 rounded-lg cursor-pointer transition-colors">
								<input class="mt-1 text-blue-500 focus:ring-blue-500" name="type" type="radio" value="0">
								<div>
									<span class="text-sm font-medium text-slate-200">列表</span>
									<p class="text-xs text-slate-400">可发表内容，下级不可建分类</p>
								</div>
							</label>
							<label class="flex items-start space-x-3 bg-slate-700 hover:bg-slate-600 p-3 rounded-lg cursor-pointer transition-colors">
								<input class="mt-1 text-blue-500 focus:ring-blue-500" name="type" type="radio" value="1">
								<div>
									<span class="text-sm font-medium text-slate-200">频道</span>
									<p class="text-xs text-slate-400">不可发表内容，下级可建分类（频道分类下才能建子分类）</p>
								</div>
							</label>
						</div>
					</div>

					<!-- 所属频道 -->
					<div id="i_upid" class="col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-2">所属频道</label>
						<select name="upid" id="upid" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
							<option value="0">无</option>
						</select>
					</div>

					<!-- 分类名称 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">
							<span class="text-red-500">*</span> 分类名称
						</label>
						<input name="name" type="text" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" placeholder="请输入分类名称" />
					</div>

					<!-- 分类别名 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">
							<span class="text-red-500">*</span> 分类别名
						</label>
						<input name="alias" type="text" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" placeholder="请输入分类别名" />
					</div>

					<!-- 分类描述 -->
					<div class="col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-2">分类描述</label>
						<input name="intro" type="text" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" placeholder="请输入分类描述" />
					</div>

					<!-- 单页内容 -->
					<div id="i_page_content" style="display:none" class="col-span-2">
						<label class="block text-sm font-medium text-slate-300 mb-2">单页内容</label>
						<textarea id="page_content" name="page_content" rows="6" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" placeholder="请输入单页内容"></textarea>
					</div>

					<!-- 排列顺序 -->
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">排列顺序</label>
						<input name="orderby" value="0" type="number" class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" />
					</div>
				</div>
			</div>

			<!-- SEO设置 -->
			<div class="border-b border-slate-600 pb-6">
				<h3 class="text-lg font-semibold text-slate-50 mb-4">SEO设置</h3>
				<div class="grid grid-cols-1 gap-4">
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">SEO标题</label>
						<input class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" type="text" name="seo_title" placeholder="请输入SEO标题" />
					</div>
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">SEO关键字</label>
						<input class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" type="text" name="seo_keywords" placeholder="请输入SEO关键字，多个关键字用逗号分隔" />
					</div>
					<div>
						<label class="block text-sm font-medium text-slate-300 mb-2">SEO描述</label>
						<input class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" type="text" name="seo_description" placeholder="请输入SEO描述" />
					</div>
				</div>
			</div>

			<!-- 模板设置 -->
			<div>
				<h3 class="text-lg font-semibold text-slate-50 mb-4">模板设置</h3>
				<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div id="i_cate_tpl">
						<label class="block text-sm font-medium text-slate-300 mb-2">列表模板</label>
						<input class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" type="text" name="cate_tpl" id="cate_tpl" placeholder="请输入模板文件名" />
					</div>
					<div id="i_show_tpl">
						<label class="block text-sm font-medium text-slate-300 mb-2">内容模板</label>
						<input class="w-full px-3 py-2 bg-slate-700 border border-slate-600 text-slate-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 placeholder-slate-400" type="text" name="show_tpl" id="show_tpl" placeholder="请输入模板文件名" />
					</div>
				</div>
			</div>

			
	</form>
	</div>
</script>

<style>
	/* 现代化对话框样式 */
	#twdialogbox {
		border-radius: 12px !important;
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
		border: none !important;
	}

	#twdialog_title {
		background: #0f172a !important;
		/* slate-950 - 统一主背景 */
		color: white !important;
		border-radius: 12px 12px 0 0 !important;
		padding: 20px !important;
		border-bottom: 1px solid #475569 !important;
		/* slate-600 */
		flex-shrink: 0 !important;
	}

	#twdialog_title span {
		font-size: 18px !important;
		font-weight: 600 !important;
	}

	#twdialog_content {
		padding: 24px !important;
		background: #1e293b !important;
		/* slate-800 */
	}

	#twdialog_button {
		background: #334155 !important;
		/* slate-700 */
		border-top: 1px solid #475569 !important;
		/* slate-600 */
		padding: 16px 24px !important;
		border-radius: 0 0 12px 12px !important;
		flex-shrink: 0 !important;
		display: flex !important;
		justify-content: flex-end !important;
		align-items: center !important;
	}

	#twdialog_button .ok {
		background: #3b82f6 !important;
		color: white !important;
		border: none !important;
		padding: 10px 24px !important;
		border-radius: 8px !important;
		font-weight: 500 !important;
		transition: all 0.2s !important;
	}

	#twdialog_button .ok:hover {
		background: #2563eb !important;
		transform: translateY(-1px) !important;
		box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
	}

	#twdialog_button .close {
		background: #6b7280 !important;
		color: white !important;
		border: none !important;
		padding: 10px 24px !important;
		border-radius: 8px !important;
		font-weight: 500 !important;
		margin-right: 12px !important;
		transition: all 0.2s !important;
	}

	#twdialog_button .close:hover {
		background: #4b5563 !important;
		transform: translateY(-1px) !important;
	}

	/* 修复对话框定位和显示 - 全新的定位方案 */
	#twdialog {
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		width: 100vw !important;
		height: 100vh !important;
		z-index: 9999 !important;
		background: rgba(0, 0, 0, 0.6) !important;
		backdrop-filter: blur(4px) !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		padding: 2rem !important;
	}

	#twdialogbox {
		position: relative !important;
		background: #1e293b !important;
		/* slate-800 */
		border-radius: 12px !important;
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
		border: 1px solid #475569 !important;
		/* slate-600 */
		width: 100% !important;
		max-width: 900px !important;
		max-height: 90vh !important;
		margin: 0 !important;
		/* 重置margin，使用flexbox居中 */
		overflow: hidden !important;
		display: flex !important;
		flex-direction: column !important;
		transform: scale(1) !important;
		transition: all 0.3s ease-in-out !important;
	}

	/* 显示动画 */
	#twdialog:not(.show) #twdialogbox {
		transform: scale(0.9) !important;
		opacity: 0 !important;
	}

	#twdialog.show #twdialogbox {
		transform: scale(1) !important;
		opacity: 1 !important;
	}

	#twdialog_content {
		flex: 1 !important;
		overflow-y: auto !important;
		padding: 24px !important;
		background: #1e293b !important;
		/* slate-800 */
	}

	/* 隐藏原来的overlay，使用新的背景遮罩 */
	#twoverlay {
		display: none !important;
	}

	/* 关闭按钮样式优化 */
	#twdialog_title a {
		position: absolute !important;
		right: 20px !important;
		top: 50% !important;
		transform: translateY(-50%) !important;
		color: #94a3b8 !important;
		/* slate-400 */
		text-decoration: none !important;
		font-size: 24px !important;
		line-height: 1 !important;
		width: 32px !important;
		height: 32px !important;
		display: flex !important;
		align-items: center !important;
		justify-content: center !important;
		border-radius: 6px !important;
		transition: all 0.2s !important;
	}

	#twdialog_title a:hover {
		background: #475569 !important;
		/* slate-600 */
		color: #f8fafc !important;
		/* slate-50 */
	}

	#twdialog_title a:before {
		content: "×" !important;
	}

	/* 响应式设计 */
	@media (max-width: 768px) {
		#twdialog {
			padding: 1rem !important;
		}

		#twdialogbox {
			max-width: 100% !important;
			max-height: 95vh !important;
		}

		#twdialog_title {
			padding: 16px !important;
		}

		#twdialog_content {
			padding: 16px !important;
		}

		#twdialog_button {
			padding: 12px 16px !important;
			flex-direction: column !important;
			gap: 8px !important;
		}

		#twdialog_button .close {
			margin-right: 0 !important;
		}
	}
</style>

<!-- 优化的提示框样式 -->
<style>
	/* 重新定义twAjax.alert样式，确保提示框正确显示 */
	.ajaxoverlay {
		position: fixed !important;
		top: 0 !important;
		left: 0 !important;
		right: 0 !important;
		bottom: 0 !important;
		z-index: 10000 !important;
		width: 100vw !important;
		height: 100vh !important;
		background: rgba(0, 0, 0, 0.4) !important;
		backdrop-filter: blur(2px) !important;
	}

	.ajaxtips {
		position: fixed !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		z-index: 10001 !important;
		max-width: 90vw !important;
		max-height: 90vh !important;
	}

	.ajaxbox {
		background: #1e293b !important; /* slate-800 */
		border: 1px solid #3b82f6 !important; /* blue-500 */
		border-radius: 12px !important;
		padding: 24px !important;
		line-height: 1.6 !important;
		box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
		min-width: 320px !important;
		max-width: 500px !important;
		color: #f8fafc !important; /* slate-50 */
		font-size: 16px !important;
		display: flex !important;
		align-items: center !important;
		gap: 12px !important;
	}

	.ajaxbox i {
		width: 32px !important;
		height: 32px !important;
		margin: 0 !important;
		flex-shrink: 0 !important;
		background-size: contain !important;
	}

	.ajaxbox b {
		flex: 1 !important;
		font-weight: 500 !important;
		font-size: 16px !important;
		line-height: 1.5 !important;
		margin: 0 !important;
	}

	.ajaxbox u {
		background: #3b82f6 !important; /* blue-500 */
		color: white !important;
		padding: 8px 16px !important;
		border-radius: 6px !important;
		cursor: pointer !important;
		text-decoration: none !important;
		font-weight: 500 !important;
		transition: all 0.2s !important;
		margin-left: 16px !important;
		flex-shrink: 0 !important;
	}

	.ajaxbox u:hover {
		background: #2563eb !important; /* blue-600 */
		transform: translateY(-1px) !important;
	}

	/* 成功状态 */
	.btrue {
		border-color: #10b981 !important; /* emerald-500 */
		background: #064e3b !important; /* emerald-900 */
	}

	.btrue i {
		background: #10b981 !important;
		border-radius: 50% !important;
		position: relative !important;
	}

	.btrue i::after {
		content: "✓" !important;
		position: absolute !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		color: white !important;
		font-size: 18px !important;
		font-weight: bold !important;
	}

	.btrue u {
		background: #10b981 !important;
	}

	.btrue u:hover {
		background: #059669 !important;
	}

	/* 错误状态 */
	.bfalse {
		border-color: #ef4444 !important; /* red-500 */
		background: #7f1d1d !important; /* red-900 */
	}

	.bfalse i {
		background: #ef4444 !important;
		border-radius: 50% !important;
		position: relative !important;
	}

	.bfalse i::after {
		content: "✕" !important;
		position: absolute !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		color: white !important;
		font-size: 18px !important;
		font-weight: bold !important;
	}

	.bfalse u {
		background: #ef4444 !important;
	}

	.bfalse u:hover {
		background: #dc2626 !important;
	}

	/* 警告状态 */
	.bnote {
		border-color: #f59e0b !important; /* amber-500 */
		background: #78350f !important; /* amber-900 */
	}

	.bnote i {
		background: #f59e0b !important;
		border-radius: 50% !important;
		position: relative !important;
	}

	.bnote i::after {
		content: "!" !important;
		position: absolute !important;
		top: 50% !important;
		left: 50% !important;
		transform: translate(-50%, -50%) !important;
		color: white !important;
		font-size: 20px !important;
		font-weight: bold !important;
	}

	.bnote u {
		background: #f59e0b !important;
	}

	.bnote u:hover {
		background: #d97706 !important;
	}

	/* 加载动画优化 */
	.ajaximg {
		width: 48px !important;
		height: 48px !important;
		background: none !important;
		border: 4px solid #334155 !important;
		border-top: 4px solid #3b82f6 !important;
		border-radius: 50% !important;
		animation: spin 1s linear infinite !important;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	/* 确认框样式优化 */
	.ajaxbox .cf {
		margin-top: 20px !important;
		padding: 0 !important;
		display: flex !important;
		justify-content: flex-end !important;
		gap: 12px !important;
	}

	.ajaxbox .cf .but3 {
		padding: 8px 16px !important;
		border-radius: 6px !important;
		border: none !important;
		cursor: pointer !important;
		font-weight: 500 !important;
		transition: all 0.2s !important;
		margin: 0 !important;
		float: none !important;
	}

	.ajaxbox .cf .but3:first-child {
		background: #6b7280 !important; /* gray-500 */
		color: white !important;
	}

	.ajaxbox .cf .but3:first-child:hover {
		background: #4b5563 !important; /* gray-600 */
	}

	.ajaxbox .cf .but3:last-child {
		background: #3b82f6 !important; /* blue-500 */
		color: white !important;
	}

	.ajaxbox .cf .but3:last-child:hover {
		background: #2563eb !important; /* blue-600 */
	}
</style>

<script type="text/javascript">
var models = <?php echo(isset($models) ? $models : ''); ?>;
load_event(); // 加载事件

// 默认无编辑器
	window.editor_init = function () {
	// 编辑器API
	window.editor_api = {
		// 单页内容
			page_content: {
				obj: $('#page_content'),
				get: function () {
				return this.obj.val();
			},
				set: function (s) {
				return this.obj.val(s);
			},
				focus: function () {
				return this.obj.focus();
			}
		}
	}
}

	// 添加分类函数
	function bindAddCategoryEvent() {
		$("#add").click(function () {
			if ($(this).text().trim() == "增加分类") {
				$(this).html('<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>关闭窗口');

		// 加载会话框
		load_dialog();
		$("#twdialog_title span").html("增加分类");

		// 初始化表单
		var mid = $("#set_dialog").attr("_mid");
		var type = $("#set_dialog").attr("_type");
		var upid = $("#set_dialog").attr("_upid");

		$("#twdialog input[name='mid']").val([mid]);
		$("#twdialog input[name='type']").val([type]);
		setFormVal(mid, type);

		// 加载所属频道
		loadCategoryUpid(mid, upid);
			} else {
		remove_dialog();
	}
});
	}

// 鼠标放上去事件
function load_event() {
	$(".cat_col").hover(
			function () {
				$(this).addClass("bg-blue-50 border-blue-200");

				if ($(".more_but").length < 1) {
				var cid = $(this).attr("cid");
				var mid = $(this).attr("mid");
				var url = $(this).attr("url");

					var s = '<div class="more_but flex space-x-2 ml-auto">';
					s += '<button onclick="edit(' + cid + ')" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors">编辑</button>';
					s += '<a class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors" href="' + url + '" target="_blank">查看</a>';
					s += '<button onclick="del(' + cid + ')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">删除</button>';
				s += '</div>';

				$(this).find(".cat_c_1").append(s);
			}
		},
			function () {
				$(this).removeClass("bg-blue-50 border-blue-200");
			$(".more_but").remove();
		}
	);

	// 快速修改排序
		$(".cat_c_3 input[name='orderby']").focusin(function () {
		$(this).attr("v", $(this).val());
		}).focusout(function () {
		var orderby = $(this).val();
		var old_orderby = $(this).attr("v");
			if (orderby == old_orderby) return;
		var cid = $(this).parent().parent().attr("cid");
			twAjax.post("index.php?u=category-edit_orderby-ajax-1", { cid: cid, orderby: orderby }, function (data) {
			var data = toJson(data);
				if (window.twExit) return;
				if (data.err == 0) getAllCate();
		});
	});
}

// 加载对话框 (增加或编辑时使用)
function load_dialog() {
		// 移除现有对话框
	$.twDialog("remove");

		// 创建新对话框，设置合适的尺寸
		var H = Math.max(600, Math.floor(window.innerHeight * 0.8));
		var W = Math.max(800, Math.floor(window.innerWidth * 0.8));

		$.twDialog({
			content: $("#set_dialog").html(),
			resizable: true,
			open: false,  // 先不显示，我们手动控制显示动画
			modal: true,
			width: W,
			height: H,
			minW: 600,
			minH: 400
		});

		// 优化对话框显示效果
		setTimeout(function () {
			var $dialog = $("#twdialog");
			var $dialogBox = $("#twdialogbox");

			if ($dialog.length && $dialogBox.length) {
				// 清除插件添加的内联样式，让CSS样式生效
				$dialogBox.removeAttr('style');
				$dialog.removeAttr('style').addClass('show');

				// 显示对话框
				$dialog.show();

				// 添加显示动画
				setTimeout(function() {
					$dialog.addClass('show');
				}, 10);

				// 确保内容区域高度正确
				var titleHeight = $("#twdialog_title").outerHeight();
				var buttonHeight = $("#twdialog_button").outerHeight();
				var maxContentHeight = Math.floor(window.innerHeight * 0.8) - titleHeight - buttonHeight - 48; // 减去padding
				$("#twdialog_content").css('max-height', maxContentHeight + 'px');
			}
		}, 50);

	// 增加关闭窗口事件
	$("#twdialog_title a,#twdialog_button .close").click(remove_dialog);

		// 点击对话框外部区域关闭
		$("#twdialog").click(function(e) {
			if (e.target === this) {
				remove_dialog();
			}
		});

		// ESC键关闭对话框
		$(document).on('keydown.dialog', function(e) {
			if (e.keyCode === 27 && $("#twdialog").length > 0) { // ESC键
				remove_dialog();
			}
		});

	// 改变分类模型
		$("#twdialog input[name='mid']").change(function () {
		var mid = $(this).val();
		setFormVal(mid, $("#twdialog").attr("type"));

		// 加载所属频道
		loadCategoryUpid(mid);
	});

	// 改变分类类型
		$("#twdialog input[name='type']").change(function () {
		setFormVal($("#twdialog").attr("mid"), $(this).val());
	});

	// 触发提交 (添加 & 编辑)
		$("#twdialog_button>.ok").click(function () { $("#twdialog form").submit(); });

	// 拦截表单提交
		twAjax.submit("#twdialog form", function (data) {
		twAjax.alert(data);
			if (window.twData.err == 0) {
			// 增加时记录状态
			var cid = $("#twdialog input[name='cid']").val();
				if (!cid) {
				var fields = $("#twdialog input[name='mid'], #twdialog input[name='type'], #twdialog input[name='upid']").serializeArray();
					$.each(fields, function (i, field) {
						if (field.name == "mid") {
						$("#set_dialog").attr("_mid", field.value);
						} else if (field.name == "type") {
						$("#set_dialog").attr("_type", field.value);
						} else if (field.name == "upid") {
						$("#set_dialog").attr("_upid", field.value);
					}
				});
			}

			remove_dialog();
			getAllCate();
		}
	});
}

	// 移除编辑器 - 添加关闭动画
function remove_dialog() {
		var $dialog = $("#twdialog");
		if ($dialog.length) {
			// 移除键盘事件监听
			$(document).off('keydown.dialog');

			// 添加关闭动画
			$dialog.removeClass('show');
			setTimeout(function() {
	$.twDialog("remove");
				$("#add").html('<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>增加分类');
			}, 200); // 等待动画完成
		} else {
			// 如果没有对话框，直接恢复按钮状态
			$("#add").html('<svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>增加分类');
		}
}

// 设置表单内的值
function setFormVal(mid, type) {
	$("#twdialog").attr("mid", mid);
	$("#twdialog").attr("type", type);

		if (mid == 1) {
		$("#i_type").hide();
		$("#i_page_content").show();
		$("#i_show_tpl").hide();
			$("#i_upid>label").html("上级分类");

		// 单页时把类型设置为 0
		$("#twdialog input[name='type']").val([0]);
		$("#twdialog").attr("type", 0);

		editor_init();
		} else {
		$("#i_type").show();
		$("#i_page_content").hide();
		$("#i_show_tpl").show();
			$("#i_upid>label").html("所属频道");
	}
		$("#i_cate_tpl>label").html(mid == 1 ? "单页模板" : (type == 1 ? "频道模板" : "列表模板"));

	// 默认模板设置
	var edit_mid = $("#twdialog").attr("edit_mid");
	var edit_type = $("#twdialog").attr("edit_type");
		if (!!edit_mid && edit_mid == mid && edit_type == type) {
		$("#cate_tpl").val($("#cate_tpl").attr("v"));
		$("#show_tpl").val($("#show_tpl").attr("v"));
		} else {
			var k = "models-mid-" + mid;
		$("#cate_tpl").val(mid != 1 && type == 1 ? models[k]["index_tpl"] : models[k]["cate_tpl"]);
		$("#show_tpl").val(models[k]["show_tpl"]);
	}
}

// 编辑分类
function edit(cid) {
		twAjax.postd("index.php?u=category-get_category_json-ajax-1", { cid: cid }, function (data) {
		data = toJson(data);
			if (window.twEixt) return;
		twAjax.remove();

		// 加载会话框
		load_dialog();
		$("#twdialog_title span").html("编辑分类");

		// 遍历设置表单值
			for (var v in data) {
				$("#twdialog [name='" + v + "']").val([data[v]]);
		}

		// 如果分类已发布了内容或有下级分类就不允许改变模型和类型
			if (data.count > 0 || data.son_cate) {
				$("#twdialog input[name='mid']").each(function () {
					if ($(this).val() != data.mid) {
						$(this).attr("disabled", "disabled").parent().addClass("opacity-50");
				}
			});

				$("#twdialog input[name='type']").each(function () {
					if ($(this).val() != data.type) {
						$(this).attr("disabled", "disabled").parent().addClass("opacity-50");
				}
			});
		}

		// 记录模板设置
		$("#cate_tpl").attr("v", data.cate_tpl);
		$("#show_tpl").attr("v", data.show_tpl);

		// 设置表单内的值
		$("#twdialog").attr("edit_mid", data.mid);
		$("#twdialog").attr("edit_type", data.type);
		setFormVal(data.mid, data.type);

		// 加载所属频道
		loadCategoryUpid(data.mid, data.upid, data.cid);
	});
}

// 删除分类
function del(cid) {
		twAjax.confirm("删除不可恢复，确定删除？", function () {
			twAjax.postd("index.php?u=category-del-ajax-1", { cid: cid }, function (data) {
			twAjax.alert(data);
				if (window.twData.err == 0) $(".category dl[cid='" + cid + "']").remove();
		});
	});
}

// 加载所属频道
function loadCategoryUpid(mid, upid, noid) {
		twAjax.get("index.php?u=category-get_category_upid-ajax-1-mid-" + mid + "-upid-" + Math.max(0, upid) + "-noid-" + Math.max(0, noid) + "&r=" + time(), function (data) {
		data = toJson(data);
			if (window.twEixt) return;
		$("#upid").html(data.upid);
	});
}

// 获取所有分类列表
function getAllCate() {
		setTimeout(function () {
			twAjax.get("index.php?u=category-get_category_content&r=" + time(), function (html) {
			$("#category_content").html(html);
			load_event();
		});
	}, 500);
}

	// 页面加载完成后初始化
	$(function () {
		// 绑定增加分类按钮事件
		bindAddCategoryEvent();

		// 初始加载分类列表
		getAllCate();

		//监听窗口大小变化，自动调整对话框大小
		$(window).resize(function () {
			if ($("#twdialog").length > 0) {
				// 重新计算内容区域的最大高度
				var titleHeight = $("#twdialog_title").outerHeight();
				var buttonHeight = $("#twdialog_button").outerHeight();
				var maxContentHeight = Math.floor(window.innerHeight * 0.8) - titleHeight - buttonHeight - 48;
				$("#twdialog_content").css('max-height', maxContentHeight + 'px');
			}
		});
	});
</script>


// Hook from plugin: editor_tinymce
<script type="text/javascript">
window.editor_init = function() {
	// 如果页面内容编辑器被隐藏，不初始化
	if ($("#page_content:hidden").length > 0) return;

	// 防止重复加载
	if (typeof window.tinymce_category_loaded !== 'undefined') return;
	window.tinymce_category_loaded = true;

	console.log('TinyMCE 分类编辑器加载开始...');

	// 获取插件路径
	var pluginPath = (function() {
		var url = window.location.href;
		var pathParts = url.split('/');
		var adminIndex = -1;
		for (var i = 0; i < pathParts.length; i++) {
			if (pathParts[i] === 'admin') {
				adminIndex = i;
				break;
			}
		}

		if (adminIndex >= 0) {
			weburl = pathParts.slice(0, adminIndex).join('/');
		} else {
			weburl = url.substring(0, url.lastIndexOf('/admin'));
		}

		return weburl + '/kbcms/plugin/editor_tinymce/';
	})();

	// 如果TinyMCE已经加载，直接初始化
	if (typeof tinymce !== 'undefined') {
		initCategoryTinyMCE();
	} else {
		// 加载本地TinyMCE
		var script = document.createElement('script');
		script.src = pluginPath + 'tinymce.min.js';
		script.onload = function() {
			console.log('TinyMCE 脚本加载完成');
			setTimeout(initCategoryTinyMCE, 100);
		};
		script.onerror = function() {
			console.error('TinyMCE 脚本加载失败');
			fallbackCategoryEditor();
		};
		document.head.appendChild(script);
	}

	function initCategoryTinyMCE() {
		console.log('开始初始化分类TinyMCE编辑器');

		// 获取后台路径
		var admurl = (function() {
			var url = document.URL || location.href;
			return url.substr(0, url.lastIndexOf("/"));
		})();

		// 如果编辑器已存在，先销毁
		if (tinymce.get('page_content')) {
			tinymce.remove('#page_content');
		}

		tinymce.init({
			selector: '#page_content',
			language: 'zh_CN',
			language_url: pluginPath + 'langs/zh_CN.js',
			height: 300,
			menubar: false,
			branding: false,
			resize: true,
			statusbar: true,

			// 插件配置 - 分类页面使用简化配置
			plugins: [
				'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
				'searchreplace', 'visualblocks', 'code', 'fullscreen',
				'table', 'help', 'wordcount', 'paste'
			],

			// 工具栏配置 - 简化版
			toolbar: 'undo redo | formatselect | ' +
				'bold italic | alignleft aligncenter alignright | ' +
				'bullist numlist | link image | code fullscreen',

			// 样式配置
			content_style: 'body { font-family: 微软雅黑, "Microsoft YaHei", Arial, sans-serif; font-size: 14px; line-height: 1.6; padding: 10px; }',

			// 图片上传配置
			images_upload_url: admurl + '/index.php?u=attach-upload_image&type=img',
			images_upload_handler: function(blobInfo, success, failure) {
				var xhr = new XMLHttpRequest();
				xhr.withCredentials = false;
				xhr.open('POST', admurl + '/index.php?u=attach-upload_image&type=img');

				xhr.onload = function() {
					if (xhr.status != 200) {
						failure('HTTP Error: ' + xhr.status);
						return;
					}

					try {
						var json = JSON.parse(xhr.responseText);
						if (!json || json.err != 0) {
							failure('上传失败: ' + (json.msg || '未知错误'));
							return;
						}
						success(json.url);
					} catch (e) {
						failure('响应解析错误');
					}
				};

				xhr.onerror = function() {
					failure('网络错误');
				};

				var formData = new FormData();
				formData.append('upfile', blobInfo.blob(), blobInfo.filename());
				xhr.send(formData);
			},

			// 初始化完成回调
			init_instance_callback: function(editor) {
				console.log('TinyMCE 分类编辑器初始化完成, ID:', editor.id);

				// 设置初始内容
				var initialContent = $('#page_content').val() || '';
				if (initialContent) {
					editor.setContent(initialContent);
				}

				// 监听内容变化
				editor.on('change keyup setcontent', function() {
					// 同步内容到原始textarea
					$('#page_content').val(editor.getContent());
				});
			}
		});

		// 创建兼容的editor_api接口
		window.editor_api = {
			page_content: {
				obj: null,
				get: function() {
					var editor = tinymce.get('page_content');
					if (editor) {
						return editor.getContent();
					}
					return $('#page_content').val() || '';
				},
				set: function(content) {
					var editor = tinymce.get('page_content');
					if (editor) {
						editor.setContent(content || '');
					} else {
						$('#page_content').val(content || '');
					}
				},
				focus: function() {
					var editor = tinymce.get('page_content');
					if (editor) {
						editor.focus();
					}
				}
			}
		};

		console.log('TinyMCE 分类编辑器 API 创建完成');
	}

	// 降级处理函数
	function fallbackCategoryEditor() {
		console.log('使用分类降级编辑器');

		// 简单的工具栏
		var toolbar = $('<div class="simple-toolbar" style="margin-bottom: 10px; padding: 8px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px;">' +
			'<button type="button" onclick="document.execCommand(\'bold\')" style="margin-right: 5px; padding: 3px 8px; font-size: 12px;">粗体</button>' +
			'<button type="button" onclick="document.execCommand(\'italic\')" style="margin-right: 5px; padding: 3px 8px; font-size: 12px;">斜体</button>' +
			'<span style="color: #999; margin-left: 10px; font-size: 12px;">TinyMCE加载失败，使用简化编辑器</span>' +
		'</div>');

		$('#page_content').before(toolbar);
		$('#page_content').attr('contenteditable', 'true').css({
			'min-height': '300px',
			'border': '1px solid #ddd',
			'padding': '10px',
			'background': 'white'
		});

		// 创建兼容的API
		window.editor_api = {
			page_content: {
				obj: $('#page_content'),
				get: function() {
					return $('#page_content').html();
				},
				set: function(content) {
					$('#page_content').html(content || '');
				},
				focus: function() {
					$('#page_content').focus();
				}
			}
		};

		console.log('分类降级编辑器初始化完成');
	}
};

// 如果页面已经加载完成，立即执行
if (document.readyState === 'complete') {
	editor_init();
} else {
	$(document).ready(function() {
		editor_init();
	});
}
</script>


</body>

</html>