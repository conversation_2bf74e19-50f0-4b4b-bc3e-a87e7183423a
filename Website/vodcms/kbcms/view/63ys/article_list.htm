<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>{php}$page = isset($_GET['page'])? $_GET['page']:'';$typetname = str_replace("片","电影",$tw['curr_catename']);
if($tw_var['upid'] == 1){$catname = $typetname."免费电影在线观看 - ".$tw['curr_catename']."大全 - 高清电影完整版在线播放";
}elseif($tw_var['upid'] == 2){$catname =  $typetname."电视剧在线观看-".$tw['curr_catename']."电视剧免费全集观看";
}elseif($tw_var['cid'] == 3){$catname =  $tw[titles].'动漫'."好看的动画片-".$tw['curr_catename']."动画片手机在线";
}else{$catname = $typetname."手机MP4无线观看-".$tw['curr_catename'].'电影排行榜-' ;}{/php}{$catname} {if:!empty($page)}第{$page}页{/if} - 63影视</title>
    <meta name="keywords" content="{if:$tw_var['upid'] == 1}{$tw[titles]}高清电影在线播放,{$tw[titles]}视频在线观看,{$tw[titles]}在线手机观看,免费在线播放{$tw[titles]}{elseif:$tw_var['upid'] == 2}{$tw[titles]}MP4手机观看,电视剧免费播放全集{$tw[titles]},{$tw[titles]}手机无线播放,{$tw[titles]}电视剧手机在线观看{elseif:$tw_var['cid'] == 3}{$tw[titles]}经典动漫电影,少儿动画片,动漫动画片推荐,{$tw[titles]}迅雷电影下载{else}{$tw[titles]}在线观看免费观看,{$tw[titles]}电视剧免费全集观看,{$tw[titles]}HD高清下载,{$tw[titles]}电影排行榜{/if}">
    <meta name="description" content="{if:$tw_var['upid'] == 1}{$tw[titles]}排行榜十强{$tw[titles]},经典电影{$tw[titles]},高清大片在线{$tw[titles]},经典电影{$tw[titles]},迅雷下载{$tw[titles]}{elseif:$tw_var['upid'] == 2}{$tw[titles]}全集在线观看,{$tw[titles]}支持全集VIP免费观看,{$tw[titles]}免费追剧大全,{$tw[titles]}最新视频播放,{$tw[titles]}电视剧天天看高清影视,{$tw[titles]}BD免费下载{elseif:$tw_var['cid'] == 3}
{$tw[titles]}BT种子下载,{$tw[titles]}动漫动画片推荐,{$tw[titles]}动画片手机在线,{$tw[titles]}最新动画片{else}{$tw[titles]}免费全集手机观看,电影排行榜{$tw[titles]},必看的经典{$tw[titles]},最新{$tw[titles]}{/if}">
    {inc:js.htm}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 主容器 -->
    <div class="min-h-screen flex flex-col">
        {inc:header.htm}

        <!-- 主内容区 -->
        <main class="flex-1 px-4 py-6 max-w-7xl mx-auto w-full">
            <!-- 广告位 -->
            <div class="mb-6">
                <div class="js_topad"></div>
            </div>

            <!-- 内容主体 -->
            <div class="space-y-6">
                <!-- 面包屑导航 -->
                <nav class="bg-white rounded-lg shadow-sm p-4">
                    <div class="flex items-center text-sm text-gray-600 space-x-2">
                        <span class="text-blue-600 font-medium">当前位置：</span>
                        <a href="https://www.63ys.com/" class="text-blue-600 hover:text-blue-800">首页</a>
                        <span>&gt;</span>
                        <a href="{$tw['curr_cateurl']}" target="_blank" class="text-blue-600 hover:text-blue-800">{$tw['curr_catename']}</a>
                        <span>&gt;&gt;</span>
                        <span class="text-gray-800">{if:!empty($page)}第{$page}页{/if}</span>
                        <div class="ml-auto">
                            <a href="{$tw['curr_catetopurl']}" title="{$tw['curr_catename']}人气榜单" target="_blank" class="text-blue-600 hover:text-blue-800 text-sm">按人气排行榜</a>
                        </div>
                    </div>
                </nav>

                <!-- 筛选区域 -->
                <div class="bg-white rounded-lg shadow-sm p-4">
                    {php}$catname_dy = array("喜剧","爱情","搞笑","战争","科幻","黑帮","枪战","动作","剧情","纪录","古装","犯罪","伦理","三级","成人","情色","色情","黄色","神话","西部","漫威","昭氏","音乐","运动","冒险","传记","恐怖","悬疑","惊悚","校园","青春","短片","同性","罪犯","罪案","文艺","限制","禁片","经典","史诗","暴力","女性","特工","R级");
$catname_tv = array("美剧","英剧","日剧","韩剧","港剧","台剧","网络剧","言情","悬疑","医务","战争","喜剧","情景","谍战","历史","刑侦","警匪","烧脑","科幻","都市","农村","奇幻","魔幻","武侠","神话","古装","剧情","家庭");
$catname_dm = array("魔法","亲子","热血","少儿","搞笑","爱情","美女","运动","机战","励志","冒险","格斗","奇幻","H漫","BL动漫");
$catname_zy = array("选秀","情感","访谈","搞笑","游戏","职场","娱乐","真人秀","生活","体育","盛会","财经","亲子","纪实","脱口秀","网络节目","美食");
$type_year = array("2022","2021","2020","2019","2018","2017","2016","2015","2014","2013","2012","2011","2010");
$type_zy = array("BD720P","BD1080P","HD720P","HD1080P","1280","DVD","英语","韩语","法语","日语","俄语","印地语","德语","西班牙","中字","粤语","国语");{/php}
                    {block:category tyname="1"} {php}$type_dir = $data['tyname'];{/php}{/block}

                    <!-- 影片类型筛选 -->
                    <div class="mb-4">
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-blue-500 mr-2"></span>
                            影片类型
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            {php}
if($tw['upid'] == 1){ $catname = $catname_dy; }
elseif($tw['upid'] == 2){ $catname = $catname_tv; }
elseif($tw_var['cid'] == 3){ $catname = $catname_dm; }
else{ $catname = $catname_dy;}
{/php}
                            {loop:$catname $v}
                            <a class="px-3 py-1 text-sm rounded-full border transition-colors {if:$tw['titles'] ==$v}bg-blue-500 text-white border-blue-500{else}bg-gray-50 text-gray-700 border-gray-200 hover:bg-blue-50 hover:border-blue-300{/if}" href="/{$type_dir}{$tw['upid']}_{$v}_{$tw['year']}_{$tw['zy']}.html" title="{$v}全集迅雷下载">{$v}</a>
                            {/loop}
                        </div>
                    </div>

                    <!-- 资源筛选 -->
                    <div class="mb-4">
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-green-500 mr-2"></span>
                            资源类型
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            {loop:$type_zy $v}
                            {php}if(strpos($v,'粤语') !== false){ $yue = str_replace('粤语','粤',$v); }else{ $yue = $v; }{/php}
                            <a href="/{$type_dir}{$tw['upid']}___{$tw['year']}_{$yue}.html" title="{$v}全集" class="px-3 py-1 text-sm rounded-full bg-gray-50 text-gray-700 border border-gray-200 hover:bg-green-50 hover:border-green-300 transition-colors">
                                {php}if(strpos($v,'粤') !== false){ $v='粤语';}{/php}{$v}
                            </a>
                            {/loop}
                        </div>
                    </div>

                    <!-- 年份筛选 -->
                    <div>
                        <h5 class="text-gray-700 font-medium mb-3 flex items-center">
                            <span class="w-1 h-4 bg-purple-500 mr-2"></span>
                            年份
                        </h5>
                        <div class="flex flex-wrap gap-2">
                            {loop:$type_year $v}
                            {php}if(strpos($v,'年代') !== false){ $year = str_replace('年代','s',$v); }else{ $year = $v;}{/php}
                            <a href="/{$type_dir}{$tw['upid']}___{$year}__.html" class="px-3 py-1 text-sm rounded-full bg-gray-50 text-gray-700 border border-gray-200 hover:bg-purple-50 hover:border-purple-300 transition-colors">{$v}</a>
                            {/loop}
                        </div>
                    </div>
                </div>

                <!-- 影片列表 -->
                {block:global_cate pagenum="30" dateformat="Y-m-d" orderby="dateline"}
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="space-y-0">
                        {loop:$gdata[list] $v}
                        <div class="border-b border-gray-100 last:border-b-0 p-4 hover:bg-gray-50 transition-colors">
                            <div class="flex space-x-4">
                                <!-- 海报 -->
                                <div class="flex-shrink-0 w-20 sm:w-24 md:w-28">
                                    <a href="{$v[url]}" title="{$v[title]}" target="_blank" class="block aspect-[3/4] overflow-hidden rounded-lg shadow-sm">
                                        <img data-original="{$tw['up_img_url']}{$v[pic]}" class="w-full h-full object-cover lazypic hover:scale-105 transition-transform duration-300" alt="{$v[title]}海报">
                                    </a>
                                </div>

                                <!-- 信息 -->
                                <div class="flex-1 min-w-0">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                        <a href="{$v[url]}" target="_blank" title="{$v[subject]}" class="hover:text-blue-600 transition-colors">{$v[subject]}</a>
                                    </h3>

                                    <div class="space-y-1 text-sm text-gray-600">
                                        <p class="line-clamp-1">
                                            <span class="text-gray-500">导演：</span>
                                            {if:!empty($v['daoyan_arr'][0]['name'])}
                                                {loop:$v[daoyan_arr] $v2}<a href="{$v2[url]}" target="_blank" class="text-blue-600 hover:text-blue-800">{$v2[name]}</a> {/loop}
                                            {else}未知{/if}
                                        </p>

                                        <p class="line-clamp-1">
                                            <span class="text-gray-500">演员：</span>
                                            {if:!empty($v['yanyuan_arr'][0]['name'])}
                                                {loop:$v[yanyuan_arr] $v2}<a href="{$v2[url]}" target="_blank" class="text-blue-600 hover:text-blue-800">{$v2[name]}</a> {/loop}
                                            {else}未知{/if}
                                        </p>

                                        <p class="flex flex-wrap items-center gap-2">
                                            <span class="text-gray-500">分类：</span>
                                            <a href="{$v[year_url]}" target="_blank" class="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">{if:!empty($v[year])}{$v[year]}{else}2019{/if}</a>
                                            <a href="{$v[cate_url]}" title="{$v[cate_name]}" target="_blank" class="px-2 py-0.5 bg-green-100 text-green-800 rounded text-xs">{$v[cate_name]}</a>
                                            {if:isset($v['tag_arr'])}
                                                {loop:$v[tag_arr] $v2}<a href="{$v2[url]}" class="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs hover:bg-gray-200">{$v2[name]}</a>{/loop}
                                            {/if}
                                        </p>

                                        {php}$time = date("Y-m-d");{/php}
                                        <p class="text-xs">
                                            <span class="text-gray-500">时间：</span>
                                            <span class="{if:$time == $v[date]}text-red-500 font-medium{else}text-gray-600{/if}">{$v[date]}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {/loop}
                    </div>

                    <!-- 分页 -->
                    <div class="p-4 border-t border-gray-100 bg-gray-50">
                        <div class="text-center">
                            {$gdata[pages]}
                        </div>
                    </div>
                </div>
                {/block}

                <!-- 推荐区域 (仅桌面端显示) -->
                <div class="hidden lg:block bg-white rounded-lg shadow-sm p-4">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <span class="w-1 h-5 bg-yellow-500 mr-2"></span>
                        {$tw['curr_catename']}推荐
                    </h3>
                    {block:list limit="32" orderby="id" titlenum="40"}
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {loop:$data[list] $v}
                        <div class="border border-gray-200 rounded p-2 hover:border-yellow-300 transition-colors">
                            <a href="{$v[url]}" title="{$v[title]}迅雷下载" target="_blank" class="text-sm text-gray-700 hover:text-yellow-600 line-clamp-1">{$v[subject]}</a>
                        </div>
                        {/loop}
                    </div>
                    {/block}
                </div>
            </div>
        </main>

        {inc:footer.htm}
    </div>
</body>
</html>