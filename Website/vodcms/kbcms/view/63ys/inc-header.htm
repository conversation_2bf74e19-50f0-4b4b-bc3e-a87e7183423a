<!-- PC端和移动端响应式导航栏 -->
<header class="bg-white shadow-md">
    <!-- 顶部区域 -->
    <div class="headtop bg-gray-50 py-2">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <div class="logo">
                    <a href="https://www.63ys.com" class="flex items-center">
                        <img src="{$tw[tpl]}images/logo.png" alt="63影视" title="63影视" class="h-8" />
                    </a>
                </div>

                <!-- 右侧搜索和链接 -->
                <div class="flex items-center space-x-4">
                    <!-- PC端顶部链接 -->
                    <div class="hidden lg:flex items-center space-x-4 text-gray-600" style="font-size: 12px;">
                        <a href="https://www.63ys.com/type.html" title="影片筛选" target="_blank" class="hover:text-blue-600">影片筛选</a>
                        <a href="https://www.63ys.com/tag-top/" target="_blank" class="hover:text-blue-600">关键词库</a>
                        <a href="https://www.63ys.com" title="加入收藏" class="hover:text-blue-600">收藏本站</a>
                    </div>

                    <!-- 搜索框 -->
                    <div class="header_search">
                        <form id="search_form" method="get" action="/index.php" class="flex">
                            <input type="hidden" name="u" value="search-index" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 hidden sm:block"
                                   type="text" name="keyword" value="{$keyword}" autocomplete="off" placeholder="搜索影片..." style="font-size: 12px;" />
                            <input class="px-3 py-2 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 w-40 sm:hidden"
                                   type="text" name="keyword" value="{$keyword}" autocomplete="off" placeholder="搜索..." style="font-size: 12px;" />
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>
                        </form>
                    </div>

                    <!-- 移动端菜单按钮 -->
                    <div class="lg:hidden">
                        <button id="mobile-menu-btn" class="p-2 text-gray-700 hover:text-blue-600 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- PC端主导航栏 -->
    <div class="layout bg-blue-600 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu">
                <ul class="flex">
                    <li class="hover:bg-blue-700 {if:empty($tw_var['topcid'])}bg-white{/if}">
                        <a href="https://www.63ys.com/" class="block px-6 py-3 {if:empty($tw_var['topcid'])}text-black{else}text-white{/if} hover:text-blue-100" style="font-size: 12px;">首页</a>
                    </li>
                    {block:category cid="1" mid="2" type="child"}
                    {loop:$data $v}
                    <li class="hover:bg-blue-700 {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'bg-white'; }{/php}">
                        <a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}" class="block px-6 py-3 {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-black'; }else{ echo 'text-white'; }{/php} hover:text-blue-100" style="font-size: 12px;">{$v[name]}</a>
                    </li>
                    {/loop}
                    {/block}
                </ul>
            </div>
        </div>
    </div>

    <!-- PC端子导航栏 -->
    <div class="layout border-b border-gray-200 bg-gray-50 hidden lg:block">
        <div class="max-w-7xl mx-auto">
            <div class="navmenu_sub py-2">
                {block:category cid="2" mid="2" type="child"}
                <ul class="flex flex-wrap">
                    {loop:$data $v}
                    <li class="mr-6 mb-1 {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'font-semibold'; }{/php}">
                        <a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}" class="text-gray-700 hover:text-blue-600" style="font-size: 12px;">{$v[name]}</a>
                    </li>
                    {/loop}
                </ul>
                {/block}

                {block:category tyname="1"}
                <ul class="flex flex-wrap mt-2 pt-2 border-t border-gray-200">
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_色情__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">色情</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_情色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">情色</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_理论__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">理论</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_限制__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">限制</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_三级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">三级</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_禁片__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">禁片</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_R级__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">R级</a></li>
                    <li class="mr-4 mb-1"><a href="/{$data['tyname']}1_黄色__.html" target="_blank" class="text-gray-500 hover:text-red-600" style="font-size: 12px;">黄色</a></li>
                </ul>
                {/block}
            </div>
        </div>
    </div>
</header>

<!-- 移动端侧边栏遮罩 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden lg:hidden"></div>

<!-- 移动端侧边栏 -->
<div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 transform -translate-x-full transition-transform duration-300 ease-in-out lg:hidden">
    <div class="p-6">
        <!-- 侧边栏头部 -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <span class="text-white font-bold text-xl">63</span>
                </div>
                <span class="text-xl font-bold">63影视</span>
            </div>
            <button id="close-sidebar" class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center hover:bg-teal-600 transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- 移动端搜索框 -->
        <div class="mb-6">
            <form method="get" action="/index.php" class="relative">
                <input type="hidden" name="u" value="search-index" />
                <input type="text" name="keyword" value="{$keyword}" placeholder="搜索..." class="w-full bg-gray-800 text-white pl-4 pr-10 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500" style="font-size: 12px;">
                <button type="submit" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-teal-400 hover:text-teal-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>
            </form>
        </div>

        <!-- 移动端导航菜单 -->
        <nav class="space-y-2">
            <!-- 首页 -->
            <a href="https://www.63ys.com/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors {if:empty($tw_var['topcid'])}bg-gray-800 text-white{/if}" style="font-size: 12px;">
                首页
            </a>

            <!-- 电影 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('movie-menu')" style="font-size: 12px;">
                    <span>电影</span>
                    <svg class="w-4 h-4 transition-transform" id="movie-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="movie-menu" class="ml-4 space-y-1 hidden">
                    {block:category cid="1" mid="2" type="child"}
                    {loop:$data $v}
                    <a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; }{/php}" style="font-size: 12px;">{$v[name]}</a>
                    {/loop}
                    {/block}
                </div>
            </div>

            <!-- 电视剧 - 可展开的二级菜单 -->
            <div class="mb-2">
                <div class="flex items-center justify-between px-4 py-3 text-gray-300 cursor-pointer hover:text-white hover:bg-gray-800 rounded-lg transition-colors" onclick="toggleSubmenu('tv-menu')" style="font-size: 12px;">
                    <span>电视剧</span>
                    <svg class="w-4 h-4 transition-transform" id="tv-arrow" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
                <div id="tv-menu" class="ml-4 space-y-1 hidden">
                    {block:category cid="2" mid="2" type="child"}
                    {loop:$data $v}
                    <a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}" class="block px-4 py-2 text-gray-400 hover:text-teal-400 transition-colors {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){ echo 'text-teal-400'; }{/php}" style="font-size: 12px;">{$v[name]}</a>
                    {/loop}
                    {/block}
                </div>
            </div>

            <!-- 动漫 - 直接链接 -->
            <a href="/dongman/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                动漫
            </a>

            <!-- 短剧 - 直接链接 -->
            <a href="/duanju/" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                短剧
            </a>

            <!-- 快捷链接 -->
            <div class="border-t border-gray-700 pt-4 mt-4">
                <a href="https://www.63ys.com/type.html" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    影片筛选
                </a>
                <a href="https://www.63ys.com/tag-top/" target="_blank" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    关键词库
                </a>
                <a href="https://www.63ys.com" class="flex items-center px-4 py-3 text-gray-300 hover:text-white hover:bg-gray-800 rounded-lg transition-colors" style="font-size: 12px;">
                    收藏本站
                </a>
            </div>
        </nav>
    </div>
</div>

<script>
// 移动端菜单控制
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuBtn = document.getElementById('mobile-menu-btn');
    const sidebar = document.getElementById('sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');
    const closeSidebar = document.getElementById('close-sidebar');

    // 打开侧边栏
    mobileMenuBtn.addEventListener('click', function() {
        sidebar.classList.remove('-translate-x-full');
        sidebarOverlay.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    });

    // 关闭侧边栏
    function closeSidebarMenu() {
        sidebar.classList.add('-translate-x-full');
        sidebarOverlay.classList.add('hidden');
        document.body.style.overflow = '';
    }

    closeSidebar.addEventListener('click', closeSidebarMenu);
    sidebarOverlay.addEventListener('click', closeSidebarMenu);

    // ESC键关闭侧边栏
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSidebarMenu();
        }
    });
});

// 移动端二级菜单展开/收起功能
function toggleSubmenu(menuId) {
    const menu = document.getElementById(menuId);
    const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

    if (menu.classList.contains('hidden')) {
        menu.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        menu.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}
</script>

