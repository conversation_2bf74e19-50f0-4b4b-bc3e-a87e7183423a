<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="renderer" content="webkit">
<meta http-equiv="Cache-Control" content="no-transform">
<meta http-equiv="Cache-Control" content="no-siteapp">
<meta name="applicable-device" content="pc,mobile">
<meta name="format-detection" content="telephone=no">
<link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">
<!-- 引用前端 Tailwind CSS -->
<link rel="stylesheet" href="/static/css/output.css">
<!-- 移动端优化脚本 -->
<script src="/static/js/jquery.min1.11.3.js"></script>
<script>
// 移动端适配
(function() {
    // 禁用双击缩放
    var lastTouchEnd = 0;
    document.addEventListener('touchend', function (event) {
        var now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            event.preventDefault();
        }
        lastTouchEnd = now;
    }, false);

    // 移动端菜单切换
    window.toggleMobileMenu = function() {
        var menu = document.getElementById('mobile-menu');
        if (menu) {
            menu.classList.toggle('hidden');
        }
    };
})();

// 侧边栏控制脚本 - 增强版
(function() {
    console.log('侧边栏脚本开始加载...');

    // 侧边栏控制函数
    function toggleSidebar() {
        console.log('toggleSidebar 被调用');
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');

        console.log('sidebar元素:', sidebar);
        console.log('overlay元素:', overlay);

        if (sidebar && overlay) {
            const isHidden = sidebar.classList.contains('-translate-x-full');
            console.log('当前侧边栏状态 - 隐藏:', isHidden);

            if (isHidden) {
                // 显示侧边栏
                sidebar.classList.remove('-translate-x-full');
                overlay.classList.remove('hidden');
                console.log('显示侧边栏');
            } else {
                // 隐藏侧边栏
                sidebar.classList.add('-translate-x-full');
                overlay.classList.add('hidden');
                console.log('隐藏侧边栏');
            }
        } else {
            console.error('找不到侧边栏或遮罩元素');
        }
    }

    function toggleSubmenu(menuId) {
        console.log('toggleSubmenu 被调用，menuId:', menuId);
        const menu = document.getElementById(menuId);
        const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

        if (menu) {
            menu.classList.toggle('hidden');
            console.log('切换子菜单:', menuId);
        }
        if (arrow) {
            arrow.classList.toggle('rotate-180');
        }
    }

    // 绑定事件的函数
    function bindEvents() {
        console.log('开始绑定事件...');

        // 移动端菜单按钮
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        if (mobileMenuBtn) {
            mobileMenuBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('移动端菜单按钮被点击');
                toggleSidebar();
            });
            console.log('移动端菜单按钮事件已绑定');
        } else {
            console.warn('找不到移动端菜单按钮');
        }

        // 关闭侧边栏按钮
        const closeSidebarBtn = document.getElementById('close-sidebar');
        if (closeSidebarBtn) {
            closeSidebarBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('关闭按钮被点击');
                toggleSidebar();
            });
            console.log('关闭按钮事件已绑定');
        } else {
            console.warn('找不到关闭侧边栏按钮');
        }

        // 遮罩层点击
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('遮罩层被点击');
                toggleSidebar();
            });
            console.log('遮罩层事件已绑定');
        } else {
            console.warn('找不到侧边栏遮罩');
        }

        // 键盘ESC关闭侧边栏
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const sidebar = document.getElementById('sidebar');
                const overlay = document.getElementById('sidebar-overlay');

                if (sidebar && overlay && !sidebar.classList.contains('-translate-x-full')) {
                    console.log('ESC键关闭侧边栏');
                    toggleSidebar();
                }
            }
        });
        console.log('键盘事件已绑定');
    }

    // 多种方式确保事件绑定
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindEvents);
    } else {
        bindEvents();
    }

    // 备用绑定方式
    setTimeout(bindEvents, 100);
    setTimeout(bindEvents, 500);

    // 全局函数，供onclick使用
    window.toggleSubmenu = toggleSubmenu;
    window.toggleSidebar = toggleSidebar;

    console.log('侧边栏脚本加载完成');
})();
</script>