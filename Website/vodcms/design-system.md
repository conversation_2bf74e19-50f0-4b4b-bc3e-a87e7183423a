# VODCMS 后台管理系统设计规范

## 配色标准

### 主色调 (Primary Colors)
- **主品牌色**: `#3b82f6` (blue-500) - 用于主要按钮、链接、强调元素
- **主品牌色悬停**: `#2563eb` (blue-600) - 主品牌色的悬停状态
- **主品牌色浅色**: `#60a5fa` (blue-400) - 用于激活状态、高亮文本
- **主品牌色深色**: `#1d4ed8` (blue-700) - 用于按下状态

### 背景色系 (Background Colors)
- **主背景**: `#0f172a` (slate-950) - 页面主背景
- **次级背景**: `#1e293b` (slate-800) - 卡片、侧边栏背景
- **三级背景**: `#334155` (slate-700) - 输入框、次级元素背景
- **四级背景**: `#475569` (slate-600) - 悬停状态背景
- **五级背景**: `#64748b` (slate-500) - 激活状态背景

### 文本色系 (Text Colors)
- **主文本**: `#f8fafc` (slate-50) - 主要文本内容
- **次级文本**: `#e2e8f0` (slate-200) - 次要文本内容
- **三级文本**: `#cbd5e1` (slate-300) - 标签、说明文字
- **四级文本**: `#94a3b8` (slate-400) - 占位符、禁用文本
- **五级文本**: `#64748b` (slate-500) - 最淡的文本

### 边框色系 (Border Colors)
- **主边框**: `#334155` (slate-700) - 主要边框
- **次级边框**: `#475569` (slate-600) - 次要边框
- **激活边框**: `#3b82f6` (blue-500) - 激活状态边框

### 状态色系 (Status Colors)
- **成功色**: `#10b981` (emerald-500) - 成功状态
- **成功色悬停**: `#059669` (emerald-600)
- **警告色**: `#f59e0b` (amber-500) - 警告状态
- **警告色悬停**: `#d97706` (amber-600)
- **错误色**: `#ef4444` (red-500) - 错误状态
- **错误色悬停**: `#dc2626` (red-600)
- **信息色**: `#06b6d4` (cyan-500) - 信息状态
- **信息色悬停**: `#0891b2` (cyan-600)

## 组件样式标准

### 按钮 (Buttons)
```css
/* 主要按钮 */
.btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-slate-50 px-4 py-2 rounded-lg font-medium transition-colors duration-200;
}

/* 次要按钮 */
.btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-slate-200 px-4 py-2 rounded-lg font-medium transition-colors duration-200;
}

/* 危险按钮 */
.btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-slate-50 px-4 py-2 rounded-lg font-medium transition-colors duration-200;
}
```

### 输入框 (Input Fields)
```css
.input-field {
    @apply bg-slate-800 border border-slate-700 text-slate-50 rounded-lg px-3 py-2 transition-all duration-200 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/50;
}
```

### 卡片 (Cards)
```css
.card {
    @apply bg-slate-800 border border-slate-700 rounded-xl shadow-lg;
}

.card-header {
    @apply bg-gradient-to-r from-slate-700 to-slate-600 px-6 py-4 rounded-t-xl border-b border-slate-600;
}

.card-body {
    @apply p-6;
}
```

### 标签页 (Tabs)
```css
.tab-item {
    @apply bg-slate-600 text-slate-200 rounded-t-lg px-3 py-2 border border-slate-500 border-b-0 transition-all duration-200;
}

.tab-item.active {
    @apply bg-slate-950 text-blue-400 border-blue-500 font-medium;
}

.tab-item:hover:not(.active) {
    @apply bg-slate-500 text-blue-300;
}
```

### 导航 (Navigation)
```css
.nav-item {
    @apply text-slate-300 hover:text-slate-100 hover:bg-slate-700 px-3 py-2 rounded-lg transition-colors duration-200;
}

.nav-item.active {
    @apply text-blue-400 bg-slate-700 font-medium;
}
```

## 间距标准 (Spacing)

### 内边距 (Padding)
- **xs**: `0.25rem` (1) - 4px
- **sm**: `0.5rem` (2) - 8px
- **md**: `0.75rem` (3) - 12px
- **lg**: `1rem` (4) - 16px
- **xl**: `1.5rem` (6) - 24px
- **2xl**: `2rem` (8) - 32px

### 外边距 (Margin)
- 使用与内边距相同的标准

### 间隙 (Gap)
- **xs**: `0.25rem` (1) - 4px
- **sm**: `0.5rem` (2) - 8px
- **md**: `0.75rem` (3) - 12px
- **lg**: `1rem` (4) - 16px
- **xl**: `1.5rem` (6) - 24px

## 圆角标准 (Border Radius)

- **sm**: `0.25rem` (rounded) - 4px - 小元素
- **md**: `0.5rem` (rounded-lg) - 8px - 按钮、输入框
- **lg**: `0.75rem` (rounded-xl) - 12px - 卡片
- **xl**: `1rem` (rounded-2xl) - 16px - 大型容器

## 阴影标准 (Shadows)

- **sm**: `shadow-sm` - 轻微阴影
- **md**: `shadow-lg` - 中等阴影，用于卡片
- **lg**: `shadow-xl` - 较强阴影，用于弹窗
- **xl**: `shadow-2xl` - 最强阴影，用于模态框

## 字体标准 (Typography)

### 字体大小
- **xs**: `0.75rem` (12px) - 小标签、说明文字
- **sm**: `0.875rem` (14px) - 次要文本
- **base**: `1rem` (16px) - 正文
- **lg**: `1.125rem` (18px) - 小标题
- **xl**: `1.25rem` (20px) - 中标题
- **2xl**: `1.5rem` (24px) - 大标题

### 字体粗细
- **normal**: `400` - 正常文本
- **medium**: `500` - 中等强调
- **semibold**: `600` - 较强强调
- **bold**: `700` - 强调标题

## 动画标准 (Animations)

### 过渡时间
- **快速**: `150ms` - 按钮悬停
- **标准**: `200ms` - 一般交互
- **慢速**: `300ms` - 复杂动画

### 缓动函数
- **ease-in-out**: 标准缓动
- **ease-out**: 进入动画
- **ease-in**: 退出动画

## 使用指南

1. **保持一致性**: 所有页面都应使用相同的配色方案
2. **层次分明**: 使用不同的背景色和文本色来区分内容层次
3. **交互反馈**: 所有可交互元素都应有悬停和激活状态
4. **无障碍性**: 确保文本和背景的对比度符合无障碍标准
5. **响应式**: 所有组件都应在不同屏幕尺寸下正常显示

## 更新记录

- **v1.2** (2024-12-19): 63ys模板全面改版 - 移动端优先响应式设计
  - ✅ 完全重构 `inc-js.htm` - 现代化头部配置，移动端优化
  - ✅ 全新设计 `inc-header.htm` - 响应式导航，汉堡菜单，搜索功能
  - ✅ 重构 `index.htm` - 现代化首页设计，轮播图，分类导航，影片推荐
  - ✅ 改造 `article_list.htm` - 移动端优先列表页，筛选功能，卡片式布局
  - ✅ 更新 `inc-footer.htm` - 现代化底部设计，社交媒体，回到顶部
  - ✅ 移动端优先设计理念，完美兼容PC端
  - ✅ 使用 Tailwind CSS 工具类，去除所有旧CSS依赖
  - ✅ 添加懒加载、平滑滚动等现代化功能
  - ✅ 优化SEO和可访问性

- **v1.1** (2024-12-19): 新增前端 Tailwind CSS 集成
  - ✅ 创建前端专用配置文件 `tailwind-frontend.config.js`
  - ✅ 新增前端样式文件 `static/css/input.css` 和 `static/css/output.css`
  - ✅ 实现前端后台样式分离，支持独立构建
  - ✅ 新增丰富的前端组件库（按钮、卡片、表单、警告框等）
  - ✅ 添加自定义工具类（阴影、渐变、毛玻璃效果）
  - ✅ 更新构建脚本，支持 `npm run build:frontend` 等命令
  - ✅ 创建示例模板 `frontend-example.html` 展示使用方法

- **v1.0** (2024-12-19): 初始版本，建立基础配色和组件标准