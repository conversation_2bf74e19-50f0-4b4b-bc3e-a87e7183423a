模板设计说明

采用tailwind css ，响应式设计，兼容移动端，具体友好设计，颜色采用蓝色#0078ee +白色 #ffffff 设计，字体是12px
移动端设备：统一图标显示 3列，圆角显示，字体占图片的居中位置。
页面：展示都以3列图片，如果是pc端 以 6列 显示
导航：
目前顶级频道：电影 id=1 ,电视剧 id=3 ,动漫 id=4,短剧 id=5 , 当前配置：电影｜电视剧 底下有子分类，用child，动漫｜短剧 用
<ul><li {if:empty($tw_var['topcid'])} class="cur_index"{/if}><a href="https://www.63ys.com">首页</a></li>{block:category cid="1" mid="2" type="child"}{loop:$data $v}<li {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){  echo 'class="cur_index"' ;  }{/php}><a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}">{$v[name]}</a></li>{/loop}{/block}</ul>
自定义筛选页：
{block:category tyname="1"} typname = 1 对标是否伪静态
{block:category tyname="1"}<li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_色情__.html" target="_blank">色情</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_动作__.html" target="_blank">情色</a></li></ul>{/block}

这个筛选页的
1通常是电影
2通常是电视剧
这是为了seo做的筛选页标签，类似聚合tag一样，还有就是筛选资源， 2015年英文动作电影 。

模板语法：
1. 包含模板
{inc:header.htm}

2. {hook:header_before.htm}
模板钩子(方便插件修改模板)

3. {php}{/php}
模板支持PHP代码 (不支持<??><?php?>的写法)

4. {block:}{/block}
模板模块

5. {loop:}{/loop}
数组遍历

6. {if:} {else} {eleseif:} {/if}
逻辑判断

7. {$变量}
显示变量
vodcms自用标签大全

模板结构及说明
注：模版在 根目录/kbcms/view/下，其目录下一个文件夹即为一套模版

| 模板名称 | 模板说明 |
| css/global.css ---全站 CSS 样式 |
| js/main.js ---全站 JS 代码 |
| index.htm ---首页 |
| index_blog.htm ---类似博客首页(替换 index.htm 即可) |
| 404.htm ---404 错误页 |
| comment.htm ---评论页 |
| search.htm ---搜索页 |
| inc-footer.htm ---公共底部 |
| inc-header.htm ---公共头部 |
| inc-right.htm ---公共右侧 |
| page_show.htm ---单页 |
| article_index.htm ---文章频道页 |
| article_list.htm ---文章列表页 |
| article_show.htm ---文章内容页 |
| product_index.htm ---产品频道页 |
| product_list.htm ---产品列表页 |
| product_show.htm ---产品内容页 |
| photo_index.htm ---图集频道页 |
| photo_list.htm ---图集列表页 |
| photo_show.htm ---图集内容页 |
| tag_list.htm ---标签列表页 |
| tag_top.htm ---标签排行页 |
通用标签
模版中调用写法：{inc:header.htm} （需将对应模版命名为inc-header.htm）
网站首页连接：{$tw[weburl]}
网站名称：{$tw[webname]}
网站标题：{$tw[titles]}
SEO标题：{$tw[seo_title]}
seo关键词：{$tw[seo_keywords]}
seo描述：{$tw[seo_description]}
网站根目录：{$tw[webdir]}
模版路径：{$tw[tpl]}
备案号：{$tw[beian]}
邮箱：{$tw[webmail]}
统计代码：{$tw[tongji]}
分类别名：{$tw_var[alias]}
域名：{$tw[webdomain]}
当前模板名称：{$tw[theme]}
根域名：{$tw[webroot]}
分类描述：{$tw_var[intro]}
分类名称：{$tw_var[name]}
您的当前位置（面包屑导航）：
{loop:$tw_var[place] $v} <a href="{$v[url]}">{$v[name]}</a>{/loop}



 
AI写代码html

模块
用途	模块标记	说明
内容列表模块	block:list	常用cid参数，而非mid
内容属性列表模块	block:list_flag	0=图片 1=推荐 2=热点 3=头条 4=精选 5=幻灯
内容列表排行模块	block:list_top	常按照view数排行
遍历内容列表模块	block:listeach	子栏目（内容）循环输出
分类展示模块	block:category	同级(sibling)、子级(child)、父级(parent)、顶级(top)
内容页模块	block:global_show
相关内容模块	block:taglike	参数type=1为显示第一个tag相关内容，2为随机显示一个tag相关内容
标签列表模块	block:taglist	参数orderby 排序方式 (参数有 tagid count)
评论列表模块	block:comment	参数humandate 人性化时间显示 默认开启 (开启: 1 关闭: 0)
评论页模块	block:global_comment
导航模块	block:navigate	最多支持两级，更多需二次开发
搜索模块	block:global_search	2：文章；3：产品；4：图集
友情链接	block:links
标签列表页模块	block:global_taglist
单页模块	block:global_page
列表页模块	block:global_cate	列表页使用，频道页不推荐使用
模型页模块	block:global_blog	类似博客列表

导航

{block:navigate}

<div class="nav">

<div class="n_c">

<dt><a href="{$tw[weburl]}">首页</a></dt>

{loop:$data $v} //调取顶级导航

<dt><a href="{$v[url]}" target="{$v[target]}">{$v[name]}</a></dt>

<dd>

{loop:$v[son] $v2}<a href="{$v2[url]}" target="{$v2[target]}">{$v2[name]}</a>{/loop}

//调取二级导航

</dd>

{/loop}

</div>

</div>

{/block}

简版写法：

{block:navigate}

{loop:$data $v}

<a href="{$v[url]}" target="{$v[target]}">{$v[name]}</a>

{/loop}

{/block}

内容列表：

{block:list cid="2"dateformat="Y-m-d" limit="2" orderby="time" titlenum="28"}

<a class="more" href="{$data[cate_url]}">{$data[cate_name]}</a>//栏目连接 栏目名称

{loop:$data[list] $v}

<a href="{$v[url]}"><img src="{$v[pic]}"><b>{$v[subject]}</b><i>{$v[intro]}</i>{$v[date]}</a>

{/loop}

{/block}

cid 分类ID （如：cid='1' 或 cid='1,2'）如果不填：自动识别 (不推荐用于读取频道分类，影响性能)

mid 模型id，1是单页2是文章3是产品4是图集。(当cid为不设置时，设置mid才能生效，否则程序自动识别)

limit 表示调用条数（如：limit='1' ）也可以表示调用范围（如：limit='1,2'）

orderby 不填默认表示按照内容ID排序（可选排序方式 dateline、id）

titlenum 标题长度（如：titlenum="18"）

direnum 描述长度（如：direnum="180"）

dateformat 时间格式 （如：date="Y年m月d日 H时i分s秒"）默认为 Y-m-d H:i:s

orderway 降序,升序，降序(-1),升序(1) 默认为降序

level 显示top(顶级)、child(子级)、parent(父级)、sibling(同级)、为空则不分级（如：level="child"）

incid 包含CID（如：incid="1,3"）

notcid 不包含CID（如：notcid="4,9"）

文章链接：{$v[url]}

文章标题：{$v[subject]}

文章简介：{$v[intro]}

文章缩略图：{$v[pic]}

文章时间：{$v[date]}

内容属性列表:

{block:list_flag flag="1" limit="8" orderby="time" titlenum="28"}

{loop:$data[list] $v}

{$v[url]}（文章链接）

{$v[title]}（全部标题）

{$v[date]}（日期）

{$v[subject]}（受字数限制的标题）

{/loop}

{/block}

内容属性列表模块

说明：

flag 属性 ID (如果不添加此属性默认为 0即：有缩略图) [可选 1=推荐 2=热点 3=头条 4=精选 5=幻灯]

cid 分类 ID 如果不填：自动识别 (不推荐用于读取频道分类，影响性能)

mid 模型 ID (当 cid 不设置时候，设置 mid 才能生效，否则程序自动识别)

dateformat 时间格式

titlenum 标题长度

intronum 简介长度

orderby 排序方式

orderway 降序(-1),升序(1)

start 开始位置

limit 显示几条

内容列表排行模块

推荐用于内容页。

{block:list_top mid="2" orderby="views" life="600"}

{loop:$data[list] $v}

<li><a href="{$v[url]}" title="{$v[title]}发表于:{$v[date]}访问数:{$v450 views}" >{$v[subject]}</a></li>

{/loop}

{/block}

说明:

cid 分类ID 如果不填，为自动识别；如果cid为0时，为整个模型

mid 模型ID (当cid为0时，设置mid才能生效，否则程序自动识别)

dateformat 时间格式

titlenum 标题长度

intronum 简介长度

orderby 排序方式 最后评论排列[lastdate] 评论数排列[comments] 点击数排列450 views

orderway 降序(-1),升序(1)

start 开始位置

limit 显示几条

life 缓存时间 (开启二级缓存后，点击数排列才会有缓存时间)

遍历内容列表模块

{block:listeach limit="8"}

{loop:$data $v}

{$v[cate_url]}（栏目链接）

{$v[cate_name]}（栏目名称）

{loop:$v[list] $lv}

{$lv[date]}（文章时间）

{$lv[url]}（文章链接）

{$lv[subject]}（文章标题）

{/loop}

{/loop}

{/block}

遍历内容列表模块（子栏目循环列出）

说明

cid 频道分类 ID。 如果不填代表0

mid 模型 ID (默认为2即文章模型。当cid不设置时mid设置才会生效)

dateformat 时间格式

titlenum 标题长度

intronum 简介长度

orderby 排序方式

orderway 降序(-1),升序(1) 默认：-1

limit 显示几条

分类展示模块

{block:category type="sibling"}

<div class="b10">

{loop:$data $v}

<li><a href="{$v[url]}">{$v[name]}</a></li>

{/loop}

</div>

{/block}

分类展示模块（相关分类）

cid 分类 ID 如果不填：自动识别

type 显示类型。同级(sibling)、子级(child)、父级(parent)、顶级(top)

。如果不填，默认显示同级。

mid 模型 ID (默认值为2。即：文章模型)

内容页模块

推荐用于内容页。

{block:global_show show_prev_next="1"}

<h1>{$gdata[title]}</h1>

<div class="info">

<span>作者: {$gdata[author]}</span>

<span>来源: {$gdata[source]}</span>

<span>日期: {$gdata[date]}</span>

<span>人气: <font id="views">-</font></span>

<span>评论: <a href="{$gdata[comment_url]}">{$gdata[comments]}</a></span>

</div>

<div class="content">{$gdata[content]}</div> //以下代码，如果有设置tag。则显示tag

{if:isset($gdata['tag_arr'])}

<div class="sh_l">标签：{loop:$gdata[tag_arr] $v} <a href="{$v[url]}">{$v[name]}</a>{/loop}</div>

{/if}

//以下代码，如果有设置显示上（下）一篇，则显示

<div class="turn">

<ul>

<li>上一篇：{if:isset($gdata[prev][url])}<a href="{$gdata[prev][url]}">{$gdata[prev][title]}</a>{else}没有了{/if}</li>

<li>下一篇：{if:isset($gdata[next][url])}<a href="{$gdata[next][url]}">{$gdata[next][title]}</a>{else}没有了{/if}</li>

</ul>

</div>

{/block}

相关内容模块

推荐用于内容页。

{block:taglike type="1"}

{loop:$data[list] $v}

{$v[date]}（时间）

{$v[url]}（链接）

{$v[title]}（全部标题）

{$v[subject]}（受限标题）

{/loop}

{/block}

说明

type (1 为显示第一个 tag 相关内容，2 为随机显示一个 tag 相关内容)

titlenum 标题长度

intronum 简介长度

dateformat 时间格式

orderway 降序(-1),升序(1)

start 开始位置

limit 显示几条

标签列表模块

推荐用于tag排行页面。

{block:taglist limit="1000"}

{loop:$data[list] $v}

<a href="{$v[url]}">{$v[name]}({$v[count]})</a>

{/loop}

{/block}

说明：

orderby 排序方式 (参数有 tagid count)

orderway 降序(-1),升序(1)

limit 显示几条标签

评论列表模块

内容页使用。

{block:comment pagenum="20" firstnum="20"}

//以下代码为留言提交表单。注意2个隐藏input不可或缺。

<form action="{$tw[webdir]}index.php?u=comment-post-ajax-1" method="post">

<textarea name="content" tabindex="1">文明上网，理性发言</textarea>

<input name="author" type="text" value="访客" />

<input type="submit" value="发表评论" />

<input type="hidden" name="cid" value="{$gdata[cid]}" />

<input type="hidden" name="id" value="{$gdata[id]}" />

</form>

//评论总数以及连接。

<a href="{$gdata[comment_url]}">更多</a>

共<font>{$gdata[comments]}</font> 条评论

//判断是否有评论

{if:empty($data[list])}

暂无评论

{else}

//如果有评论，则输出评论

{loop:$data[list] $v $k}

<div class="post_header"><b>{$v[author]} ({$v[ip]})</b> {$v[date]}</div>

<div class="post_message">{$v[content]}</div>

{/loop}

{/if}

{/block}

可选参数：

pagenum 每页显示条数

firstnum 首次显示条数 (有利于SEO)

dateformat 时间格式

humandate 人性化时间显示 默认开启 (开启: 1 关闭: 0)

orderway 降序(-1),升序(1)

评论页模块

推荐用于评论页。

{block:global_comment humandate="1"}

{$gdata[title]}//评论原文章标题

{$gdata[url]}//评论原文章连接

{$gdata[intro]}//评论原文章简介

{$gdata[comments]}//评论总条数

//以下代码显示评论

{loop:$gdata[list] $v}

{$v[author]} //评论作者

{$v[ip]}//评论作者IP

{$v[date]}//评论时间

{$v[content]}//评论内容

{/loop}

共 {$gdata[comments]} 条评论

{$gdata[pages]}//评论分页

{/block}

说明：

pagenum 每页显示条数

dateformat 时间格式

humandate 人性化时间显示 默认开启 (开启: 1 关闭: 0)

orderway 降序(-1),升序(1)

搜索模块

{block:global_search pagenum="20" maxcount="20000"}

{if:empty($gdata['list'])} //搜索无结果

抱歉，未找到和 {$keyword}相关的内容。

{else}

/.....以下判断搜索结果的类型，并且输出对应模型的结果.../

{if:isset($_GET['mid']) && $_GET['mid']==3} //搜索产品模块结果

{loop:$gdata[list] $v}

{$v[url]}

{$v[title]}

{$v[date]}

{$v[pic]}

{$v[subject]}

{/loop}

共{$gdata[total]}篇{$gdata[pages]

{elseif:isset($_GET['mid']) && $_GET['mid']==4}//搜索图集模块结果

{loop:$gdata[list] $v}

{$v[url]}

{$v[title]}

{$v[date]}

{$v[pic]}

{$v[subject]}

{/loop}

共{$gdata[total]} 篇{$gdata[pages]}

{else} //输出文章模型的搜索结果

{loop:$gdata[list] $v}

{$v[url]}

{$v[title]}

{$v[date]}

{$v[subject]}

{$v[date]}

{$v[intro]}

{/loop}

共 {$gdata[total]} 篇{$gdata[pages]}

{/if}

{/block}

附加：

//搜索提交

<form method="get" action="{$tw[webdir]}index.php">

//（2：文章；3：产品；4：图集）

<input type="hidden" name="mid" value="2" />

//提交搜索内容

<input type="text" name="keyword" value="{$keyword}" />

<input type="submit" value="" />

</form>

pagenum 每页显示条数

titlenum 标题长度

intronum 简介长度

dateformat 时间格式

maxcount 允许最大内容数(数据库搜索)

友情链接

{block:links}

{loop:$data $v}

{$v[url]}（网址）

{$v[name]}（网站名）

{/loop}

{/block}

标签列表页模块

推荐用于tag列表页面。

//taglist需按照不同模块显示。2文章模块3产品模块4图集模块

{block:global_taglist pagenum="20"}

{if:isset($_GET['mid']) && $_GET['mid']==2}

//输出当前模块下包含此tag的内容

{loop:$gdata[list] $v}

{$v[url]}

{$v[title]}

{$v[date]}

{$v[pic]}

{$v[subject]}

{$v[intro]}

{$v[author]}

//输出当前模块下包含此tag的tag

{if:isset($v['tag_arr'])}

{loop:$v[tag_arr] $v2}

<a href="{$v2[url]}">{$v2[name]}</a>{/loop}

{/if}

{/loop}

//总数量，以及翻页

共{$gdata[total]} 篇{$gdata[pages]

{elseif:isset($_GET['mid']) && $_GET['mid']==3}

...代码...

{else}

...代码...

{/if}

{/block}

单页模块

{block:global_page}

<div class="content">{$gdata[content]}</div>// 调用内容

{/block}

列表页模块

*推荐用于列表页*

{block:global_cate pagenum="10"}

<div class="">

{loop:$gdata[list] $v}

<dl class="">

<dt><a href="{$v[url]}" title="{$v[title]}" target="_blank"><img src="{$v[pic]}" /></a></dt>

<dd>

<a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[subject]}</a>

<div>{$v[intro]}</div>

//如果有tag，则显示tag

{if:isset($v['tag_arr'])}

<div class="">{loop:$v[tag_arr] $v2}<a href="{$v2[url]}">{$v2[name]}</a>{/loop}</div>

{/if}

<div class=""><span>作者：{$v[author]}</span><span>发表于：{$v[date]}</span></div>

</dd>

</dl>

{/loop}

//如果大于一页，则显示分页

<div class=""><span>共 <font color="red">{$gdata[total]}</font> 篇</span>{$gdata[pages]}</div>

</div>

{/block}

列表页模块 (不推荐频道分类使用此模块，影响性能)

pagenum 每页显示条数

titlenum 标题长度

intronum 简介长度

dateformat 时间格式

orderby 排序方式

orderway 降序(-1),升序(1)

模型页模块

{block:global_blog mid="2" pagenum="10"}

<div class="">

<div class="">

{loop:$gdata[list] $v}

<dl class="">

<a href="{$v[url]}" title="{$v[title]}" target="_blank"><img src="{$v[pic]}" /></a>

<dd>

<h3 class="cf"><a href="{$v[url]}" title="{$v[title]}" target="_blank">{$v[subject]}</a></h3>

<div>{$v[intro]}</div>

<div class="news_info"><span>作者：{$v[author]}</span><span>发表于：{$v[date]}</span></div>

</dd>

</dl>

{/loop}

<div class="pages">

<span>共 <font color="red">{$gdata[total]}</font> 篇</span>{$gdata[pages]}</div>

</div>

</div>

{/block}

mid 模型ID

pagenum 每页显示条数

titlenum 标题长度

intronum 简介长度

dateformat 时间格式

orderby 排序方式

orderway 降序(-1),升序(1)

<header><div class="headtop"><div class="float-left logo"><a href="https://www.66zo.com"><img src="{$tw[tpl]}images/logo.png" alt="遛走影视" title="遛走影视" /></a></div><!-- //logo--><div class="float-right search"><div class="search_txt text-right hidden-l hidden-s"><a href="https://www.66zo.com/type.html" title="影片筛选" target=_blank>影片筛选</a><a href="https://www.66zo.com/tag-top/" target=_blank>关键词库</a><a href="https://www.66zo.com" title="加入收藏">收藏本站</a></div><div class="float-left header_search"><form id="search_form" method="get" action="/index.php"><input type="hidden" name="u" value="search-index" /><input class="input input_txt" type="text" name="keyword" size="50" value="{$keyword}" autocomplete="off" placeholder="色即是空" /><button type="submit" class="button"><i class="icon icon-search"></i></button></form></div></div><!--//search--></div><!--//headtop --><div class="layout bg-main"><div class="navmenu"><ul><li {if:empty($tw_var['topcid'])} class="cur_index"{/if}><a href="https://www.66zo.com/">首页</a></li>{block:category cid="1" mid="2" type="child"}{loop:$data $v}<li {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){  echo 'class="cur_index"' ;  }{/php}><a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}">{$v[name]}</a></li>{/loop}{/block}</ul></div><!--//navmenu --></div><!--//layout --><div class="layout border-bottom border-back"><div class="navmenu navmenu_sub">{block:category cid="2" mid="2" type="child"}<ul>{loop:$data $v}<li {php}$cur_id = isset($tw_var["cid"])? $tw_var["cid"]:''; if($cur_id == $v['cid']){  echo 'class="cur_index"' ;  }{/php}><a href="{$v[url]}" title="{$v[name]}" target="{$v[target]}">{$v[name]}</a></li>{/loop}{/block}{block:category tyname="1"}<li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_色情__.html" target="_blank">色情</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_情色__.html" target="_blank">情色</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_理论__.html" target="_blank">理论</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_限制__.html" target="_blank">限制</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_三级__.html" target="_blank">三级</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_禁片__.html" target="_blank">禁片</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_R级__.html" target="_blank">R级</a></li><li class="hidden-l hidden-s"><a href="/{$data['tyname']}1_黄色__.html" target="_blank">黄色</a></li></ul>{/block}</div><!--//navmenu --></div><!--//layout --></header>