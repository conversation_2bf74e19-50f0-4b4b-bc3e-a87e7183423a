{"name": "vodcms", "version": "1.0.0", "description": "This project requires **PHP 8.0** or later and is tested with **MySQL 8**. Older PHP 7 versions are no longer supported.", "main": "tailwind.config.js", "directories": {"test": "tests"}, "scripts": {"build:admin": "tailwindcss -i ./static/admin/css/input.css -o ./static/admin/css/output.css --watch", "build:frontend": "./tailwindcss-macos-arm64 --config tailwind-frontend.config.js -i ./static/css/input.css -o ./static/css/output.css --watch", "build:admin-prod": "tailwindcss -i ./static/admin/css/input.css -o ./static/admin/css/output.css --minify", "build:frontend-prod": "./tailwindcss-macos-arm64 --config tailwind-frontend.config.js -i ./static/css/input.css -o ./static/css/output.css --minify", "build:all": "npm run build:admin-prod && npm run build:frontend-prod", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC"}