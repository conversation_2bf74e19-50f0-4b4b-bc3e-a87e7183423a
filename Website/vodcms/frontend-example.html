<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端模板 - Tailwind CSS 示例</title>
    <!-- 引用前端 Tailwind CSS 文件 -->
    <link rel="stylesheet" href="/static/css/output.css">
</head>
<body class="bg-gray-50">
    <!-- 导航栏 -->
    <nav class="bg-white shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="text-xl font-bold text-gray-800">网站标题</div>
                <ul class="nav">
                    <li><a href="#" class="nav-link active">首页</a></li>
                    <li><a href="#" class="nav-link">关于</a></li>
                    <li><a href="#" class="nav-link">服务</a></li>
                    <li><a href="#" class="nav-link">联系</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">欢迎使用 Tailwind CSS</h1>
            <p class="text-lg text-gray-600">这是一个使用 Tailwind CSS 的前端模板示例</p>
        </div>

        <!-- 卡片网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- 卡片 1 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold">功能特色 1</h3>
                </div>
                <div class="card-body">
                    <p class="text-gray-600 mb-4">这里是功能描述，展示了如何使用 Tailwind CSS 的工具类来快速构建美观的界面。</p>
                    <button class="btn btn-primary">了解更多</button>
                </div>
            </div>

            <!-- 卡片 2 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold">功能特色 2</h3>
                </div>
                <div class="card-body">
                    <p class="text-gray-600 mb-4">响应式设计，自动适配不同屏幕尺寸，提供最佳的用户体验。</p>
                    <button class="btn btn-success">查看详情</button>
                </div>
            </div>

            <!-- 卡片 3 -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold">功能特色 3</h3>
                </div>
                <div class="card-body">
                    <p class="text-gray-600 mb-4">模块化组件，易于维护和扩展，支持自定义主题配置。</p>
                    <button class="btn btn-secondary">开始使用</button>
                </div>
            </div>
        </div>

        <!-- 表单示例 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold mb-6">联系表单</h2>
            <form class="space-y-4">
                <div class="form-group">
                    <label class="form-label">姓名</label>
                    <input type="text" class="form-input" placeholder="请输入您的姓名">
                </div>

                <div class="form-group">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-input" placeholder="请输入您的邮箱">
                </div>

                <div class="form-group">
                    <label class="form-label">消息</label>
                    <textarea class="form-textarea" rows="4" placeholder="请输入您的消息"></textarea>
                </div>

                <div class="flex gap-4">
                    <button type="submit" class="btn btn-primary">发送消息</button>
                    <button type="reset" class="btn btn-secondary">重置</button>
                </div>
            </form>
        </div>

        <!-- 警告框示例 -->
        <div class="space-y-4 mb-8">
            <div class="alert alert-info">
                <strong>信息提示：</strong> 这是一个信息提示框，用于显示一般性信息。
            </div>

            <div class="alert alert-success">
                <strong>成功：</strong> 操作已成功完成！
            </div>

            <div class="alert alert-warning">
                <strong>警告：</strong> 请注意这个重要提示。
            </div>

            <div class="alert alert-danger">
                <strong>错误：</strong> 发生了一个错误，请检查您的输入。
            </div>
        </div>

        <!-- 徽章示例 -->
        <div class="mb-8">
            <h3 class="text-lg font-semibold mb-4">状态徽章</h3>
            <div class="flex gap-2 flex-wrap">
                <span class="badge badge-primary">主要</span>
                <span class="badge badge-success">成功</span>
                <span class="badge badge-warning">警告</span>
                <span class="badge badge-danger">危险</span>
            </div>
        </div>

        <!-- 工具类示例 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold mb-4">工具类示例</h3>
            <div class="space-y-4">
                <p class="text-shadow">这段文字有阴影效果</p>
                <div class="gradient-bg text-white p-4 rounded-lg">渐变背景效果</div>
                <div class="glass-effect p-4 rounded-lg">毛玻璃效果</div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 网站名称. 保留所有权利.</p>
        </div>
    </footer>
</body>
</html>