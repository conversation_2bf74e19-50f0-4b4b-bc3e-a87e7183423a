@tailwind base;
@tailwind components;
@tailwind utilities;

/* 前端模板自定义样式 */
@layer base {
  /* 基础样式重置 */
  body {
    @apply font-sans text-gray-900;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }

  h1 {
    @apply text-3xl mb-4;
  }

  h2 {
    @apply text-2xl mb-3;
  }

  h3 {
    @apply text-xl mb-2;
  }

  a {
    @apply text-blue-600 hover:text-blue-800 transition-colors;
  }

  img {
    @apply max-w-full h-auto;
  }
}

@layer components {
  /* 通用组件样式 */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-outline {
    @apply border-2 border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* 导航组件 */
  .nav {
    @apply flex space-x-4;
  }

  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
  }

  .nav-link-active {
    @apply bg-blue-600 text-white;
  }

  .nav-link-inactive {
    @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100;
  }

  /* 表单组件 */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }

  .form-textarea {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical;
  }

  .form-select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white;
  }

  .form-error {
    @apply text-red-600 text-sm mt-1;
  }

  /* 列表组件 */
  .list-group {
    @apply divide-y divide-gray-200;
  }

  .list-group-item {
    @apply py-3 px-4 hover:bg-gray-50 transition-colors;
  }

  /* 分页组件 */
  .pagination {
    @apply flex items-center justify-center space-x-2;
  }

  .pagination-item {
    @apply px-3 py-2 text-sm leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700;
  }

  .pagination-item.active {
    @apply bg-blue-600 text-white border-blue-600;
  }

  /* 面包屑导航 */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-gray-600;
  }

  .breadcrumb-item:not(:last-child)::after {
    content: '/';
    @apply ml-2 text-gray-400;
  }

  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  /* 警告框组件 */
  .alert {
    @apply p-4 rounded-lg border;
  }

  .alert-info {
    @apply bg-blue-50 border-blue-200 text-blue-800;
  }

  .alert-success {
    @apply bg-green-50 border-green-200 text-green-800;
  }

  .alert-warning {
    @apply bg-yellow-50 border-yellow-200 text-yellow-800;
  }

  .alert-error {
    @apply bg-red-50 border-red-200 text-red-800;
  }

  /* 侧边栏组件 */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-80 bg-gray-900 text-white z-50 transform transition-transform duration-300 ease-in-out;
  }

  .sidebar-hidden {
    @apply -translate-x-full;
  }

  .sidebar-visible {
    @apply translate-x-0;
  }

  .sidebar-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
  }
}

@layer utilities {
  /* 自定义工具类 */
  .text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }

  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
  }

  /* 响应式隐藏类 */
  .hidden-mobile {
    @apply block;
  }

  @screen sm {
    .hidden-mobile {
      @apply hidden;
    }
  }

  .hidden-desktop {
    @apply hidden;
  }

  @screen lg {
    .hidden-desktop {
      @apply block;
    }
  }

  /* 动画类 */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-in-out;
  }

  .animate-slide-out-left {
    animation: slideOutLeft 0.3s ease-in-out;
  }

  /* 确保包含所有transform类 */
  .transform { transform: var(--tw-transform); }
  .translate-x-0 { --tw-translate-x: 0px; transform: var(--tw-transform); }
  .-translate-x-full { --tw-translate-x: -100%; transform: var(--tw-transform); }
  .translate-x-full { --tw-translate-x: 100%; transform: var(--tw-transform); }
  .transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
  .duration-300 { transition-duration: 300ms; }
  .ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}